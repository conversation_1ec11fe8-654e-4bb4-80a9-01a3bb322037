# Demand Forecast Excel Upload Format

## 📊 Excel Template Structure

### **Required Columns:**

| Column Name | Data Type | Required | Description | Example |
|-------------|-----------|----------|-------------|---------|
| `Date (YYYY-MM-DD)` | Date | ✅ | Forecast date | 2025-08-01 |
| `Department ID` | Integer | ✅ | Department ID from system | 1 |
| `Department Name` | String | ❌ | Department name (for reference) | Sales |
| `Designation ID` | Integer | ✅ | Designation ID from system | 5 |
| `Designation Name` | String | ❌ | Designation name (for reference) | Sales Executive |
| `Required Count` | Integer | ✅ | Number of employees needed | 5 |
| `Priority (1-10)` | Integer | ❌ | Priority level (default: 5) | 7 |
| `Confidence (0-100)` | Decimal | ❌ | Confidence percentage (default: 100) | 85.5 |
| `Forecast Type` | String | ❌ | Type of forecast (default: manual_override) | historical_average |
| `Valid From (YYYY-MM-DD)` | Date | ❌ | Validity start date | 2025-08-01 |
| `Valid To (YYYY-MM-DD)` | Date | ❌ | Validity end date | 2025-08-31 |
| `Notes` | String | ❌ | Additional notes | Peak season forecast |

### **Forecast Types:**
- `historical_average` - Based on historical data
- `trend_analysis` - Based on trend analysis
- `seasonal_pattern` - Based on seasonal patterns
- `machine_learning` - ML-generated forecast
- `manual_override` - Manual input (default)

### **Sample Data:**

```
Date (YYYY-MM-DD) | Department ID | Department Name | Designation ID | Designation Name | Required Count | Priority (1-10) | Confidence (0-100) | Forecast Type | Valid From (YYYY-MM-DD) | Valid To (YYYY-MM-DD) | Notes
2025-08-01 | 1 | Sales | 5 | Sales Executive | 5 | 7 | 85 | historical_average | 2025-08-01 | 2025-08-31 | Peak season forecast
2025-08-02 | 1 | Sales | 5 | Sales Executive | 3 | 5 | 90 | trend_analysis | 2025-08-01 | 2025-08-31 | Weekend forecast
2025-08-01 | 2 | Marketing | 8 | Marketing Manager | 2 | 8 | 95 | manual_override | 2025-08-01 | 2025-08-31 | Campaign launch
```

## 🔧 API Endpoints

### **1. Create Demand Forecasts (Unified Endpoint)**
```bash
POST /api/v1/demand-forecasts
```

**✅ SUPPORTS 3 MODES:**

#### **Mode 1: Single Manual Creation**
```bash
POST /api/v1/demand-forecasts
Content-Type: application/json

{
  "departmentId": 1,
  "designationId": 5,
  "date": "2025-08-01",
  "requiredCount": 5,
  "priority": 7,
  "confidence": 85,
  "forecastType": "historical_average",
  "notes": "Peak season forecast"
}
```

#### **Mode 2: Bulk Manual Creation**
```bash
POST /api/v1/demand-forecasts
Content-Type: application/json

{
  "forecasts": [
    {
      "departmentId": 1,
      "designationId": 5,
      "date": "2025-08-01",
      "requiredCount": 5,
      "forecastType": "historical_average"
    },
    {
      "departmentId": 1,
      "designationId": 5,
      "date": "2025-08-02",
      "requiredCount": 3,
      "forecastType": "trend_analysis"
    }
  ]
}
```

#### **Mode 3: Excel Upload**
```bash
POST /api/v1/demand-forecasts
Content-Type: multipart/form-data

Form Data:
- file: [Excel file with forecast data]
```

**Response (All Modes):**
```json
{
  "success": true,
  "message": "Excel processing completed. 5 successful, 2 failed. File processed and removed.",
  "data": {
    "total": 7,
    "successful": 5,
    "failed": 2,
    "errors": [
      "Row 3: Department ID 999 not found",
      "Forecast 2: Required Count must be a positive integer"
    ],
    "created": [
      {
        "id": 123,
        "date": "2025-08-01",
        "departmentId": 1,
        "designationId": 5,
        "requiredCount": 5
      }
    ],
    "fileInfo": {
      "originalName": "forecasts.xlsx",
      "processedAt": "2025-07-23T10:30:00.000Z",
      "totalRows": 7,
      "fileSize": 15360
    }
  }
}
```

### **2. Get Excel Template Data**
```bash
GET /api/v1/demand-forecasts/excel/template
```

### **3. Download Excel Template File**
```bash
GET /api/v1/demand-forecasts/excel/download-template
```

## ✅ Validation Rules

### **Required Fields:**
- Date (YYYY-MM-DD)
- Department ID
- Designation ID  
- Required Count

### **Data Validation:**
- **Date Format:** Must be YYYY-MM-DD
- **Department ID:** Must exist in system
- **Designation ID:** Must exist in system
- **Required Count:** Positive integer
- **Priority:** 1-10 range
- **Confidence:** 0-100 range
- **Forecast Type:** Must be valid type

### **Business Rules:**
- No duplicate forecasts for same date/department/designation
- Department and Designation must belong to same business unit
- Valid From date cannot be after Valid To date

## 🚀 Usage Example

```bash
# 1. Download template
curl -X GET "http://localhost:3000/api/v1/demand-forecasts/excel/download-template" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -o demand_forecast_template.xlsx

# 2. Fill the template with your data

# 3. Upload via unified create endpoint (RECOMMENDED)
curl -X POST "http://localhost:3000/api/v1/demand-forecasts" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@filled_template.xlsx"

# Note: Dedicated Excel upload endpoint has been removed for simplicity
# Use the unified create endpoint above for all forecast creation needs
```

## ⚙️ Integration with Existing Multer Configuration

**✅ USES YOUR EXISTING MULTER SETUP:**

- **Middleware:** `uploadSingleFile('file')` from your upload middleware
- **File Storage:** Uses your configured upload directory
- **File Validation:** Leverages your existing file type and size validations
- **Error Handling:** Integrates with your multer error handling
- **Automatic Cleanup:** Files are deleted after processing (success or error)

**Benefits:**
- ✅ Consistent with your existing file upload patterns
- ✅ No duplicate multer configurations
- ✅ Uses your established upload directory structure
- ✅ Maintains your file validation rules
- ✅ Automatic file cleanup after processing

## 📋 Error Handling

**Common Errors:**
- Invalid file format (only .xlsx, .xls allowed)
- Missing required columns
- Invalid data types
- Department/Designation not found
- Duplicate forecasts
- File size too large (>10MB)

**Error Response Format:**
```json
{
  "success": false,
  "message": "Excel processing completed. 3 successful, 2 failed.",
  "data": {
    "errors": [
      "Row 2: Date must be in YYYY-MM-DD format",
      "Row 4: Department ID 999 not found"
    ]
  }
}
```
