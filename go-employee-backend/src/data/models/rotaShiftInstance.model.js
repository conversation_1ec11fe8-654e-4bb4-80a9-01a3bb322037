'use strict';

module.exports = (sequelize, DataTypes) => {
  const RotaShiftInstance = sequelize.define('RotaShiftInstance', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    rotaShiftId: {
      field: 'rota_shift_id',
      type: DataTypes.INTEGER,
      allowNull: true, // Made optional - will be null for template-based instances
      references: {
        model: 'rota_shifts',
        key: 'id'
      },
    },
    scheduleId: {
      field: 'schedule_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'rota_schedules',
        key: 'id'
      },
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },

    // Source tracking for hybrid approach
    sourceType: {
      field: 'source_type',
      type: DataTypes.ENUM('rotaShift', 'template', 'manual'),
      defaultValue: 'rotaShift',
      // comment: 'Source of this instance: individual shift, template, or manual creation'
    },
    sourceId: {
      field: 'source_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      // comment: 'ID of source (RotaShift ID or Template ID)'
    },


    // Performance optimization fields
    totalRequired: {
      field: 'total_required',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      // comment: 'Cached total required count for performance'
    },
    totalAssigned: {
      field: 'total_assigned',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      // comment: 'Cached total assigned count for performance'
    },

    // Legacy field (keep for backward compatibility)
    actualRequiredCount: {
      field: 'actual_required_count',
      type: DataTypes.INTEGER,
      allowNull: true,
      // comment: 'Legacy field - use totalRequired instead'
    },
    overrideReason: {
      field: 'override_reason',
      type: DataTypes.ENUM(
        'template_default',     // Using template base count
        'forecast_prediction',  // Overridden by demand forecast
        'manual_override',      // Manager manual adjustment
        'emergency_requirement', // Emergency staffing need
        'holiday_adjustment',   // Holiday-specific adjustment
        'business_rule_override' // Business rule adjustment
      ),
      defaultValue: 'template_default',
    },
    forecastData: {
      field: 'forecast_data',
      type: DataTypes.JSON,
      allowNull: true,
    },
    // Status and workflow management
    status: {
      type: DataTypes.ENUM(
        'draft',        // Instance created but not finalized
        'open',         // Ready for employee assignment
        'partial',      // Partially filled
        'full',         // All positions filled
        'published',    // Published to employees
        'locked',       // No more changes allowed
        'completed',    // Shift completed
        'cancelled'     // Shift cancelled
      ),
      defaultValue: 'draft',
      // comment: 'Instance status for workflow management'
    },

    // Conflict and validation tracking
    conflictFlags: {
      field: 'conflict_flags',
      type: DataTypes.JSON,
      allowNull: true,
      // defaultValue: {},
      // comment: 'Pre-computed conflict information for performance'
    },

    // Assignment metadata for bulk operations
    assignmentMetadata: {
      field: 'assignment_metadata',
      type: DataTypes.JSON,
      allowNull: true,
      // defaultValue: {},
      // comment: 'Bulk assignment tracking and metadata'
    },

    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    createdById: {
      field: 'created_by_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updatedById: {
      field: 'updated_by_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'rota_shift_instances',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['rota_shift_id', 'schedule_id', 'date'],
        unique: true,
        name: 'unique_shift_schedule_date'
      },
      {
        fields: ['schedule_id'],
        name: 'idx_schedule_id'
      },
      {
        fields: ['date'],
        name: 'idx_date'
      },
      {
        fields: ['rota_shift_id'],
        name: 'idx_rota_shift_id'
      },
      {
        fields: ['schedule_id', 'date'],
        name: 'idx_schedule_date'
      },
      // Performance indexes for scale
      {
        fields: ['source_type', 'source_id'],
        name: 'idx_source_tracking'
      },
      {
        fields: ['status'],
        name: 'idx_status'
      },
      {
        fields: ['date', 'status'],
        name: 'idx_date_status'
      },
      {
        fields: ['schedule_id', 'status'],
        name: 'idx_schedule_status'
      }
    ]
  });

  RotaShiftInstance.associate = (models) => {
    // RotaShiftInstance belongs to RotaShift (template) - optional relationship
    RotaShiftInstance.belongsTo(models.RotaShift, {
      foreignKey: {
        name: 'rotaShiftId',
        field: 'rota_shift_id'
      },
      as: 'rotaShift',
      constraints: false // Allow null for template-based instances
    });

    // RotaShiftInstance belongs to RotaSchedule
    RotaShiftInstance.belongsTo(models.RotaSchedule, {
      foreignKey: {
        name: 'scheduleId',
        field: 'schedule_id'
      },
      as: 'schedule'
    });

    // RotaShiftInstance belongs to User (creator)
    RotaShiftInstance.belongsTo(models.User, {
      foreignKey: {
        name: 'createdById',
        field: 'created_by_id'
      },
      as: 'createdBy'
    });

    // RotaShiftInstance belongs to User (updater)
    RotaShiftInstance.belongsTo(models.User, {
      foreignKey: {
        name: 'updatedById',
        field: 'updated_by_id'
      },
      as: 'updatedBy'
    });

    // RotaShiftInstance has many ShiftAssignments
    RotaShiftInstance.hasMany(models.ShiftAssignment, {
      foreignKey: {
        name: 'shiftInstanceId',
        field: 'shift_instance_id'
      },
      as: 'assignments'
    });

    // RotaShiftInstance has many DesignationRequirements (junction table)
    RotaShiftInstance.hasMany(models.RotaShiftInstanceDesignationRequirement, {
      foreignKey: {
        name: 'shiftInstanceId',
        field: 'shift_instance_id'
      },
      as: 'designationRequirementsTable'
    });
  };

  return RotaShiftInstance;
};
