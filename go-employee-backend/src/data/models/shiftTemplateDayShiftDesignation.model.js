'use strict';

module.exports = (sequelize, DataTypes) => {
  const ShiftTemplateDayShiftDesignation = sequelize.define('ShiftTemplateDayShiftDesignation', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    dayShiftId: {
      field: 'day_shift_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'shift_template_day_shifts',
        key: 'id'
      }
    },
    designationId: {
      field: 'designation_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'designations',
        key: 'id'
      }
    },
    requiredCount: {
      field: 'required_count',
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        min: 0,
        max: 100
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isActive: {
      field: 'is_active',
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdById: {
      field: 'created_by_id',
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updatedById: {
      field: 'updated_by_id',
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'shift_template_day_shift_designations',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['day_shift_id', 'designation_id']
      },
      {
        fields: ['designation_id']
      },
      {
        fields: ['day_shift_id', 'is_active']
      },
      {
        fields: ['required_count']
      }
    ]
  });

  ShiftTemplateDayShiftDesignation.associate = (models) => {
    // Belongs to ShiftTemplateDayShift
    ShiftTemplateDayShiftDesignation.belongsTo(models.ShiftTemplateDayShift, {
      foreignKey: {
        name: 'dayShiftId',
        field: 'day_shift_id'
      },
      as: 'dayShift'
    });

    // Belongs to Designation
    ShiftTemplateDayShiftDesignation.belongsTo(models.Designation, {
      foreignKey: {
        name: 'designationId',
        field: 'designation_id'
      },
      as: 'designation'
    });

    // Belongs to User (created by)
    ShiftTemplateDayShiftDesignation.belongsTo(models.User, {
      foreignKey: {
        name: 'createdById',
        field: 'created_by_id'
      },
      as: 'createdBy'
    });

    // Belongs to User (updated by)
    ShiftTemplateDayShiftDesignation.belongsTo(models.User, {
      foreignKey: {
        name: 'updatedById',
        field: 'updated_by_id'
      },
      as: 'updatedBy'
    });
  };

  return ShiftTemplateDayShiftDesignation;
};
