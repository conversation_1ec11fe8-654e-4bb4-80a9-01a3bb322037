'use strict';

module.exports = (sequelize, DataTypes) => {
  const ShiftTemplateDayShift = sequelize.define('ShiftTemplateDayShift', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    dayConfigId: {
      field: 'day_config_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'shift_template_day_configs',
        key: 'id'
      }
    },
    rotaShiftId: {
      field: 'rota_shift_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'rota_shifts',
        key: 'id'
      }
    },
    priority: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      validate: {
        min: 1,
        max: 10
      }
    },
    isFlexible: {
      field: 'is_flexible',
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    useDesignationOverride: {
      field: 'use_designation_override',
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether to use custom designation requirements or RotaShift defaults'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isActive: {
      field: 'is_active',
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdById: {
      field: 'created_by_id',
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updatedById: {
      field: 'updated_by_id',
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'shift_template_day_shifts',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['day_config_id', 'priority']
      },
      {
        fields: ['rota_shift_id']
      },
      {
        fields: ['day_config_id', 'is_active']
      },
      {
        fields: ['use_designation_override']
      }
    ]
  });

  ShiftTemplateDayShift.associate = (models) => {
    // Belongs to ShiftTemplateDayConfig
    ShiftTemplateDayShift.belongsTo(models.ShiftTemplateDayConfig, {
      foreignKey: {
        name: 'dayConfigId',
        field: 'day_config_id'
      },
      as: 'dayConfig'
    });

    // Belongs to RotaShift
    ShiftTemplateDayShift.belongsTo(models.RotaShift, {
      foreignKey: {
        name: 'rotaShiftId',
        field: 'rota_shift_id'
      },
      as: 'rotaShift'
    });

    // Has many designation requirements
    ShiftTemplateDayShift.hasMany(models.ShiftTemplateDayShiftDesignation, {
      foreignKey: {
        name: 'dayShiftId',
        field: 'day_shift_id'
      },
      as: 'designationRequirements'
    });

    // Belongs to User (created by)
    ShiftTemplateDayShift.belongsTo(models.User, {
      foreignKey: {
        name: 'createdById',
        field: 'created_by_id'
      },
      as: 'createdBy'
    });

    // Belongs to User (updated by)
    ShiftTemplateDayShift.belongsTo(models.User, {
      foreignKey: {
        name: 'updatedById',
        field: 'updated_by_id'
      },
      as: 'updatedBy'
    });
  };

  return ShiftTemplateDayShift;
};
