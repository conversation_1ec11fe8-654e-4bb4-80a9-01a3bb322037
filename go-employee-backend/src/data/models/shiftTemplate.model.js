'use strict';

module.exports = (sequelize, DataTypes) => {
  const ShiftTemplate = sequelize.define('ShiftTemplate', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    companyId: {
      field: 'company_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id'
      }
    },
    businessUnitId: {
      field: 'business_unit_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'business_units',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [3, 100]
      }
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [3, 20]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    category: {
      type: DataTypes.ENUM('regular', 'overtime', 'holiday', 'emergency', 'training'),
      defaultValue: 'regular'
    },
    
    // REMOVED: dayConfigurations JSONB field
    // Now using normalized tables: ShiftTemplateDayConfig, ShiftTemplateDayShift, ShiftTemplateDayShiftDesignation
    
    // Template metadata
    templateType: {
      field: 'template_type',
      type: DataTypes.ENUM('weekly', 'bi_weekly', 'monthly', 'custom'),
      defaultValue: 'weekly'
    },
    
    // Usage tracking
    usageCount: {
      field: 'usage_count',
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    lastUsedAt: {
      field: 'last_used_at',
      type: DataTypes.DATE,
      allowNull: true
    },
    
    // Template settings
    isActive: {
      field: 'is_active',
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    isDefault: {
      field: 'is_default',
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    
    // Auto-assignment settings
    autoAssignmentEnabled: {
      field: 'auto_assignment_enabled',
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    assignmentRules: {
      field: 'assignment_rules',
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Rules for automatic employee assignment'
      /*
      Structure:
      {
        "preferenceOrder": ["seniority", "availability", "skills"],
        "avoidConsecutiveNights": true,
        "maxConsecutiveDays": 6,
        "restBetweenShifts": 12,
        "skillMatching": "strict"
      }
      */
    },    
    // Audit fields
    createdById: {
      field: 'created_by_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updatedById: {
      field: 'updated_by_id',
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'shift_templates',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['company_id', 'business_unit_id']
      },
      {
        fields: ['company_id', 'is_active']
      },
      {
        fields: ['company_id', 'category']
      },
      {
        fields: ['code'],
        unique: true
      }
    ]
  });

  ShiftTemplate.associate = (models) => {
    // ShiftTemplate belongs to Company
    ShiftTemplate.belongsTo(models.Company, {
      foreignKey: {
        name: 'companyId',
        field: 'company_id'
      },
      as: 'company'
    });

    // ShiftTemplate belongs to BusinessUnit
    ShiftTemplate.belongsTo(models.BusinessUnit, {
      foreignKey: {
        name: 'businessUnitId',
        field: 'business_unit_id'
      },
      as: 'businessUnit'
    });

    // ShiftTemplate belongs to User (creator)
    ShiftTemplate.belongsTo(models.User, {
      foreignKey: {
        name: 'createdById',
        field: 'created_by_id'
      },
      as: 'createdBy'
    });

    // ShiftTemplate belongs to User (updater)
    ShiftTemplate.belongsTo(models.User, {
      foreignKey: {
        name: 'updatedById',
        field: 'updated_by_id'
      },
      as: 'updatedBy'
    });

    // ShiftTemplate belongs to User (approver)
    ShiftTemplate.belongsTo(models.User, {
      foreignKey: {
        name: 'approvedBy',
        field: 'approved_by'
      },
      as: 'approver'
    });

    // NEW ASSOCIATIONS: Normalized day configurations
    // ShiftTemplate has many day configurations
    ShiftTemplate.hasMany(models.ShiftTemplateDayConfig, {
      foreignKey: {
        name: 'shiftTemplateId',
        field: 'shift_template_id'
      },
      as: 'dayConfigs'
    });
  };

  return ShiftTemplate;
};
