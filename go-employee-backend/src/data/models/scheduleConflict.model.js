const { DataTypes, Op } = require('sequelize');

/**
 * ScheduleConflict Model - Tracks all scheduling conflicts for resolution
 * 
 * This model stores detailed information about scheduling conflicts that occur
 * during auto-schedule generation, allowing for tracking, analysis, and resolution.
 */

module.exports = (sequelize) => {
  const ScheduleConflict = sequelize.define('ScheduleConflict', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },

    // Conflict Identification
    conflictId: {
      field: 'conflict_id',
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: 'Unique conflict identifier for tracking'
    },

    // Session Information
    sessionId: {
      field: 'session_id',
      type: DataTypes.UUID,
      allowNull: false,
      comment: 'Auto-schedule session that generated this conflict'
    },

    // Conflict Classification
    conflictType: {
      field: 'conflict_type',
      type: DataTypes.ENUM(
        'employee_pool_exhaustion',
        'constraint_violation',
        'already_assigned',
        'availability_conflict',
        'designation_mismatch',
        'time_overlap',
        'workload_limit',
        'partial_assignment'
      ),
      allowNull: false,
      comment: 'Type of scheduling conflict'
    },

    severity: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      allowNull: false,
      defaultValue: 'medium',
      comment: 'Severity level of the conflict'
    },

    status: {
      type: DataTypes.ENUM('pending', 'in_progress', 'resolved', 'ignored', 'escalated'),
      allowNull: false,
      defaultValue: 'pending',
      comment: 'Current resolution status'
    },

    // Context Information
    scheduleId: {
      field: 'schedule_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'rota_schedules',
        key: 'id'
      },
      comment: 'Related schedule ID'
    },

    shiftInstanceId: {
      field: 'shift_instance_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'rota_shift_instances',
        key: 'id'
      },
      comment: 'Related shift instance ID'
    },

    employeeId: {
      field: 'employee_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      },
      comment: 'Employee involved in conflict (if applicable)'
    },

    designationId: {
      field: 'designation_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'designations',
        key: 'id'
      },
      comment: 'Designation involved in conflict'
    },

    conflictDate: {
      field: 'conflict_date',
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Date when conflict occurred'
    },

    // Conflict Details
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: 'Brief conflict title'
    },

    description: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Detailed conflict description'
    },

    conflictData: {
      field: 'conflict_data',
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Additional conflict-specific data (JSON)'
    },

    // Requirements vs Availability
    requiredCount: {
      field: 'required_count',
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Required employee count'
    },

    availableCount: {
      field: 'available_count',
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Available employee count'
    },

    assignedCount: {
      field: 'assigned_count',
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Successfully assigned count'
    },

    shortfall: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Assignment shortfall (required - assigned)'
    },

    // Resolution Information
    resolutionStrategy: {
      field: 'resolution_strategy',
      type: DataTypes.ENUM(
        'increase_pool',
        'relax_constraints',
        'cross_training',
        'shift_adjustment',
        'manual_assignment',
        'accept_partial',
        'other'
      ),
      allowNull: true,
      comment: 'Chosen resolution strategy'
    },

    resolutionNotes: {
      field: 'resolution_notes',
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Notes about resolution process'
    },

    resolvedAt: {
      field: 'resolved_at',
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When conflict was resolved'
    },

    resolvedBy: {
      field: 'resolved_by',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'User who resolved the conflict'
    },

    // Impact Assessment
    impactLevel: {
      field: 'impact_level',
      type: DataTypes.ENUM('minimal', 'moderate', 'significant', 'severe'),
      allowNull: false,
      defaultValue: 'moderate',
      comment: 'Business impact level'
    },

    affectedEmployees: {
      field: 'affected_employees',
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      comment: 'Number of employees affected'
    },

    // Multi-tenant Support
    companyId: {
      field: 'company_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id'
      }
    },

    businessUnitId: {
      field: 'business_unit_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'business_units',
        key: 'id'
      }
    },

    // Audit Fields
    createdAt: {
      field: 'created_at',
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },

    updatedAt: {
      field: 'updated_at',
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }

  }, {
    tableName: 'schedule_conflicts',
    timestamps: true,
    underscored: true,
    
    indexes: [
      // Primary search indexes
      {
        name: 'idx_schedule_conflicts_session',
        fields: ['session_id']
      },
      {
        name: 'idx_schedule_conflicts_type_status',
        fields: ['conflict_type', 'status']
      },
      {
        name: 'idx_schedule_conflicts_date',
        fields: ['conflict_date']
      },
      {
        name: 'idx_schedule_conflicts_employee',
        fields: ['employee_id', 'conflict_date']
      },
      {
        name: 'idx_schedule_conflicts_designation',
        fields: ['designation_id', 'conflict_date']
      },
      {
        name: 'idx_schedule_conflicts_severity',
        fields: ['severity', 'status']
      },
      
      // Multi-tenant indexes
      {
        name: 'idx_schedule_conflicts_tenant',
        fields: ['company_id', 'business_unit_id']
      },
      
      // Resolution tracking
      {
        name: 'idx_schedule_conflicts_resolution',
        fields: ['status', 'resolved_at']
      }
    ],
    
    comment: 'Tracks scheduling conflicts for analysis and resolution'
  });

  // Model associations
  ScheduleConflict.associate = function(models) {
    // Schedule relationship
    ScheduleConflict.belongsTo(models.RotaSchedule, {
      foreignKey: 'scheduleId',
      as: 'schedule'
    });

    // Shift instance relationship
    ScheduleConflict.belongsTo(models.RotaShiftInstance, {
      foreignKey: 'shiftInstanceId',
      as: 'shiftInstance'
    });

    // Employee relationship
    ScheduleConflict.belongsTo(models.Employee, {
      foreignKey: 'employeeId',
      as: 'employee'
    });

    // Designation relationship
    ScheduleConflict.belongsTo(models.Designation, {
      foreignKey: 'designationId',
      as: 'designation'
    });

    // Company relationship
    ScheduleConflict.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });

    // Business unit relationship
    ScheduleConflict.belongsTo(models.BusinessUnit, {
      foreignKey: 'businessUnitId',
      as: 'businessUnit'
    });

    // Resolver relationship
    ScheduleConflict.belongsTo(models.User, {
      foreignKey: 'resolvedBy',
      as: 'resolver'
    });

    // Conflict recommendations relationship
    ScheduleConflict.hasMany(models.ConflictRecommendation, {
      foreignKey: 'conflictId',
      as: 'recommendations'
    });
  };

  return ScheduleConflict;
};
