'use strict';

const { DataTypes } = require('sequelize');

/**
 * RotaShiftDesignationRequirement Model
 * Junction table for multiple designations per rota shift template
 * 
 * Purpose: Handle multiple designation requirements for each shift template
 * Example: Morning shift needs 2 Managers + 3 Executives + 1 Supervisor
 */
module.exports = (sequelize) => {
  const RotaShiftDesignationRequirement = sequelize.define('RotaShiftDesignationRequirement', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    rotaShiftId: {
      field: 'rota_shift_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'rota_shifts',
        key: 'id'
      },
    },
    designationId: {
      field: 'designation_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'designations',
        key: 'id'
      },
    },
    requiredCount: {
      field: 'required_count',
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        min: 1
      },
    }
  }, {
    tableName: 'rota_shift_designation_requirements',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['rota_shift_id']
      },
      {
        fields: ['designation_id']
      },
      {
        unique: true,
        fields: ['rota_shift_id', 'designation_id'],
        name: 'unique_shift_designation'
      }
    ],
  });

  // Define associations
  RotaShiftDesignationRequirement.associate = (models) => {
    // Belongs to RotaShift
    RotaShiftDesignationRequirement.belongsTo(models.RotaShift, {
      foreignKey: {
        name: 'rotaShiftId',
        field: 'rota_shift_id'
      },
      as: 'rotaShift'
    });

    // Belongs to Designation
    RotaShiftDesignationRequirement.belongsTo(models.Designation, {
      foreignKey: {
        name: 'designationId',
        field: 'designation_id'
      },
      as: 'designation'
    });
  };

  return RotaShiftDesignationRequirement;
};
