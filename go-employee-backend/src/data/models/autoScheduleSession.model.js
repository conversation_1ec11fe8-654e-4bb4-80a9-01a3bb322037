/**
 * AutoScheduleSession Model
 * 
 * Purpose: Track auto-scheduling sessions for monitoring and rollback
 * Benefits:
 * - Session-based tracking
 * - Rollback capability
 * - Performance monitoring
 * - Audit trail
 */

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AutoScheduleSession = sequelize.define('AutoScheduleSession', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    
    // Session Identification
    sessionId: {
      field: 'session_id',
      type: DataTypes.UUID,
      allowNull: false,
      unique: true,
      defaultValue: DataTypes.UUIDV4
    },
    
    scheduleId: {
      field: 'schedule_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'rota_schedules',
        key: 'id'
      }
    },
    
    // Session Configuration
    mode: {
      type: DataTypes.ENUM('template', 'department', 'hybrid', 'manual'),
      allowNull: false,
      defaultValue: 'template'
    },
    
    assignmentStrategy: {
      field: 'assignment_strategy',
      type: DataTypes.ENUM('smart', 'rotation', 'balanced', 'skill_based'),
      allowNull: false,
      defaultValue: 'smart'
    },
    
    constraints: {
      type: DataTypes.JSON,
      defaultValue: {},
      comment: 'Assignment constraints used in this session'
    },
    
    // Date Range
    startDate: {
      field: 'start_date',
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    
    endDate: {
      field: 'end_date',
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    
    // Session Status
    status: {
      type: DataTypes.ENUM(
        'initializing',   // Session starting
        'processing',     // Assignment in progress
        'completed',      // Successfully completed
        'failed',         // Failed with errors
        'cancelled',      // Cancelled by user
        'rolled_back'     // Changes were rolled back
      ),
      defaultValue: 'initializing'
    },
    
    // Progress Tracking
    totalInstances: {
      field: 'total_instances',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    processedInstances: {
      field: 'processed_instances',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    successfulAssignments: {
      field: 'successful_assignments',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    failedAssignments: {
      field: 'failed_assignments',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    // Performance Metrics
    startTime: {
      field: 'start_time',
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    
    endTime: {
      field: 'end_time',
      type: DataTypes.DATE,
      allowNull: true
    },
    
    processingTimeMs: {
      field: 'processing_time_ms',
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Total processing time in milliseconds'
    },
    
    averageAssignmentTimeMs: {
      field: 'average_assignment_time_ms',
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Average time per assignment in milliseconds'
    },
    
    // Results Summary
    results: {
      type: DataTypes.JSON,
      defaultValue: {},
      comment: 'Detailed results of the auto-scheduling session'
    },
    
    errors: {
      type: DataTypes.JSON,
      defaultValue: [],
      comment: 'List of errors encountered during processing'
    },
    
    warnings: {
      type: DataTypes.JSON,
      defaultValue: [],
      comment: 'List of warnings generated during processing'
    },
    
    // Coverage Statistics
    overallCoverage: {
      field: 'overall_coverage',
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0.00,
      validate: { min: 0, max: 100 },
      comment: 'Overall assignment coverage percentage'
    },
    
    designationCoverage: {
      field: 'designation_coverage',
      type: DataTypes.JSON,
      defaultValue: {},
      comment: 'Coverage breakdown by designation'
    },
    
    // Conflict Resolution
    conflictsDetected: {
      field: 'conflicts_detected',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    conflictsResolved: {
      field: 'conflicts_resolved',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    // Rollback Information
    canRollback: {
      field: 'can_rollback',
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Whether this session can be rolled back'
    },
    
    rollbackReason: {
      field: 'rollback_reason',
      type: DataTypes.TEXT,
      allowNull: true
    },
    
    rollbackAt: {
      field: 'rollback_at',
      type: DataTypes.DATE,
      allowNull: true
    },
    
    rollbackBy: {
      field: 'rollback_by',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    
    // User Information
    initiatedBy: {
      field: 'initiated_by',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    
    // Multi-tenant Support
    companyId: {
      field: 'company_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id'
      }
    },
    
    businessUnitId: {
      field: 'business_unit_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'business_units',
        key: 'id'
      }
    }
    
  }, {
    tableName: 'auto_schedule_sessions',
    timestamps: true,
    underscored: true,
    
    indexes: [
      // Primary indexes
      {
        name: 'idx_auto_schedule_session_id',
        fields: ['session_id'],
        unique: true
      },
      {
        name: 'idx_auto_schedule_session_schedule',
        fields: ['schedule_id', 'status']
      },
      {
        name: 'idx_auto_schedule_session_status',
        fields: ['status', 'start_time']
      },
      {
        name: 'idx_auto_schedule_session_user',
        fields: ['initiated_by', 'start_time']
      },
      
      // Performance indexes
      {
        name: 'idx_auto_schedule_session_date_range',
        fields: ['start_date', 'end_date']
      },
      {
        name: 'idx_auto_schedule_session_rollback',
        fields: ['can_rollback', 'status']
      },
      
      // Multi-tenant indexes
      {
        name: 'idx_auto_schedule_session_tenant',
        fields: ['company_id', 'business_unit_id']
      }
    ],
    
    comment: 'Tracks auto-scheduling sessions for monitoring and rollback'
  });

  // Define associations
  AutoScheduleSession.associate = function(models) {
    // Schedule association
    AutoScheduleSession.belongsTo(models.RotaSchedule, {
      foreignKey: 'scheduleId',
      as: 'schedule'
    });
    
    // User associations
    AutoScheduleSession.belongsTo(models.User, {
      foreignKey: 'initiatedBy',
      as: 'initiator'
    });
    
    AutoScheduleSession.belongsTo(models.User, {
      foreignKey: 'rollbackBy',
      as: 'rollbackUser'
    });
    
    // Tracking associations
    AutoScheduleSession.hasMany(models.ShiftAssignmentTracker, {
      foreignKey: 'sessionId',
      sourceKey: 'sessionId',
      as: 'assignmentTrackers'
    });
    
    // Multi-tenant associations
    AutoScheduleSession.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    AutoScheduleSession.belongsTo(models.BusinessUnit, {
      foreignKey: 'businessUnitId',
      as: 'businessUnit'
    });
  };

  return AutoScheduleSession;
};
