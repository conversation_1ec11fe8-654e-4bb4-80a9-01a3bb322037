'use strict';

module.exports = (sequelize, DataTypes) => {
  const RotaShiftInstanceDesignationRequirement = sequelize.define('RotaShiftInstanceDesignationRequirement', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    shiftInstanceId: {
      field: 'shift_instance_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'rota_shift_instances',
        key: 'id'
      },
    },
    designationId: {
      field: 'designation_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'designations',
        key: 'id'
      },
    },
    requiredCount: {
      field: 'required_count',
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        min: 0
      }
    },
    assignedCount: {
      field: 'assigned_count',
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    priority: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    requirementType: {
      field: 'requirement_type',
      type: DataTypes.ENUM('base', 'custom', 'override'),
      allowNull: false,
      defaultValue: 'base'
    },
    sourceType: {
      field: 'source_type',
      type: DataTypes.ENUM('template', 'rotaShift', 'manual', 'forecast'),
      allowNull: false,
      defaultValue: 'template'
    },
    overrideReason: {
      field: 'override_reason',
      type: DataTypes.TEXT,
      allowNull: true
    },
    createdById: {
      field: 'created_by_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updatedById: {
      field: 'updated_by_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'rota_shift_instance_designation_requirements',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['shift_instance_id', 'designation_id'],
        unique: true,
        name: 'unique_instance_designation'
      },
      {
        fields: ['shift_instance_id'],
        name: 'idx_shift_instance_id'
      },
      {
        fields: ['designation_id'],
        name: 'idx_designation_id'
      },
      {
        fields: ['shift_instance_id', 'required_count'],
        name: 'idx_instance_required'
      },
      {
        fields: ['designation_id', 'required_count'],
        name: 'idx_designation_required'
      }
    ]
  });

  RotaShiftInstanceDesignationRequirement.associate = (models) => {
    // Belongs to RotaShiftInstance
    RotaShiftInstanceDesignationRequirement.belongsTo(models.RotaShiftInstance, {
      foreignKey: {
        name: 'shiftInstanceId',
        field: 'shift_instance_id'
      },
      as: 'shiftInstance'
    });

    // Belongs to Designation
    RotaShiftInstanceDesignationRequirement.belongsTo(models.Designation, {
      foreignKey: {
        name: 'designationId',
        field: 'designation_id'
      },
      as: 'designation'
    });

    // Belongs to User (creator)
    RotaShiftInstanceDesignationRequirement.belongsTo(models.User, {
      foreignKey: {
        name: 'createdById',
        field: 'created_by_id'
      },
      as: 'createdBy'
    });

    // Belongs to User (updater)
    RotaShiftInstanceDesignationRequirement.belongsTo(models.User, {
      foreignKey: {
        name: 'updatedById',
        field: 'updated_by_id'
      },
      as: 'updatedBy'
    });
  };

  return RotaShiftInstanceDesignationRequirement;
};
