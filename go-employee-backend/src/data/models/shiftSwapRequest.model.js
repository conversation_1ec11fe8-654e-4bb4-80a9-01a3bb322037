'use strict';

module.exports = (sequelize, DataTypes) => {
  const ShiftSwapRequest = sequelize.define('ShiftSwapRequest', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    companyId: {
      field: 'company_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id'
      }
    },
    businessUnitId: {
      field: 'business_unit_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'business_units',
        key: 'id'
      }
    },
    // ENHANCED: Requester information
    requesterId: {
      field: 'requester_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      }
    },

    // ENHANCED: Current shift assignment to swap away
    currentShiftAssignmentId: {
      field: 'current_shift_assignment_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'shift_assignments',
        key: 'id'
      }
    },

    // ENHANCED: Date when swap should happen
    swapDate: {
      field: 'swap_date',
      type: DataTypes.DATEONLY,
      allowNull: false
    },

    // ENHANCED: Desired shift (optional - what they want)
    desiredShiftId: {
      field: 'desired_shift_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'rota_shifts',
        key: 'id'
      }
    },

    // ENHANCED: Target employee (optional - specific person)
    targetEmployeeId: {
      field: 'target_employee_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      }
    },

    // ENHANCED: Flexible swap options (boolean flags)
    swapWithAnyAvailableEmployee: {
      field: 'swap_with_any_available_employee',
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },

    swapWithAnyAvailableShift: {
      field: 'swap_with_any_available_shift',
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },

    // ENHANCED: Categorized reason for swap
    reasonForSwap: {
      field: 'reason_for_swap',
      type: DataTypes.ENUM(
        'personal_emergency',
        'medical_appointment',
        'family_commitment',
        'education_training',
        'transportation_issue',
        'work_life_balance',
        'other'
      ),
      allowNull: false
    },

    // ENHANCED: Detailed reason description
    reasonDescription: {
      field: 'reason_description',
      type: DataTypes.TEXT,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM(
        'pending',                    // Initial request
        'approved',                   // Manager approved
        'rejected',                   // Manager rejected
        'cancelled',                  // Cancelled by requester
        'completed'                   // Swap executed successfully (final status)
      ),
      defaultValue: 'pending'
    },
    acceptingEmployeeId: {
      field: 'accepting_employee_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      },
    },
    // ENHANCED: Urgency level
    urgency: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      allowNull: false,
      defaultValue: 'medium'
    },

    // Keep existing fields
    rejectionReason: {
      field: 'rejection_reason',
      type: DataTypes.TEXT,
      allowNull: true,
    },

    // Notes field (replaces message)
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    requestDate: {
      field: 'request_date',
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    responseDate: {
      field: 'response_date',
      type: DataTypes.DATE,
      allowNull: true
    },
    respondedBy: {
      field: 'responded_by',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    managerNotes: {
      field: 'manager_notes',
      type: DataTypes.TEXT,
      allowNull: true,
    },
    alternativeShiftId: {
      field: 'alternative_shift_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'rota_shifts',
        key: 'id'
      },
    },
    responseDeadline: {
      field: 'response_deadline',
      type: DataTypes.DATE,
      allowNull: true,
    },
    managerApprovalDeadline: {
      field: 'manager_approval_deadline',
      type: DataTypes.DATE,
      allowNull: true,
    },
    isExpired: {
      field: 'is_expired',
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    expiredAt: {
      field: 'expired_at',
      type: DataTypes.DATE,
      allowNull: true,
    },
    requesterAssignmentId: {
      field: 'requester_assignment_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'shift_assignments',
        key: 'id'
      }
    },
    targetAssignmentId: {
      field: 'target_assignment_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'shift_assignments',
        key: 'id'
      }
    },

    // ENHANCED: Recurring pattern support
    isRecurring: {
      field: 'is_recurring',
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },

    recurringPattern: {
      field: 'recurring_pattern',
      type: DataTypes.JSONB,
      allowNull: true,
      // comment: 'Recurring pattern configuration (frequency, endDate, specificDays)'
    },

    // ENHANCED: Notification preferences
    notificationPreferences: {
      field: 'notification_preferences',
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'User notification preferences for this swap request'
    },


    // ENHANCED: Slot reservation to prevent over-booking
    reservedSlotInfo: {
      field: 'reserved_slot_info',
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Information about reserved slot for this swap request to prevent over-booking'
    }
  }, {
    tableName: 'shift_swap_requests',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['company_id', 'business_unit_id']
      },
      {
        fields: ['company_id', 'requester_id']
      },
      {
        fields: ['company_id', 'target_employee_id']
      },
      {
        fields: ['company_id', 'desired_shift_id']
      },
      {
        fields: ['company_id', 'status']
      },
      {
        fields: ['company_id', 'request_date']
      },
      {
        fields: ['swap_date', 'status']
      },
      {
        fields: ['current_shift_assignment_id']
      }
    ]
  });

  // Add hooks to set default values
  ShiftSwapRequest.addHook('beforeCreate', (instance, options) => {
    // Set default notification preferences if not provided
    if (!instance.notificationPreferences) {
      instance.notificationPreferences = {
        emailNotification: true,
        smsNotification: false,
        pushNotification: true
      };
    }
  });

  ShiftSwapRequest.associate = (models) => {
    // ShiftSwapRequest belongs to Company
    ShiftSwapRequest.belongsTo(models.Company, {
      foreignKey: {
        name: 'companyId',
        field: 'company_id'
      },
      as: 'company'
    });

    // ShiftSwapRequest belongs to BusinessUnit
    ShiftSwapRequest.belongsTo(models.BusinessUnit, {
      foreignKey: {
        name: 'businessUnitId',
        field: 'business_unit_id'
      },
      as: 'businessUnit'
    });

    // ENHANCED: ShiftSwapRequest belongs to Employee (requester)
    ShiftSwapRequest.belongsTo(models.Employee, {
      foreignKey: {
        name: 'requesterId',
        field: 'requester_id'
      },
      as: 'requester'
    });

    // ENHANCED: ShiftSwapRequest belongs to Employee (target employee)
    ShiftSwapRequest.belongsTo(models.Employee, {
      foreignKey: {
        name: 'targetEmployeeId',
        field: 'target_employee_id'
      },
      as: 'targetEmployee'
    });

    // ShiftSwapRequest belongs to Employee (accepting employee)
    ShiftSwapRequest.belongsTo(models.Employee, {
      foreignKey: {
        name: 'acceptingEmployeeId',
        field: 'accepting_employee_id'
      },
      as: 'acceptingEmployee'
    });

    // ENHANCED: ShiftSwapRequest belongs to ShiftAssignment (current assignment)
    ShiftSwapRequest.belongsTo(models.ShiftAssignment, {
      foreignKey: {
        name: 'currentShiftAssignmentId',
        field: 'current_shift_assignment_id'
      },
      as: 'currentShiftAssignment'
    });

    // ENHANCED: ShiftSwapRequest belongs to RotaShift (desired shift)
    ShiftSwapRequest.belongsTo(models.RotaShift, {
      foreignKey: {
        name: 'desiredShiftId',
        field: 'desired_shift_id'
      },
      as: 'desiredShift'
    });

    // Note: Removed invalid associations for fields that don't exist in the model
    // The model uses currentShiftAssignmentId and desiredShiftId instead

    // ShiftSwapRequest belongs to RotaShift (alternative shift)
    ShiftSwapRequest.belongsTo(models.RotaShift, {
      foreignKey: {
        name: 'alternativeShiftId',
        field: 'alternative_shift_id'
      },
      as: 'alternativeShift'
    });

    // ShiftSwapRequest belongs to User (responder)
    ShiftSwapRequest.belongsTo(models.User, {
      foreignKey: {
        name: 'respondedBy',
        field: 'responded_by'
      },
      as: 'responder'
    });

    // ShiftSwapRequest belongs to ShiftAssignment (requesting employee assignment)
    ShiftSwapRequest.belongsTo(models.ShiftAssignment, {
      foreignKey: {
        name: 'requesterAssignmentId',
        field: 'requester_assignment_id'
      },
      as: 'requestingEmployeeAssignment'
    });

    // ShiftSwapRequest belongs to ShiftAssignment (target employee assignment)
    ShiftSwapRequest.belongsTo(models.ShiftAssignment, {
      foreignKey: {
        name: 'targetAssignmentId',
        field: 'target_assignment_id'
      },
      as: 'targetEmployeeAssignment'
    });
  };

  return ShiftSwapRequest;
};
