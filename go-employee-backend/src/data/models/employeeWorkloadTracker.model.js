/**
 * EmployeeWorkloadTracker Model
 * 
 * Purpose: Track employee workload distribution for fair assignment
 * Benefits:
 * - Balanced workload distribution
 * - Prevent overworking employees
 * - Historical workload analysis
 * - Performance optimization
 */

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const EmployeeWorkloadTracker = sequelize.define('EmployeeWorkloadTracker', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    
    // Employee Information
    employeeId: {
      field: 'employee_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      }
    },
    
    designationId: {
      field: 'designation_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'designations',
        key: 'id'
      }
    },
    
    // Tracking Period
    trackingPeriod: {
      field: 'tracking_period',
      type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'quarterly'),
      allowNull: false,
      defaultValue: 'weekly'
    },
    
    periodStartDate: {
      field: 'period_start_date',
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    
    periodEndDate: {
      field: 'period_end_date',
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    
    // Workload Metrics
    totalShiftsAssigned: {
      field: 'total_shifts_assigned',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    totalHoursScheduled: {
      field: 'total_hours_scheduled',
      type: DataTypes.DECIMAL(8, 2),
      defaultValue: 0.00,
      validate: { min: 0 }
    },
    
    totalHoursWorked: {
      field: 'total_hours_worked',
      type: DataTypes.DECIMAL(8, 2),
      defaultValue: 0.00,
      validate: { min: 0 }
    },
    
    overtimeHours: {
      field: 'overtime_hours',
      type: DataTypes.DECIMAL(8, 2),
      defaultValue: 0.00,
      validate: { min: 0 }
    },
    
    // Shift Type Distribution
    morningShifts: {
      field: 'morning_shifts',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    afternoonShifts: {
      field: 'afternoon_shifts',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    eveningShifts: {
      field: 'evening_shifts',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    nightShifts: {
      field: 'night_shifts',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    weekendShifts: {
      field: 'weekend_shifts',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    holidayShifts: {
      field: 'holiday_shifts',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    // Consecutive Work Tracking
    maxConsecutiveDays: {
      field: 'max_consecutive_days',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0, max: 31 }
    },
    
    currentConsecutiveDays: {
      field: 'current_consecutive_days',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0, max: 31 }
    },
    
    lastWorkDate: {
      field: 'last_work_date',
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    
    lastRestDate: {
      field: 'last_rest_date',
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    
    // Assignment Performance
    assignmentScore: {
      field: 'assignment_score',
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 50.00,
      validate: { min: 0, max: 100 },
      comment: 'Overall assignment performance score (0-100)'
    },
    
    balanceScore: {
      field: 'balance_score',
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 50.00,
      validate: { min: 0, max: 100 },
      comment: 'Workload balance score compared to peers (0-100)'
    },
    
    // Availability Metrics
    availabilityPercentage: {
      field: 'availability_percentage',
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 100.00,
      validate: { min: 0, max: 100 },
      comment: 'Percentage of time employee is available'
    },
    
    absenceCount: {
      field: 'absence_count',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    lateCount: {
      field: 'late_count',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: { min: 0 }
    },
    
    // Preference Compliance
    preferenceCompliance: {
      field: 'preference_compliance',
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 100.00,
      validate: { min: 0, max: 100 },
      comment: 'How well assignments match employee preferences'
    },
    
    // Status and Flags
    isOverworked: {
      field: 'is_overworked',
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Flag indicating if employee is overworked'
    },
    
    isUnderworked: {
      field: 'is_underworked',
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Flag indicating if employee is underworked'
    },
    
    needsRest: {
      field: 'needs_rest',
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Flag indicating if employee needs rest'
    },
    
    // Audit Fields
    lastUpdated: {
      field: 'last_updated',
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    
    updatedBy: {
      field: 'updated_by',
      type: DataTypes.ENUM('system', 'manual', 'auto_schedule'),
      defaultValue: 'system'
    },
    
    // Multi-tenant Support
    companyId: {
      field: 'company_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id'
      }
    },
    
    businessUnitId: {
      field: 'business_unit_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'business_units',
        key: 'id'
      }
    }
    
  }, {
    tableName: 'employee_workload_trackers',
    timestamps: true,
    underscored: true,
    
    indexes: [
      // Primary tracking indexes
      {
        name: 'idx_workload_tracker_employee_period',
        fields: ['employee_id', 'tracking_period', 'period_start_date'],
        unique: true
      },
      {
        name: 'idx_workload_tracker_designation_period',
        fields: ['designation_id', 'tracking_period', 'period_start_date']
      },
      {
        name: 'idx_workload_tracker_overworked',
        fields: ['is_overworked', 'needs_rest']
      },
      {
        name: 'idx_workload_tracker_balance',
        fields: ['balance_score', 'assignment_score']
      },
      
      // Multi-tenant indexes
      {
        name: 'idx_workload_tracker_tenant',
        fields: ['company_id', 'business_unit_id']
      }
    ],
    
    comment: 'Tracks employee workload distribution for balanced assignment'
  });

  // Define associations
  EmployeeWorkloadTracker.associate = function(models) {
    // Employee association
    EmployeeWorkloadTracker.belongsTo(models.Employee, {
      foreignKey: 'employeeId',
      as: 'employee'
    });
    
    // Designation association
    EmployeeWorkloadTracker.belongsTo(models.Designation, {
      foreignKey: 'designationId',
      as: 'designation'
    });
    
    // Multi-tenant associations
    EmployeeWorkloadTracker.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    EmployeeWorkloadTracker.belongsTo(models.BusinessUnit, {
      foreignKey: 'businessUnitId',
      as: 'businessUnit'
    });
  };

  return EmployeeWorkloadTracker;
};
