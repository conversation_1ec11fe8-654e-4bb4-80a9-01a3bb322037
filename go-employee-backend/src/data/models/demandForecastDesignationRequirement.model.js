'use strict';

module.exports = (sequelize, DataTypes) => {
  const DemandForecastDesignationRequirement = sequelize.define('DemandForecastDesignationRequirement', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    instanceId: {
      field: 'instance_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'demand_forecast_instances',
        key: 'id'
      }
    },
    designationId: {
      field: 'designation_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'designations',
        key: 'id'
      }
    },
    
    // ✅ REQUIREMENT DETAILS
    requiredCount: {
      field: 'required_count',
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        min: 0
      },
      // comment: 'Number of employees required for this designation'
    },
    priority: {
      type: DataTypes.INTEGER,
      defaultValue: 5,
      validate: {
        min: 1,
        max: 10
      },
      // comment: 'Priority level (1=highest, 10=lowest)'
    },
    
    // ✅ FORECAST CONFIDENCE
    confidence: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      validate: {
        min: 0,
        max: 100
      },
      // comment: 'Confidence level for this designation requirement'
    },
    
    // ✅ SKILL/EXPERIENCE REQUIREMENTS
    minExperienceMonths: {
      field: 'min_experience_months',
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0
      },
      // comment: 'Minimum experience required in months'
    },
    requiredSkills: {
      field: 'required_skills',
      type: DataTypes.JSON,
      allowNull: true,
      // comment: 'Array of required skill IDs'
    },
    
    // ✅ COST ESTIMATION
    estimatedHourlyCost: {
      field: 'estimated_hourly_cost',
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      // comment: 'Estimated hourly cost for this designation'
    },
    
    // ✅ OVERRIDE SUPPORT
    isOverridden: {
      field: 'is_overridden',
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      // comment: 'Whether this requirement has been manually overridden'
    },
    overrideReason: {
      field: 'override_reason',
      type: DataTypes.TEXT,
      allowNull: true,
      // comment: 'Reason for manual override'
    },
    originalRequiredCount: {
      field: 'original_required_count',
      type: DataTypes.INTEGER,
      allowNull: true,
      // comment: 'Original count before override'
    },
    
    // ✅ FORECAST METADATA
    forecastMethod: {
      field: 'forecast_method',
      type: DataTypes.ENUM('manual', 'historical', 'template_based', 'seasonal', 'predictive'),
      defaultValue: 'manual',
      // comment: 'Method used to generate this requirement'
    },
    historicalAverage: {
      field: 'historical_average',
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      // comment: 'Historical average for this designation on similar dates'
    },
    seasonalAdjustment: {
      field: 'seasonal_adjustment',
      type: DataTypes.DECIMAL(3, 2),
      allowNull: true,
      // comment: 'Seasonal adjustment factor applied'
    },
    
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isActive: {
      field: 'is_active',
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdById: {
      field: 'created_by_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updatedById: {
      field: 'updated_by_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'demand_forecast_designation_requirements',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    paranoid: false,
    indexes: [
      {
        unique: true,
        fields: ['instance_id', 'designation_id'],
        name: 'unique_requirement_per_instance_designation'
      },
      {
        fields: ['instance_id']
      },
      {
        fields: ['designation_id']
      },
      {
        fields: ['required_count']
      },
      {
        fields: ['priority']
      },
      {
        fields: ['forecast_method']
      },
      {
        fields: ['is_overridden']
      }
    ]
  });

  DemandForecastDesignationRequirement.associate = function(models) {
    // Parent instance
    DemandForecastDesignationRequirement.belongsTo(models.DemandForecastInstance, {
      foreignKey: 'instanceId',
      as: 'instance'
    });

    // Designation
    DemandForecastDesignationRequirement.belongsTo(models.Designation, {
      foreignKey: 'designationId',
      as: 'designation'
    });

    // User associations
    DemandForecastDesignationRequirement.belongsTo(models.User, {
      foreignKey: 'createdById',
      as: 'createdBy'
    });

    DemandForecastDesignationRequirement.belongsTo(models.User, {
      foreignKey: 'updatedById',
      as: 'updatedBy'
    });
  };

  return DemandForecastDesignationRequirement;
};
