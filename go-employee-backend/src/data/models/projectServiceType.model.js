'use strict';

module.exports = (sequelize, DataTypes) => {
  const ProjectServiceType = sequelize.define('ProjectServiceType', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    projectId: {
      field: 'project_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'projects',
        key: 'id'
      }
    },
    serviceTypeId: {
      field: 'service_type_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'service_types',
        key: 'id'
      }
    },
    createdBy: {
      field: 'created_by',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      }
    },
    updatedBy: {
      field: 'updated_by',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      }
    }
  }, {
    tableName: 'project_service_types',
    timestamps: true,
    paranoid: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['project_id', 'service_type_id']
      }
    ]
  });

  ProjectServiceType.associate = function(models) {
    ProjectServiceType.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project'
    });
    
    ProjectServiceType.belongsTo(models.ServiceType, {
      foreignKey: 'serviceTypeId',
      as: 'serviceType'
    });
    
    ProjectServiceType.belongsTo(models.Employee, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
    
    ProjectServiceType.belongsTo(models.Employee, {
      foreignKey: 'updatedBy',
      as: 'updater'
    });
  };

  return ProjectServiceType;
};
