'use strict';

module.exports = (sequelize, DataTypes) => {
  const ServiceType = sequelize.define('ServiceType', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    companyId: {
      field: 'company_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    createdBy: {
      field: 'created_by',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      }
    },
    updatedBy: {
      field: 'updated_by',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      }
    }
  }, {
    tableName: 'service_types',
    timestamps: true,
    paranoid: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['company_id', 'name']
      },
      {
        fields: ['company_id']
      }
    ]
  });

  ServiceType.associate = function(models) {
    ServiceType.belongsTo(models.Company, { 
      foreignKey: 'companyId', 
      as: 'company' 
    });
    
    ServiceType.belongsToMany(models.Project, {
      through: models.ProjectServiceType,
      foreignKey: 'serviceTypeId',
      otherKey: 'projectId',
      as: 'projects'
    });
    
    ServiceType.belongsTo(models.Employee, { 
      foreignKey: 'createdBy', 
      as: 'creator' 
    });
    
    ServiceType.belongsTo(models.Employee, { 
      foreignKey: 'updatedBy', 
      as: 'updater' 
    });
  };

  return ServiceType;
};
