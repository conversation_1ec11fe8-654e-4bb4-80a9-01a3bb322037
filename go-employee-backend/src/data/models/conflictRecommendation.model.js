const { DataTypes } = require('sequelize');

/**
 * ConflictRecommendation Model - Stores actionable recommendations for conflict resolution
 * 
 * This model stores specific recommendations for resolving scheduling conflicts,
 * including priority, effort estimates, and action steps.
 */

module.exports = (sequelize) => {
  const ConflictRecommendation = sequelize.define('ConflictRecommendation', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },

    // Recommendation Identification
    recommendationId: {
      field: 'recommendation_id',
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: 'Unique recommendation identifier'
    },

    // Related Conflict
    conflictId: {
      field: 'conflict_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'schedule_conflicts',
        key: 'id'
      },
      comment: 'Related conflict ID'
    },

    // Recommendation Details
    type: {
      type: DataTypes.ENUM(
        'employee_pool',
        'constraint_relaxation',
        'cross_training',
        'availability_management',
        'shift_adjustment',
        'workload_rebalancing',
        'process_improvement',
        'system_configuration'
      ),
      allowNull: false,
      comment: 'Type of recommendation'
    },

    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      allowNull: false,
      defaultValue: 'medium',
      comment: 'Recommendation priority'
    },

    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: 'Brief recommendation title'
    },

    description: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Detailed recommendation description'
    },

    // Impact Assessment
    expectedImpact: {
      field: 'expected_impact',
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Expected impact of implementing recommendation'
    },

    effortLevel: {
      field: 'effort_level',
      type: DataTypes.ENUM('low', 'medium', 'high'),
      allowNull: false,
      defaultValue: 'medium',
      comment: 'Effort required to implement'
    },

    estimatedTimeline: {
      field: 'estimated_timeline',
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: 'Estimated implementation timeline'
    },

    // Action Steps
    actionSteps: {
      field: 'action_steps',
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Specific action steps (JSON array)'
    },

    // Implementation Status
    status: {
      type: DataTypes.ENUM('pending', 'in_progress', 'completed', 'rejected', 'deferred'),
      allowNull: false,
      defaultValue: 'pending',
      comment: 'Implementation status'
    },

    implementedAt: {
      field: 'implemented_at',
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When recommendation was implemented'
    },

    implementedBy: {
      field: 'implemented_by',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'User who implemented the recommendation'
    },

    // Results Tracking
    implementationNotes: {
      field: 'implementation_notes',
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Notes about implementation process'
    },

    effectivenessRating: {
      field: 'effectiveness_rating',
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5
      },
      comment: 'Effectiveness rating (1-5) after implementation'
    },

    // Configuration Changes (if applicable)
    configurationChanges: {
      field: 'configuration_changes',
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'System configuration changes made (JSON)'
    },

    // Multi-tenant Support
    companyId: {
      field: 'company_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id'
      }
    },

    // Audit Fields
    createdAt: {
      field: 'created_at',
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },

    updatedAt: {
      field: 'updated_at',
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }

  }, {
    tableName: 'conflict_recommendations',
    timestamps: true,
    underscored: true,
    
    indexes: [
      // Primary search indexes
      {
        name: 'idx_conflict_recommendations_conflict',
        fields: ['conflict_id']
      },
      {
        name: 'idx_conflict_recommendations_type_priority',
        fields: ['type', 'priority']
      },
      {
        name: 'idx_conflict_recommendations_status',
        fields: ['status']
      },
      {
        name: 'idx_conflict_recommendations_company',
        fields: ['company_id']
      },
      
      // Implementation tracking
      {
        name: 'idx_conflict_recommendations_implementation',
        fields: ['status', 'implemented_at']
      }
    ],
    
    comment: 'Stores actionable recommendations for conflict resolution'
  });

  // Model associations
  ConflictRecommendation.associate = function(models) {
    // Conflict relationship
    ConflictRecommendation.belongsTo(models.ScheduleConflict, {
      foreignKey: 'conflictId',
      as: 'conflict'
    });

    // Company relationship
    ConflictRecommendation.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });

    // Implementer relationship
    ConflictRecommendation.belongsTo(models.User, {
      foreignKey: 'implementedBy',
      as: 'implementer'
    });
  };

  return ConflictRecommendation;
};
