'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class CheckinReminderLog extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define associations here
      CheckinReminderLog.belongsTo(models.Employee, {
        foreignKey: 'employeeId',
        as: 'employee'
      });

      CheckinReminderLog.belongsTo(models.BusinessUnit, {
        foreignKey: 'businessUnitId',
        as: 'businessUnit'
      });

      CheckinReminderLog.belongsTo(models.ShiftType, {
        foreignKey: 'shiftTypeId',
        as: 'shiftType'
      });
    }

    /**
     * Check if reminder was already sent to employee for specific date and shift
     * @param {number} employeeId - Employee ID
     * @param {string} reminderDate - Date in YYYY-MM-DD format
     * @param {number} shiftTypeId - Shift Type ID (optional)
     * @returns {Promise<boolean>} True if reminder already sent
     */
    static async isReminderAlreadySent(employeeId, reminderDate, shiftTypeId = null) {
      const whereCondition = {
        employeeId,
        reminderDate
      };

      if (shiftTypeId) {
        whereCondition.shiftTypeId = shiftTypeId;
      }

      const existingReminder = await CheckinReminderLog.findOne({
        where: whereCondition
      });

      return !!existingReminder;
    }

    /**
     * Log a sent reminder
     * @param {Object} reminderData - Reminder data
     * @returns {Promise<CheckinReminderLog>} Created reminder log
     */
    static async logSentReminder(reminderData) {
      return await CheckinReminderLog.create({
        employeeId: reminderData.employeeId,
        businessUnitId: reminderData.businessUnitId,
        shiftTypeId: reminderData.shiftTypeId,
        reminderDate: reminderData.reminderDate,
        shiftStartTime: reminderData.shiftStartTime,
        employeeEmail: reminderData.employeeEmail,
        reminderType: reminderData.reminderType || 'SHIFT_START',
        reminderSentAt: new Date()
      });
    }

    /**
     * Get reminder statistics for a business unit and date
     * @param {number} businessUnitId - Business Unit ID
     * @param {string} reminderDate - Date in YYYY-MM-DD format
     * @returns {Promise<Object>} Reminder statistics
     */
    static async getReminderStats(businessUnitId, reminderDate) {
      const stats = await CheckinReminderLog.findAll({
        where: {
          businessUnitId,
          reminderDate
        },
        include: [
          {
            model: sequelize.models.ShiftType,
            as: 'shiftType',
            attributes: ['id', 'name', 'startTime', 'endTime']
          }
        ]
      });

      return {
        totalReminders: stats.length,
        remindersByShift: stats.reduce((acc, reminder) => {
          const shiftName = reminder.shiftType?.name || 'Unknown Shift';
          acc[shiftName] = (acc[shiftName] || 0) + 1;
          return acc;
        }, {}),
        remindersByType: stats.reduce((acc, reminder) => {
          acc[reminder.reminderType] = (acc[reminder.reminderType] || 0) + 1;
          return acc;
        }, {})
      };
    }

    /**
     * Clean up old reminder logs (older than specified days)
     * @param {number} daysToKeep - Number of days to keep logs
     * @returns {Promise<number>} Number of deleted records
     */
    static async cleanupOldLogs(daysToKeep = 30) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const deletedCount = await CheckinReminderLog.destroy({
        where: {
          reminderDate: {
            [sequelize.Sequelize.Op.lt]: cutoffDate.toISOString().split('T')[0]
          }
        }
      });

      return deletedCount;
    }
  }

  CheckinReminderLog.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    employeeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'employee_id'
    },
    businessUnitId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'business_unit_id'
    },
    shiftTypeId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'shift_type_id'
    },
    reminderDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'reminder_date'
    },
    shiftStartTime: {
      type: DataTypes.TIME,
      allowNull: true,
      field: 'shift_start_time'
    },
    reminderSentAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'reminder_sent_at'
    },
    employeeEmail: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'employee_email'
    },
    reminderType: {
      type: DataTypes.ENUM('SHIFT_START', 'DAILY_GENERAL'),
      allowNull: false,
      defaultValue: 'SHIFT_START',
      field: 'reminder_type'
    }
  }, {
    sequelize,
    modelName: 'CheckinReminderLog',
    tableName: 'checkin_reminder_logs',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['employee_id', 'reminder_date', 'shift_type_id'],
        name: 'unique_employee_date_shift_reminder'
      },
      {
        fields: ['business_unit_id', 'reminder_date'],
        name: 'idx_business_unit_date'
      },
      {
        fields: ['reminder_date'],
        name: 'idx_reminder_date'
      }
    ]
  });

  return CheckinReminderLog;
};
