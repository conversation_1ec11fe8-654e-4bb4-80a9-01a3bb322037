'use strict';

module.exports = (sequelize, DataTypes) => {
  const DemandForecastInstance = sequelize.define('DemandForecastInstance', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    forecastId: {
      field: 'forecast_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'demand_forecasts',
        key: 'id'
      }
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Specific date for this forecast instance'
    },

    // ✅ ENHANCED: Add rotaShiftId for better integration with RotaShiftInstance
    rotaShiftId: {
      field: 'rota_shift_id',
      type: DataTypes.INTEGER,
      allowNull: false, // Make it required since we always need a shift reference
      references: {
        model: 'rota_shifts',
        key: 'id'
      },
      comment: 'Reference to RotaShift - all shift details come from this table'
    },
    
    totalRequiredCount: {
      field: 'total_required_count',
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    confidence: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      validate: {
        min: 0,
        max: 100
      },
    },
    forecastMethod: {
      field: 'forecast_method',
      type: DataTypes.ENUM('manual', 'historical', 'template_based', 'seasonal', 'predictive'),
      defaultValue: 'manual',
    },
    
    isOverridden: {
      field: 'is_overridden',
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this instance has been manually overridden'
    },
    overrideReason: {
      field: 'override_reason',
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Reason for manual override'
    },
    
    externalEventId: {
      field: 'external_event_id',
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    weatherFactor: {
      field: 'weather_factor',
      type: DataTypes.DECIMAL(3, 2),
      allowNull: true,
    },
    seasonalFactor: {
      field: 'seasonal_factor',
      type: DataTypes.DECIMAL(3, 2),
      allowNull: true,
    },
    
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isActive: {
      field: 'is_active',
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdById: {
      field: 'created_by_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      }
    },
    updatedById: {
      field: 'updated_by_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'employees',
        key: 'id'
      }
    }
  }, {
    tableName: 'demand_forecast_instances',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    paranoid: true,
    deletedAt: 'deleted_at',
    indexes: [
      {
        unique: true,
        fields: ['forecast_id', 'date', 'shift_name'],
        name: 'unique_forecast_instance_per_date_shift'
      },
      {
        fields: ['forecast_id']
      },
      {
        fields: ['date']
      },
      {
        fields: ['forecast_method']
      },
      {
        fields: ['is_overridden']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  DemandForecastInstance.associate = function(models) {
    // Parent forecast
    DemandForecastInstance.belongsTo(models.DemandForecast, {
      foreignKey: 'forecastId',
      as: 'forecast'
    });

    // ✅ DESIGNATION REQUIREMENTS - Multiple designations per instance
    DemandForecastInstance.hasMany(models.DemandForecastDesignationRequirement, {
      foreignKey: 'instanceId',
      as: 'designationRequirements'
    });

    // ✅ ROTA SHIFT ASSOCIATION - For template-based forecasts
    DemandForecastInstance.belongsTo(models.RotaShift, {
      foreignKey: 'rotaShiftId',
      as: 'rotaShift'
    });

    // User associations
    DemandForecastInstance.belongsTo(models.Employee, {
      foreignKey: 'createdById',
      as: 'createdBy'
    });

    DemandForecastInstance.belongsTo(models.Employee, {
      foreignKey: 'updatedById',
      as: 'updatedBy'
    });
  };

  return DemandForecastInstance;
};
