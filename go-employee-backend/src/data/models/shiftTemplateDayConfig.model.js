'use strict';

module.exports = (sequelize, DataTypes) => {
  const ShiftTemplateDayConfig = sequelize.define('ShiftTemplateDayConfig', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    shiftTemplateId: {
      field: 'shift_template_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'shift_templates',
        key: 'id'
      }
    },
    dayOfWeek: {
      field: 'day_of_week',
      type: DataTypes.ENUM('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'),
      allowNull: false
    },
    isWorkingDay: {
      field: 'is_working_day',
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    dayType: {
      field: 'day_type',
      type: DataTypes.ENUM('regular', 'overtime', 'holiday', 'emergency', 'training'),
      defaultValue: 'regular'
    },
    priority: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      validate: {
        min: 1,
        max: 10
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isActive: {
      field: 'is_active',
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdById: {
      field: 'created_by_id',
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updatedById: {
      field: 'updated_by_id',
      type: DataTypes.INTEGER,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'shift_template_day_configs',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['shift_template_id', 'day_of_week']
      },
      {
        fields: ['shift_template_id', 'is_active']
      },
      {
        fields: ['day_of_week', 'is_working_day']
      }
    ]
  });

  ShiftTemplateDayConfig.associate = (models) => {
    // Belongs to ShiftTemplate
    ShiftTemplateDayConfig.belongsTo(models.ShiftTemplate, {
      foreignKey: {
        name: 'shiftTemplateId',
        field: 'shift_template_id'
      },
      as: 'shiftTemplate'
    });

    // Has many day shifts
    ShiftTemplateDayConfig.hasMany(models.ShiftTemplateDayShift, {
      foreignKey: {
        name: 'dayConfigId',
        field: 'day_config_id'
      },
      as: 'dayShifts'
    });

    // Belongs to User (created by)
    ShiftTemplateDayConfig.belongsTo(models.User, {
      foreignKey: {
        name: 'createdById',
        field: 'created_by_id'
      },
      as: 'createdBy'
    });

    // Belongs to User (updated by)
    ShiftTemplateDayConfig.belongsTo(models.User, {
      foreignKey: {
        name: 'updatedById',
        field: 'updated_by_id'
      },
      as: 'updatedBy'
    });
  };

  return ShiftTemplateDayConfig;
};
