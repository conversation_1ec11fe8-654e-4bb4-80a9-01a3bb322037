'use strict';

module.exports = (sequelize, DataTypes) => {
  const ShiftAssignment = sequelize.define('ShiftAssignment', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    shiftInstanceId: {
      field: 'shift_instance_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'rota_shift_instances',
        key: 'id'
      },
    },
    employeeId: {
      field: 'employee_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      },
    },
    designationId: {
      field: 'designation_id',
      type: DataTypes.INTEGER,
      allowNull: true, // Allow null for backward compatibility
      references: {
        model: 'designations',
        key: 'id'
      },
      comment: 'Designation for which this employee is assigned'
    },
    assignedAt: {
      field: 'assigned_at',
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    assignedBy: {
      field: 'assigned_by',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
    },
    status: {
      type: DataTypes.ENUM(
        'assigned',     // Employee assigned to shift
        'confirmed',    // Employee confirmed attendance
        'checked_in',   // Employee started work
        'completed',    // Employee finished work
        'absent',       // Employee didn't show up
        'cancelled',    // Assignment cancelled
        'swapped',      // Assignment was swapped
        'emergency_assigned' // Emergency assignment
      ),
      defaultValue: 'assigned',
    },
    assignmentType: {
      field: 'assignment_type',
      type: DataTypes.ENUM(
        'auto_scheduled',     // Auto-assigned by system
        'manual_assigned',    // Manually assigned by manager
        'swap_assigned',      // Assigned through swap
        'emergency_assigned', // Emergency assignment
        'volunteer_assigned'  // Employee volunteered
      ),
      defaultValue: 'manual_assigned',
    },
    actualStartTime: {
      field: 'actual_start_time',
      type: DataTypes.TIME,
      allowNull: true,
    },
    actualEndTime: {
      field: 'actual_end_time',
      type: DataTypes.TIME,
      allowNull: true,
    },
    breakDuration: {
      field: 'break_duration',
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    confirmedAt: {
      field: 'confirmed_at',
      type: DataTypes.DATE,
      allowNull: true,
    },
    completedAt: {
      field: 'completed_at',
      type: DataTypes.DATE,
      allowNull: true,
    },
    swapRequestId: {
      field: 'swap_request_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'shift_swap_requests',
        key: 'id'
      },
    },
    // Bulk operation support
    batchId: {
      field: 'batch_id',
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Batch identifier for bulk assignments'
    },

    // Conflict resolution tracking
    conflictResolution: {
      field: 'conflict_resolution',
      type: DataTypes.JSON,
      allowNull: true,
      // defaultValue: {},
      // comment: 'How conflicts were resolved during assignment'
    },

    // Priority for assignment (useful for auto-assignment)
    priority: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      // comment: 'Assignment priority (higher = more important)'
    },

    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    createdById: {
      field: 'created_by_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updatedById: {
      field: 'updated_by_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'shift_assignments',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['shift_instance_id', 'employee_id', 'designation_id'],
        unique: true,
        name: 'unique_instance_employee_designation'
      },
      {
        fields: ['employee_id']
      },
      {
        fields: ['shift_instance_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['assigned_at']
      },
      {
        fields: ['employee_id', 'status']
      },
      // Performance indexes for bulk operations
      {
        fields: ['batch_id'],
        name: 'idx_batch_id'
      },
      {
        fields: ['assignment_type'],
        name: 'idx_assignment_type'
      },
      {
        fields: ['priority'],
        name: 'idx_priority'
      },
      {
        fields: ['employee_id', 'assigned_at'],
        name: 'idx_employee_assigned_at'
      },
      {
        fields: ['designation_id'],
        name: 'idx_designation_id'
      }
    ]
  });

  ShiftAssignment.associate = (models) => {
    // ShiftAssignment belongs to RotaShiftInstance
    ShiftAssignment.belongsTo(models.RotaShiftInstance, {
      foreignKey: {
        name: 'shiftInstanceId',
        field: 'shift_instance_id'
      },
      as: 'shiftInstance'
    });

    // ShiftAssignment belongs to Employee
    ShiftAssignment.belongsTo(models.Employee, {
      foreignKey: {
        name: 'employeeId',
        field: 'employee_id'
      },
      as: 'employee'
    });

    // ShiftAssignment belongs to Designation
    ShiftAssignment.belongsTo(models.Designation, {
      foreignKey: {
        name: 'designationId',
        field: 'designation_id'
      },
      as: 'designation'
    });

    // ShiftAssignment belongs to User (assigner)
    ShiftAssignment.belongsTo(models.User, {
      foreignKey: {
        name: 'assignedBy',
        field: 'assigned_by'
      },
      as: 'assigner'
    });

    // ShiftAssignment belongs to User (creator)
    ShiftAssignment.belongsTo(models.User, {
      foreignKey: {
        name: 'createdById',
        field: 'created_by_id'
      },
      as: 'createdBy'
    });

    // ShiftAssignment belongs to User (updater)
    ShiftAssignment.belongsTo(models.User, {
      foreignKey: {
        name: 'updatedById',
        field: 'updated_by_id'
      },
      as: 'updatedBy'
    });

    // ShiftAssignment has many ShiftSwapRequest (as requesting employee assignment)
    ShiftAssignment.hasMany(models.ShiftSwapRequest, {
      foreignKey: {
        name: 'requesterAssignmentId',
        field: 'requester_assignment_id'
      },
      as: 'requestingSwapRequests'
    });

    // ShiftAssignment has many ShiftSwapRequest (as target employee assignment)
    ShiftAssignment.hasMany(models.ShiftSwapRequest, {
      foreignKey: {
        name: 'targetAssignmentId',
        field: 'target_assignment_id'
      },
      as: 'targetSwapRequests'
    });
  };

  return ShiftAssignment;
};
