/**
 * ShiftAssignmentTracker Model
 * 
 * Purpose: Real-time tracking of employee assignments during auto-scheduling
 * Benefits: 
 * - Prevents duplicate assignments
 * - Tracks cross-shift conflicts
 * - Enables rollback on failures
 * - Performance optimization
 */

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ShiftAssignmentTracker = sequelize.define('ShiftAssignmentTracker', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    
    // Tracking Context
    sessionId: {
      field: 'session_id',
      type: DataTypes.UUID,
      allowNull: false,
      comment: 'Unique session ID for each auto-schedule run'
    },
    
    scheduleId: {
      field: 'schedule_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'rota_schedules',
        key: 'id'
      }
    },
    
    // Employee Assignment Details
    employeeId: {
      field: 'employee_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'employees',
        key: 'id'
      }
    },
    
    shiftInstanceId: {
      field: 'shift_instance_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'rota_shift_instances',
        key: 'id'
      }
    },
    
    designationId: {
      field: 'designation_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'designations',
        key: 'id'
      }
    },
    
    // Date and Time Tracking
    assignmentDate: {
      field: 'assignment_date',
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Date of the shift assignment'
    },
    
    shiftStartTime: {
      field: 'shift_start_time',
      type: DataTypes.TIME,
      allowNull: true,
      comment: 'Start time of the shift for overlap detection'
    },
    
    shiftEndTime: {
      field: 'shift_end_time',
      type: DataTypes.TIME,
      allowNull: true,
      comment: 'End time of the shift for overlap detection'
    },
    
    // Assignment Status
    status: {
      type: DataTypes.ENUM(
        'pending',      // Assignment in progress
        'assigned',     // Successfully assigned
        'failed',       // Assignment failed
        'rolled_back'   // Assignment was rolled back
      ),
      defaultValue: 'pending'
    },
    
    // Assignment Strategy Used
    assignmentStrategy: {
      field: 'assignment_strategy',
      type: DataTypes.ENUM('smart', 'rotation', 'balanced', 'skill_based', 'fallback'),
      allowNull: false
    },
    
    // Priority and Scoring
    assignmentPriority: {
      field: 'assignment_priority',
      type: DataTypes.INTEGER,
      defaultValue: 5,
      validate: { min: 1, max: 10 },
      comment: 'Priority used during assignment (1=lowest, 10=highest)'
    },
    
    assignmentScore: {
      field: 'assignment_score',
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Score calculated by assignment strategy'
    },
    
    // Conflict Detection
    hasConflicts: {
      field: 'has_conflicts',
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    
    conflictDetails: {
      field: 'conflict_details',
      type: DataTypes.JSON,
      defaultValue: null,
      comment: 'Details of any conflicts detected'
    },
    
    // Performance Metrics
    processingTimeMs: {
      field: 'processing_time_ms',
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Time taken to process this assignment in milliseconds'
    },
    
    // Audit Fields
    createdAt: {
      field: 'created_at',
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    
    updatedAt: {
      field: 'updated_at',
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    
    createdById: {
      field: 'created_by_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    
    // Multi-tenant Support
    companyId: {
      field: 'company_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id'
      }
    },
    
    businessUnitId: {
      field: 'business_unit_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'business_units',
        key: 'id'
      }
    }
    
  }, {
    tableName: 'shift_assignment_trackers',
    timestamps: true,
    underscored: true,
    
    indexes: [
      // Primary tracking indexes
      {
        name: 'idx_shift_assignment_tracker_session',
        fields: ['session_id']
      },
      {
        name: 'idx_shift_assignment_tracker_employee_date',
        fields: ['employee_id', 'assignment_date'],
        unique: false
      },
      {
        name: 'idx_shift_assignment_tracker_schedule',
        fields: ['schedule_id', 'status']
      },
      {
        name: 'idx_shift_assignment_tracker_conflicts',
        fields: ['has_conflicts', 'status']
      },
      
      // Performance indexes
      {
        name: 'idx_shift_assignment_tracker_designation_date',
        fields: ['designation_id', 'assignment_date']
      },
      {
        name: 'idx_shift_assignment_tracker_instance',
        fields: ['shift_instance_id']
      },
      
      // Multi-tenant indexes
      {
        name: 'idx_shift_assignment_tracker_tenant',
        fields: ['company_id', 'business_unit_id']
      }
    ],
    
    comment: 'Tracks employee assignments during auto-scheduling for conflict detection and rollback'
  });

  // Define associations
  ShiftAssignmentTracker.associate = function(models) {
    // Employee association
    ShiftAssignmentTracker.belongsTo(models.Employee, {
      foreignKey: 'employeeId',
      as: 'employee'
    });
    
    // Shift instance association
    ShiftAssignmentTracker.belongsTo(models.RotaShiftInstance, {
      foreignKey: 'shiftInstanceId',
      as: 'shiftInstance'
    });
    
    // Designation association
    ShiftAssignmentTracker.belongsTo(models.Designation, {
      foreignKey: 'designationId',
      as: 'designation'
    });
    
    // Schedule association
    ShiftAssignmentTracker.belongsTo(models.RotaSchedule, {
      foreignKey: 'scheduleId',
      as: 'schedule'
    });
    
    // User association
    ShiftAssignmentTracker.belongsTo(models.User, {
      foreignKey: 'createdById',
      as: 'createdBy'
    });
    
    // Multi-tenant associations
    ShiftAssignmentTracker.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    ShiftAssignmentTracker.belongsTo(models.BusinessUnit, {
      foreignKey: 'businessUnitId',
      as: 'businessUnit'
    });
  };

  return ShiftAssignmentTracker;
};
