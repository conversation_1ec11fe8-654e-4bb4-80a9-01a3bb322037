<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your action is required for Policy</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        
        .email-container {
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: rgb(0, 0, 0);
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .info-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            flex: 1;
        }
        
        .info-value {
            font-weight: 500;
            color: #2c3e50;
            flex: 1;
            text-align: right;
        }
        
        .attendance-details {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .attendance-details h3 {
            margin-top: 0;
            color: #1976d2;
            font-size: 18px;
        }
        
        .time-badge {
            display: inline-block;
            background-color: #667eea;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .work-hours {
            background-color: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            margin: 20px 0;
        }
        
        .work-hours h3 {
            margin: 0 0 10px 0;
            color: #2e7d32;
            font-size: 20px;
        }
        
        .work-hours .hours {
            font-size: 24px;
            font-weight: bold;
            color: #1b5e20;
        }
        
        .alert-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .alert-box .alert-icon {
            font-size: 20px;
            margin-right: 10px;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }
        
        .footer p {
            margin: 5px 0;
        }
        
        .company-logo {
            max-width: 150px;
            margin-bottom: 10px;
        }
        
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .info-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .info-value {
                text-align: left;
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        
        <div class="content">
            <div class="greeting">
                Hello <strong><%= firstName %> <%= lastName %></strong>,
            </div>
            
            <p>
                <%= policyAction === 'create'
                    ? 'Your approval is required for the following policy:'
                    : 'Your approval is required for the updated policy:'
                %>
            </p>

            <span class="info-label">Policy Name:</span>
            <span class="info-value"><%= policyName %></span>

            <br>
            <span class="info-label">Description:</span>
            <span class="info-value"><%= policyDescription %></span> 

            <!-- <br>
            <span class="info-label">Status:</span>
            <span class="info-value"><%= policyStatus %></span> -->

            <br>
            <span class="info-label">Effective Date:</span>
            <span class="info-value"><%= policyEffectiveDate %></span>

            <br>
            <span class="info-label">Expiry Date:</span>
            <span class="info-value"><%= policyExpiryDate %></span>
            
            <!-- <div class="attendance-details">
                <h3>Policy Details</h3>
                <div class="info-row">
                    <p class="info-label">Policy Name:</p>
                    <p class="info-value"><%= policyName %></p>

                    <br>
                    <p class="info-label">Policy Description:</p>
                    <p class="info-value"><%= policyDescription %></p> 
                </div>
            </div>     -->
        </div>
        
        <div class="footer">
            <p><strong>📧 This is an automated notification </strong></p>
            <p style="margin-top: 15px; font-size: 12px; color: #999;">
                Go-Employee
            </p>
        </div>
    </div>
</body>
</html>
