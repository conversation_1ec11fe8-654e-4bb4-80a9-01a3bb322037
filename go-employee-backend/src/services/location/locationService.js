'use strict';

const { EmployeeLocation, Employee, Company, BusinessUnit } = require('../../data/models');
const { NotFoundError, ValidationError, ForbiddenError } = require('../../common/errors');
const { Op } = require('sequelize');
const logger = require('../../common/logging');

/**
 * Location Service
 * Handles business logic for employee location tracking
 */
class LocationService {
  /**
   * Create a new employee location entry
   * @param {Object} locationData - Location data
   * @param {Object} tenantContext - Tenant context with companyId, businessUnitId
   * @param {number} userId - User ID for employee lookup
   * @returns {Promise<Object>} Created location record
   */
  async createEmployeeLocation(locationData, tenantContext, userId) {
    try {
      // Get employee by user ID
      const employee = await Employee.findOne({
        where: {
          userId: userId,
          companyId: tenantContext.companyId,
          status: 'active'
        }
      });

      if (!employee) {
        throw new NotFoundError('Employee not found or not active');
      }

      // Determine business unit context priority
      const businessUnitId = locationData.businessUnitId || 
                           tenantContext.businessUnitId || 
                           employee.businessUnitId;

      // Validate business unit access if provided
      if (businessUnitId && businessUnitId !== employee.businessUnitId) {
        // Check if user has access to the specified business unit
        const hasAccess = await this.validateBusinessUnitAccess(
          employee.id, 
          businessUnitId, 
          tenantContext.companyId
        );
        
        if (!hasAccess) {
          throw new ForbiddenError('Access denied to specified business unit');
        }
      }

      // Prepare location data
      const locationRecord = {
        employeeId: employee.id,
        companyId: tenantContext.companyId,
        businessUnitId: businessUnitId,
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        address: locationData.address,
        locationType: locationData.locationType || 'check-in',
        accuracy: locationData.accuracy,
        timestamp: locationData.timestamp || new Date(),
        deviceInfo: locationData.deviceInfo,
        metadata: locationData.metadata,
        isManual: locationData.isManual || false,
        status: 'active'
      };

      // Create location record
      const createdLocation = await EmployeeLocation.create(locationRecord);

      // Return with employee details
      return await this.getLocationById(createdLocation.id, tenantContext);

    } catch (error) {
      logger.error('Error creating employee location:', error);
      throw error;
    }
  }

  /**
   * Get location by ID with employee details
   * @param {number} locationId - Location ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Location record with employee details
   */
  async getLocationById(locationId, tenantContext) {
    try {
      const location = await EmployeeLocation.findOne({
        where: {
          id: locationId,
          companyId: tenantContext.companyId,
          status: 'active'
        },
        include: [
          {
            model: Employee,
            as: 'employee',
            attributes: ['id', 'employeeId', 'firstName', 'lastName', 'contactEmail', 'profileImage']
          },
          {
            model: BusinessUnit,
            as: 'businessUnit',
            attributes: ['id', 'name', 'code']
          }
        ]
      });

      if (!location) {
        throw new NotFoundError('Location not found');
      }

      return location;
    } catch (error) {
      logger.error('Error getting location by ID:', error);
      throw error;
    }
  }

  /**
   * Get current locations for all employees
   * @param {Object} filters - Filter options
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Paginated list of current employee locations
   */
  async getCurrentEmployeeLocations(filters, tenantContext) {
    try {
      let {
        businessUnitId,
        search,
        page = 1,
        limit = 50,
        sortBy = 'timestamp',
        sortOrder = 'desc',
        startDate,
        endDate
      } = filters;

      // If no date filter provided, default to current date
      if (!startDate && !endDate) {
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
        startDate = today;
        endDate = today;
      }

      // Build where conditions
      const whereConditions = {
        companyId: tenantContext.companyId,
        status: 'active'
      };

      // Apply business unit filter with context priority
      const targetBusinessUnitId = businessUnitId || tenantContext.businessUnitId;
      if (targetBusinessUnitId) {
        whereConditions.businessUnitId = targetBusinessUnitId;
      }

      // Build employee search conditions
      const employeeWhere = {};
      if (search) {
        employeeWhere[Op.or] = [
          { first_name: { [Op.iLike]: `%${search}%` } },
          { last_name: { [Op.iLike]: `%${search}%` } },
          { employee_id: { [Op.iLike]: `%${search}%` } },
          { contact_email: { [Op.iLike]: `%${search}%` } }
        ];
      }

      // Build date filter for subquery (always applied since we default to current date)
      let dateFilter = '';
      if (startDate && endDate) {
        dateFilter = `AND el.timestamp >= :startDate AND el.timestamp <= :endDate`;
      } else if (startDate) {
        dateFilter = `AND el.timestamp = :startDate`;
      } else if (endDate) {
        dateFilter = `AND el.timestamp <= :endDate`;
      }

      // Get latest location for each employee using subquery (only return IDs)
      // Always use date filter since we default to current date
      const latestLocationsSubquery = `
        SELECT DISTINCT ON (el.employee_id) el.id
        FROM employee_locations el
        WHERE el.company_id = :companyId
          AND el.status = 'active'
          AND el.deleted_at IS NULL
          ${targetBusinessUnitId ? 'AND el.business_unit_id = :businessUnitId' : ''}
          ${dateFilter}
        ORDER BY el.employee_id, el.timestamp DESC
      `;

      const replacements = { companyId: tenantContext.companyId };
      if (targetBusinessUnitId) {
        replacements.businessUnitId = targetBusinessUnitId;
      }

      // Add date parameters to replacements
      if (startDate) {
        const startDateTime = new Date(startDate);
        startDateTime.setHours(0, 0, 0, 0);
        replacements.startDate = startDateTime;
      }
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        replacements.endDate = endDateTime;
      }

      // First get total count of unique employees with locations
      // Always use date filter since we default to current date
      const totalCountQuery = `
        SELECT COUNT(DISTINCT el.employee_id) as total
        FROM employee_locations el
        INNER JOIN employees e ON el.employee_id = e.id
        WHERE el.company_id = :companyId
          AND el.status = 'active'
          AND el.deleted_at IS NULL
          AND e.status = 'active'
          ${targetBusinessUnitId ? 'AND el.business_unit_id = :businessUnitId' : ''}
          ${dateFilter}
          ${search ? `AND (
            e.first_name ILIKE :search OR
            e.last_name ILIKE :search OR
            e.employee_id ILIKE :search OR
            e.contact_email ILIKE :search
          )` : ''}
      `;

      const countReplacements = {
        companyId: tenantContext.companyId,
        ...(targetBusinessUnitId && { businessUnitId: targetBusinessUnitId }),
        ...(search && { search: `%${search}%` }),
        ...(startDate && { startDate: replacements.startDate }),
        ...(endDate && { endDate: replacements.endDate })
      };

      const [countResult] = await EmployeeLocation.sequelize.query(totalCountQuery, {
        replacements: countReplacements,
        type: EmployeeLocation.sequelize.QueryTypes.SELECT
      });

      const totalCount = parseInt(countResult.total);

      // Calculate offset and pagination
      const offset = (page - 1) * limit;
      const totalPages = Math.ceil(totalCount / limit);

      // Get paginated locations with employee details
      const rows = await EmployeeLocation.findAll({
        where: {
          id: {
            [Op.in]: EmployeeLocation.sequelize.literal(`(${latestLocationsSubquery})`)
          }
        },
        include: [
          {
            model: Employee,
            as: 'employee',
            attributes: ['id', 'employeeId', 'firstName', 'lastName', 'contactEmail', 'profileImage', 'status'],
            where: employeeWhere,
            required: true
          },
          {
            model: BusinessUnit,
            as: 'businessUnit',
            attributes: ['id', 'name', 'code']
          }
        ],
        order: [[sortBy, sortOrder.toUpperCase()]],
        // limit: parseInt(limit),
        // offset: offset,
        replacements
      });

      return {
        locations: rows,
        // pagination: {
        //   currentPage: parseInt(page),
        //   totalPages,
        //   totalItems: totalCount,
        //   itemsPerPage: totalCount,
        //   hasNextPage: page < totalPages,
        //   hasPreviousPage: page > 1
        // }
      };

    } catch (error) {
      logger.error('Error getting current employee locations:', error);
      throw error;
    }
  }

  /**
   * Get location history for a specific employee
   * @param {number} employeeId - Employee ID
   * @param {Object} filters - Filter options
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Paginated location history
   */
  async getEmployeeLocationHistory(employeeId, filters, tenantContext) {
    try {
      // Verify employee exists and user has access
      const employee = await Employee.findOne({
        where: {
          id: employeeId,
          companyId: tenantContext.companyId,
          status: 'active'
        },
        include: [
          {
            model: BusinessUnit,
            as: 'businessUnit',
            attributes: ['id', 'name', 'code']
          }
        ]
      });

      if (!employee) {
        throw new NotFoundError('Employee not found');
      }

      // // Check business unit access
      // if (tenantContext.businessUnitId &&
      //     employee.business_unit_id !== tenantContext.businessUnitId) {
      //   throw new ForbiddenError('Access denied to employee data');
      // }

      const {
        startDate,
        endDate,
        locationType,
        page = 1,
        limit = 50,
        sortBy = 'timestamp',
        sortOrder = 'desc'
      } = filters;

      // Build where conditions using Sequelize model field names
      const whereConditions = {
        employeeId: employeeId,
        companyId: tenantContext.companyId,
        status: 'active'
      };

      // Apply date range filter
      if (startDate || endDate) {
        whereConditions.timestamp = {};
        if (startDate) {
          // Start of the day (00:00:00)
          const startDateTime = new Date(startDate);
          startDateTime.setHours(0, 0, 0, 0);
          whereConditions.timestamp[Op.gte] = startDateTime;
        }
        if (endDate) {
          // End of the day (23:59:59.999)
          const endDateTime = new Date(endDate);
          endDateTime.setHours(23, 59, 59, 999);
          whereConditions.timestamp[Op.lte] = endDateTime;
        }
      }

      // Apply location type filter
      if (locationType) {
        whereConditions.locationType = locationType;
      }

      // Calculate offset
      const offset = (page - 1) * limit;



      // Get location history
      const { count, rows } = await EmployeeLocation.findAndCountAll({
        where: whereConditions,
        include: [
          {
            model: Employee,
            as: 'employee',
            attributes: ['id', 'employeeId', 'firstName', 'lastName', 'contactEmail', 'profileImage']
          },
          {
            model: BusinessUnit,
            as: 'businessUnit',
            attributes: ['id', 'name', 'code']
          }
        ],
        order: [[sortBy, sortOrder.toUpperCase()]],
        limit: parseInt(limit),
        offset: offset
      });



      // Calculate pagination
      const totalPages = Math.ceil(count / limit);

      return {
        employee: {
          id: employee.id,
          employeeId: employee.employeeId,
          firstName: employee.firstName,
          lastName: employee.lastName,
          fullName: `${employee.firstName} ${employee.lastName}`,
          contactEmail: employee.contactEmail,
          profileImage: employee.profileImage,
          businessUnit: employee.businessUnit ? {
            id: employee.businessUnit.id,
            name: employee.businessUnit.name,
            code: employee.businessUnit.code
          } : null
        },
        locations: rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: count,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        }
      };

    } catch (error) {
      logger.error('Error getting employee location history:', error);
      throw error;
    }
  }

  /**
   * Get latest location for a specific employee
   * @param {number} employeeId - Employee ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object|null>} Latest location record
   */
  async getEmployeeLatestLocation(employeeId, tenantContext) {
    try {
      const location = await EmployeeLocation.findOne({
        where: {
          employeeId: employeeId,
          companyId: tenantContext.companyId,
          status: 'active'
        },
        include: [
          {
            model: Employee,
            as: 'employee',
            attributes: ['id', 'employeeId', 'firstName', 'lastName', 'contactEmail', 'profileImage']
          },
          {
            model: BusinessUnit,
            as: 'businessUnit',
            attributes: ['id', 'name', 'code']
          }
        ],
        order: [['timestamp', 'DESC']]
      });

      return location;
    } catch (error) {
      logger.error('Error getting employee latest location:', error);
      throw error;
    }
  }

  /**
   * Validate business unit access for employee
   * @param {number} employeeId - Employee ID
   * @param {number} businessUnitId - Business unit ID
   * @param {number} companyId - Company ID
   * @returns {Promise<boolean>} Access validation result
   */
  async validateBusinessUnitAccess(employeeId, businessUnitId, companyId) {
    try {
      // Check if business unit exists and belongs to company
      const businessUnit = await BusinessUnit.findOne({
        where: {
          id: businessUnitId,
          companyId: companyId,
          status: 'active'
        }
      });

      if (!businessUnit) {
        return false;
      }

      // For now, allow access if business unit exists
      // This can be extended with more complex permission logic
      return true;
    } catch (error) {
      logger.error('Error validating business unit access:', error);
      return false;
    }
  }
}

module.exports = new LocationService();
