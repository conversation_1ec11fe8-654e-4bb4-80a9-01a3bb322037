'use strict';

const cron = require('node-cron');
const moment = require('moment-timezone');
const {
  Company,
  BusinessUnit,
  ShiftType,
  ShiftTypeAssignment,
  Employee,
  Attendance
} = require('../../data/models');
const attendanceService = require('./attendanceService');
const ShiftAutoCheckoutUtils = require('./shiftAutoCheckoutUtils');
const autoCheckoutEmailService = require('./autoCheckoutEmailService');
const logger = require('../../common/logging');
const { Op } = require('sequelize');

/**
 * Optimized Auto Checkout Cron Service
 * 
 * This service uses 3 consolidated cron jobs instead of individual jobs per shift:
 * 1. Morning shifts (6 AM - 2 PM) → Processed at 5 PM daily
 * 2. Evening shifts (2 PM - 10 PM) → Processed at 1 AM daily  
 * 3. Night shifts (10 PM - 6 AM) → Processed at 11 AM daily
 * 
 * This approach scales better for thousands of business units and shifts.
 */
class OptimizedAutoCheckoutCronService {
  constructor() {
    this.consolidatedJobs = new Map(); // Map to store consolidated cron jobs
    this.isInitialized = false;
    this.jobStats = {
      totalConsolidatedJobs: 3, // Fixed number of consolidated jobs
      activeJobs: 0,
      totalCheckouts: 0,
      lastInitialization: null,
      errors: [],
      shiftsProcessed: 0
    };
  }

  /**
   * Initialize the consolidated auto-checkout cron service
   */
  async initialize() {
    try {
      logger.info('🚀 Initializing Optimized Auto Checkout Cron Service...');
      
      // Clear existing jobs
      await this.clearAllJobs();
      
      // Create 3 consolidated cron jobs
      await this.createConsolidatedCronJobs();

      this.isInitialized = true;
      this.jobStats.lastInitialization = new Date();
      this.jobStats.activeJobs = this.consolidatedJobs.size;

      logger.info(`✅ Optimized Auto Checkout Service initialized with ${this.jobStats.activeJobs} consolidated jobs`);
      return {
        success: true,
        totalJobsCreated: this.jobStats.activeJobs,
        message: 'Optimized auto-checkout service initialized successfully'
      };

    } catch (error) {
      logger.error('❌ Error initializing optimized auto-checkout service:', error);
      this.jobStats.errors.push({
        timestamp: new Date(),
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Create 3 consolidated cron jobs for different shift time ranges
   */
  async createConsolidatedCronJobs() {
    const consolidatedSchedules = [
      {
        name: 'morning_shifts',
        description: 'Morning shifts (6 AM - 2 PM) - Multiple runs during grace period',
        cronExpression: '0 9,11,13 * * *', // 9 AM, 11 AM, 1 PM UTC = 2:30 PM, 4:30 PM, 6:30 PM IST
        shiftTimeRange: { start: '06:00:00', end: '14:00:00' },
        gracePeriodHours: 5
      },
      {
        name: 'evening_shifts',
        description: 'Evening shifts (2 PM - 10 PM) - Multiple runs during grace period',
        cronExpression: '0 17,19,21 * * *', // 5 PM, 7 PM, 9 PM UTC = 10:30 PM, 12:30 AM, 2:30 AM IST
        shiftTimeRange: { start: '14:00:00', end: '22:00:00' },
        gracePeriodHours: 5
      },
      {
        name: 'night_shifts',
        description: 'Night shifts (10 PM - 6 AM) - Multiple runs during grace period',
        cronExpression: '0 1,3,5 * * *', // 1 AM, 3 AM, 5 AM UTC = 6:30 AM, 8:30 AM, 10:30 AM IST
        shiftTimeRange: { start: '22:00:00', end: '06:00:00' },
        gracePeriodHours: 5
      }
    ];

    for (const schedule of consolidatedSchedules) {
      await this.createConsolidatedCronJob(schedule);
    }
  }

  /**
   * Create a single consolidated cron job for a specific shift time range
   */
  async createConsolidatedCronJob(schedule) {
    try {
      logger.info(`⏰ Creating consolidated cron job: ${schedule.name}`);
      logger.info(`   Description: ${schedule.description}`);
      logger.info(`   Cron expression: ${schedule.cronExpression} (UTC)`);
      logger.info(`   Shift time range: ${schedule.shiftTimeRange.start} - ${schedule.shiftTimeRange.end}`);

      // Create the consolidated cron job
      const cronJob = cron.schedule(schedule.cronExpression, async () => {
        const currentTimeUTC = moment().utc().format('YYYY-MM-DD HH:mm:ss');
        logger.info(`🔔 Consolidated auto-checkout cron triggered: ${schedule.name}`);
        logger.info(`   Current time UTC: ${currentTimeUTC}`);
        logger.info(`   Processing: ${schedule.description}`);
        
        // Process all shifts in this time range
        await this.processShiftsInTimeRange(schedule);
      }, {
        scheduled: false, // Start as false, then explicitly start
        timezone: 'UTC' // Always use UTC for consistency
      });

      // Start the job
      cronJob.start();

      // Store the job reference
      this.consolidatedJobs.set(schedule.name, {
        cronJob,
        schedule,
        createdAt: new Date(),
        isActive: true
      });

      logger.info(`✅ Consolidated cron job created and started: ${schedule.name}`);

    } catch (error) {
      logger.error(`❌ Error creating consolidated cron job ${schedule.name}:`, error);
      throw error;
    }
  }

  /**
   * Process all shifts that fall within a specific time range
   */
  async processShiftsInTimeRange(schedule) {
    try {
      const startTime = Date.now();
      logger.info(`🔄 Processing shifts in time range: ${schedule.shiftTimeRange.start} - ${schedule.shiftTimeRange.end}`);

      // Get all active companies and business units
      const companies = await Company.findAll({
        where: { status: 'active' },
        include: [{
          model: BusinessUnit,
          as: 'businessUnits',
          where: { status: 'active' },
          required: false
        }]
      });

      let totalShiftsProcessed = 0;
      let totalEmployeesProcessed = 0;
      let totalSuccessfulCheckouts = 0;
      const errors = [];

      // Process each company and business unit
      for (const company of companies) {
        for (const businessUnit of company.businessUnits) {
          try {
            const result = await this.processBusinessUnitShiftsInRange(
              company, 
              businessUnit, 
              schedule
            );
            
            totalShiftsProcessed += result.shiftsProcessed;
            totalEmployeesProcessed += result.employeesProcessed;
            totalSuccessfulCheckouts += result.successfulCheckouts;
            errors.push(...result.errors);

          } catch (error) {
            logger.error(`❌ Error processing business unit ${businessUnit.name}:`, error);
            errors.push({
              businessUnitId: businessUnit.id,
              businessUnitName: businessUnit.name,
              error: error.message
            });
          }
        }
      }

      const processingTime = Date.now() - startTime;
      
      logger.info(`✅ Consolidated processing completed for ${schedule.name}:`);
      logger.info(`   Shifts processed: ${totalShiftsProcessed}`);
      logger.info(`   Employees processed: ${totalEmployeesProcessed}`);
      logger.info(`   Successful checkouts: ${totalSuccessfulCheckouts}`);
      logger.info(`   Errors: ${errors.length}`);
      logger.info(`   Processing time: ${processingTime}ms`);

      // Update stats
      this.jobStats.shiftsProcessed += totalShiftsProcessed;
      this.jobStats.totalCheckouts += totalSuccessfulCheckouts;

      if (errors.length > 0) {
        this.jobStats.errors.push(...errors.slice(0, 10)); // Keep only last 10 errors
      }

      return {
        success: true,
        shiftsProcessed: totalShiftsProcessed,
        employeesProcessed: totalEmployeesProcessed,
        successfulCheckouts: totalSuccessfulCheckouts,
        errors,
        processingTime
      };

    } catch (error) {
      logger.error(`❌ Error in consolidated processing for ${schedule.name}:`, error);
      throw error;
    }
  }

  /**
   * Clear all existing cron jobs
   */
  async clearAllJobs() {
    try {
      logger.info('🧹 Clearing existing consolidated cron jobs...');
      
      for (const [jobName, jobData] of this.consolidatedJobs) {
        if (jobData.cronJob) {
          jobData.cronJob.stop();
          jobData.cronJob.destroy();
        }
      }
      
      this.consolidatedJobs.clear();
      this.jobStats.activeJobs = 0;
      
      logger.info('✅ All existing consolidated jobs cleared');
    } catch (error) {
      logger.error('❌ Error clearing consolidated jobs:', error);
      throw error;
    }
  }

  /**
   * Process shifts in a specific business unit that fall within the time range
   */
  async processBusinessUnitShiftsInRange(company, businessUnit, schedule) {
    try {
      // Get shifts that fall within the time range
      const shiftsInRange = await this.getShiftsInTimeRange(
        company.id,
        businessUnit.id,
        schedule.shiftTimeRange
      );

      let shiftsProcessed = 0;
      let employeesProcessed = 0;
      let successfulCheckouts = 0;
      const errors = [];

      for (const shiftType of shiftsInRange) {
        try {
          // Get timezone for this business unit
          const timezone = ShiftAutoCheckoutUtils.getBusinessUnitTimezone(businessUnit);

          // Check if this shift should be processed (grace period check)
          const shouldProcess = this.shouldProcessShift(shiftType, schedule, timezone);

          logger.info(`🔍 Shift ${shiftType.name} (${shiftType.startTime}-${shiftType.endTime}) - Should process: ${shouldProcess}`);

          if (shouldProcess) {
            logger.info(`🚀 Processing auto-checkout for shift: ${shiftType.name}`);

            const result = await this.processShiftAutoCheckout(
              company,
              businessUnit,
              shiftType,
              timezone
            );

            shiftsProcessed++;
            employeesProcessed += result.employeesProcessed;
            successfulCheckouts += result.successfulCheckouts;
            errors.push(...result.errors);

            logger.info(`✅ Shift ${shiftType.name} processed: ${result.employeesProcessed} employees, ${result.successfulCheckouts} checkouts`);
          } else {
            logger.info(`⏭️ Skipping shift ${shiftType.name} - not within processing window`);
          }

        } catch (error) {
          logger.error(`❌ Error processing shift ${shiftType.name}:`, error);
          errors.push({
            shiftTypeId: shiftType.id,
            shiftTypeName: shiftType.name,
            error: error.message
          });
        }
      }

      return {
        shiftsProcessed,
        employeesProcessed,
        successfulCheckouts,
        errors
      };

    } catch (error) {
      logger.error(`❌ Error processing business unit shifts:`, error);
      throw error;
    }
  }

  /**
   * Get shifts that fall within a specific time range
   * IMPROVED: Better logic for filtering shifts within time ranges
   */
  async getShiftsInTimeRange(companyId, businessUnitId, timeRange) {
    try {
      const whereCondition = {
        companyId,
        businessUnitId,
        isActive: true
      };

      // IMPROVED: More flexible time range matching
      // Instead of strict time range matching, we'll get all shifts and filter them
      // This is more reliable for edge cases and timezone handling

      logger.debug(`🔍 Getting shifts in time range ${timeRange.start} - ${timeRange.end} for BU ${businessUnitId}`);

      // Get all active shifts for this business unit
      // We'll filter them in the processing logic based on actual shift end times and grace periods

      const shifts = await ShiftType.findAll({
        where: whereCondition,
        include: [{
          model: ShiftTypeAssignment,
          as: 'assignments',
          where: { isActive: true },
          required: false
        }]
      });

      // IMPROVED: Check if shifts have any kind of employee assignments
      const shiftsWithEmployees = [];

      for (const shift of shifts) {
        // Check if shift has any employees assigned (direct, assignment-based, or department-based)
        const hasEmployees = await ShiftAutoCheckoutUtils.hasAssignedEmployees(
          companyId,
          businessUnitId,
          shift.id
        );

        if (hasEmployees) {
          shiftsWithEmployees.push(shift);
        }
      }

      logger.debug(`📊 Found ${shiftsWithEmployees.length} shifts with assigned employees out of ${shifts.length} total shifts`);

      return shiftsWithEmployees;

    } catch (error) {
      logger.error('❌ Error getting shifts in time range:', error);
      throw error;
    }
  }

  /**
   * Check if a shift should be processed based on grace period
   * IMPROVED: More flexible logic that checks multiple possible shift end times
   */
  shouldProcessShift(shiftType, schedule, timezone) {
    try {
      const now = moment().tz(timezone);
      const today = now.format('YYYY-MM-DD');
      const yesterday = moment().tz(timezone).subtract(1, 'day').format('YYYY-MM-DD');

      // Handle overnight shifts differently
      const isOvernightShift = ShiftAutoCheckoutUtils.isOvernightShift(
        shiftType.startTime,
        shiftType.endTime
      );

      // Check multiple possible shift end times to be more flexible
      const possibleShiftEndTimes = [];

      if (isOvernightShift) {
        // For overnight shifts, check both today's end time and yesterday's end time
        possibleShiftEndTimes.push(moment.tz(`${today} ${shiftType.endTime}`, timezone));
        possibleShiftEndTimes.push(moment.tz(`${yesterday} ${shiftType.endTime}`, timezone).add(1, 'day'));
      } else {
        // For regular shifts, check today's and yesterday's end times
        possibleShiftEndTimes.push(moment.tz(`${today} ${shiftType.endTime}`, timezone));
        possibleShiftEndTimes.push(moment.tz(`${yesterday} ${shiftType.endTime}`, timezone));
      }

      // Check if any of the possible shift end times should be processed
      let shouldProcess = false;
      let selectedShiftEnd = null;

      for (const shiftEndDateTime of possibleShiftEndTimes) {
        const graceEndTime = shiftEndDateTime.clone().add(schedule.gracePeriodHours, 'hours');
        const shiftHasEnded = now.isAfter(shiftEndDateTime);
        const withinGracePeriod = now.isBefore(graceEndTime);

        if (shiftHasEnded && withinGracePeriod) {
          shouldProcess = true;
          selectedShiftEnd = shiftEndDateTime;
          break;
        }
      }

      if (shouldProcess && selectedShiftEnd) {
        const graceEndTime = selectedShiftEnd.clone().add(schedule.gracePeriodHours, 'hours');

        logger.debug(`🔍 Shift processing check for ${shiftType.name}:`);
        logger.debug(`   Current time: ${now.format('YYYY-MM-DD HH:mm:ss')} (${timezone})`);
        logger.debug(`   Selected shift end: ${selectedShiftEnd.format('YYYY-MM-DD HH:mm:ss')} (${timezone})`);
        logger.debug(`   Grace end: ${graceEndTime.format('YYYY-MM-DD HH:mm:ss')} (${timezone})`);
        logger.debug(`   Should process: ✅ YES`);
      } else {
        logger.debug(`🔍 Shift processing check for ${shiftType.name}: ❌ NO (not in grace period)`);
      }

      return shouldProcess;

    } catch (error) {
      logger.error('❌ Error checking if shift should be processed:', error);
      return false;
    }
  }

  /**
   * Process auto-checkout for a specific shift
   */
  async processShiftAutoCheckout(company, businessUnit, shiftType, timezone) {
    try {
      // Get employees for this shift
      const employees = await ShiftAutoCheckoutUtils.getEmployeesForShift(
        company.id,
        businessUnit.id,
        shiftType.id
      );

      let employeesProcessed = 0;
      let successfulCheckouts = 0;
      const errors = [];

      // Determine if this is an overnight shift
      const isOvernightShift = ShiftAutoCheckoutUtils.isOvernightShift(
        shiftType.startTime,
        shiftType.endTime
      );

      // Calculate attendance dates to check
      const attendanceDates = ShiftAutoCheckoutUtils.getAttendanceDatesToCheck(
        timezone,
        isOvernightShift
      );

      for (const employee of employees) {
        try {
          const result = await this.processEmployeeAutoCheckout(
            employee,
            company,
            businessUnit,
            shiftType,
            timezone,
            attendanceDates,
            isOvernightShift
          );

          employeesProcessed++;
          if (result.success) {
            successfulCheckouts++;
          }

        } catch (error) {
          employeesProcessed++;
          errors.push({
            employeeId: employee.id,
            employeeCode: employee.employeeId,
            error: error.message
          });
        }
      }

      return {
        employeesProcessed,
        successfulCheckouts,
        errors
      };

    } catch (error) {
      logger.error('❌ Error processing shift auto-checkout:', error);
      throw error;
    }
  }

  /**
   * Process auto-checkout for a specific employee
   */
  async processEmployeeAutoCheckout(employee, company, businessUnit, shiftType, timezone, attendanceDates, isOvernightShift) {
    try {
      // Find active attendance record (checked in but not checked out)
      const activeAttendance = await Attendance.findOne({
        where: {
          companyId: company.id,
          employeeId: employee.id,
          date: { [Op.in]: attendanceDates },
          clockInStatus: 'CHECKIN',
          clockOutTime: null
        },
        order: [['clockInTime', 'DESC']] // Get the most recent check-in
      });

      if (!activeAttendance) {
        return {
          success: false,
          reason: 'No active check-in found (clockInStatus=CHECKIN and clockOutTime=null)'
        };
      }

      // Perform auto-checkout
      await this.performAutoCheckout(
        employee,
        activeAttendance,
        company,
        businessUnit,
        timezone,
        shiftType,
        isOvernightShift
      );

      return {
        success: true,
        attendanceDate: activeAttendance.date,
        checkInTime: activeAttendance.clockInTime
      };

    } catch (error) {
      logger.error(`❌ Error processing employee auto-checkout for ${employee.employeeId}:`, error);
      throw error;
    }
  }

  /**
   * Perform auto-checkout for an employee
   */
  async performAutoCheckout(employee, attendance, company, businessUnit, timezone, shiftType, isOvernightShift) {
    try {
      // Calculate the checkout time: shift end time - 2 hours
      const checkoutTime = this.calculateCheckoutTime(attendance, shiftType, timezone, isOvernightShift);

      // Calculate work hours
      const checkInTime = moment(attendance.clockInTime);
      const workHours = checkoutTime.diff(checkInTime, 'hours', true);
      const workHoursRounded = Math.round(workHours * 100) / 100; // Round to 2 decimal places

      // Auto-checkout location
      const autoCheckoutLocation = `Auto-checkout: ${businessUnit.name} - ${shiftType.name}`;

      // Use transaction for data consistency
      const transaction = await require('../../data/models').sequelize.transaction();

      try {
        // Update attendance record with all required fields
        const updateResult = await Attendance.update({
          clockOutTime: checkoutTime.toDate(),
          clockOutNote: `Automatic checkout - System generated (shift end - 2 hours)${isOvernightShift ? ' [Overnight Shift]' : ''}`,
          clockInStatus: 'CHECKOUT', // Set to CHECKOUT as required
          clockOutLocation: autoCheckoutLocation,
          workHours: workHoursRounded,
          overtimeHours: 0, // No overtime for auto-checkout
          modeOfCheckout: 'WFO',
          isManualEntry: false, // System-generated checkout
          updatedById: null // System update
        }, {
          where: {
            id: attendance.id,
            companyId: company.id,
            clockInStatus: 'CHECKIN', // Ensure we only update CHECKIN records
            clockOutTime: null // Ensure we only update records without checkout
          },
          transaction
        });

        if (updateResult[0] === 0) {
          throw new Error('No attendance record was updated - may have been already processed');
        }

        await transaction.commit();

        logger.info(`✅ Auto-checkout completed for employee ${employee.employeeId}`);
        logger.info(`   Check-in: ${checkInTime.format('YYYY-MM-DD HH:mm:ss')}`);
        logger.info(`   Check-out: ${checkoutTime.format('YYYY-MM-DD HH:mm:ss')}`);
        logger.info(`   Work hours: ${workHoursRounded}`);

        // Send email notification to employee
        try {
          // Get updated attendance record with all data
          const updatedAttendance = await Attendance.findByPk(attendance.id);

          // Prepare email data
          const emailData = {
            employee: {
              firstName: employee.firstName,
              lastName: employee.lastName,
              employeeId: employee.employeeId || employee.id,
              contactEmail: employee.contactEmail,
              user: employee.user // Include user association for email fallback
            },
            attendance: {
              date: updatedAttendance.date,
              clockInTime: updatedAttendance.clockInTime,
              clockOutTime: updatedAttendance.clockOutTime,
              workHours: updatedAttendance.workHours,
              overtimeHours: updatedAttendance.overtimeHours,
              modeOfWork: updatedAttendance.modeOfWork
            },
            shift: shiftType,
            company: company,
            businessUnit: businessUnit
          };

          // Send email notification
          await autoCheckoutEmailService.sendAutoCheckoutNotification(
            emailData.employee,
            emailData.attendance,
            emailData.shift,
            emailData.company,
            emailData.businessUnit
          );

        } catch (emailError) {
          // Don't fail the auto-checkout if email fails
          logger.error(`⚠️ Failed to send auto-checkout email to ${employee.employeeId}:`, emailError);
        }

      } catch (error) {
        await transaction.rollback();
        throw error;
      }

    } catch (error) {
      logger.error(`❌ Error performing auto-checkout for employee ${employee.employeeId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate checkout time (shift end - 2 hours)
   */
  calculateCheckoutTime(attendance, shiftType, timezone, isOvernightShift) {
    try {
      const attendanceDate = moment(attendance.date).format('YYYY-MM-DD');
      let shiftEndDateTime;

      if (isOvernightShift) {
        // For overnight shifts, end time is next day
        const nextDay = moment(attendanceDate).add(1, 'day').format('YYYY-MM-DD');
        shiftEndDateTime = moment.tz(`${nextDay} ${shiftType.endTime}`, timezone);
      } else {
        // Regular shift, same day
        shiftEndDateTime = moment.tz(`${attendanceDate} ${shiftType.endTime}`, timezone);
      }

      // Calculate checkout time: shift end - 2 hours
      const checkoutTime = shiftEndDateTime.subtract(2, 'hours');

      return checkoutTime;

    } catch (error) {
      logger.error('❌ Error calculating checkout time:', error);
      throw error;
    }
  }

  /**
   * Manually trigger auto-checkout for a specific shift
   * @param {number} companyId - Company ID
   * @param {number} businessUnitId - Business unit ID
   * @param {number} shiftTypeId - Shift type ID
   * @returns {Object} Processing result
   */
  async manualTrigger(companyId, businessUnitId, shiftTypeId) {
    try {
      logger.info(`🚀 Manual auto-checkout trigger for shift ${shiftTypeId} in BU ${businessUnitId}`);

      // Get company and business unit
      const company = await Company.findByPk(companyId);
      const businessUnit = await BusinessUnit.findByPk(businessUnitId);
      const shiftType = await ShiftType.findByPk(shiftTypeId);

      if (!company || !businessUnit || !shiftType) {
        throw new Error('Company, Business Unit, or Shift Type not found');
      }

      // Get timezone
      const timezone = ShiftAutoCheckoutUtils.getBusinessUnitTimezone(businessUnit);

      // Process auto-checkout for this specific shift
      const result = await this.processShiftAutoCheckout(
        company,
        businessUnit,
        shiftType,
        timezone
      );

      logger.info(`✅ Manual trigger completed: ${result.employeesProcessed} employees processed, ${result.successfulCheckouts} checkouts`);

      return {
        success: true,
        companyId,
        businessUnitId,
        shiftTypeId,
        shiftName: shiftType.name,
        timezone,
        ...result
      };

    } catch (error) {
      logger.error(`❌ Manual trigger failed for shift ${shiftTypeId}:`, error);
      throw error;
    }
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      isInitialized: this.isInitialized,
      consolidatedJobs: Array.from(this.consolidatedJobs.keys()),
      jobStats: this.jobStats,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage()
    };
  }

  /**
   * Shutdown the service
   */
  async shutdown() {
    try {
      logger.info('🛑 Shutting down Optimized Auto Checkout Cron Service...');
      await this.clearAllJobs();
      this.isInitialized = false;
      logger.info('✅ Optimized Auto Checkout Cron Service shutdown completed');
    } catch (error) {
      logger.error('❌ Error during shutdown:', error);
      throw error;
    }
  }
}

module.exports = new OptimizedAutoCheckoutCronService();
