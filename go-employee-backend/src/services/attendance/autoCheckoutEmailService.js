const ejs = require('ejs');
const path = require('path');
const emailService = require('../email/emailService');
const logger = require('../../common/logging');
const moment = require('moment-timezone');

/**
 * Auto-Checkout Email Service
 * Sends email notifications to employees when they are auto-checked out
 */
class AutoCheckoutEmailService {
  constructor() {
    this.templatePath = path.join(__dirname, '../../views/emails/auto-checkout-notification.ejs');
  }

  /**
   * Send auto-checkout notification email to employee
   * @param {Object} employeeData - Employee information
   * @param {Object} attendanceData - Attendance record
   * @param {Object} shiftData - Shift information
   * @param {Object} companyData - Company information
   * @param {Object} businessUnitData - Business unit information
   */
  async sendAutoCheckoutNotification(employeeData, attendanceData, shiftData, companyData, businessUnitData) {
    try {
      // Validate required data - check both contactEmail and user.email
      const employeeEmail = employeeData?.contactEmail || employeeData?.user?.email || employeeData?.email;
      if (!employeeEmail) {
        logger.warn(`No email found for employee ${employeeData?.employeeId || 'unknown'}`);
        return false;
      }

      // Format attendance data for email
      const formattedAttendance = this.formatAttendanceData(attendanceData);
      
      // Prepare template data
      const templateData = {
        employee: {
          firstName: employeeData.firstName || 'Employee',
          lastName: employeeData.lastName || '',
          employeeId: employeeData.employeeId || employeeData.id,
          email: employeeEmail
        },
        attendance: formattedAttendance,
        shiftType: {
          name: shiftData.name || 'Standard Shift',
          startTime: this.formatTime(shiftData.startTime),
          endTime: this.formatTime(shiftData.endTime)
        },
        company: {
          name: companyData.name || 'Company'
        },
        businessUnit: {
          name: businessUnitData.name || 'Business Unit'
        }
      };

      // Render email template
      const emailContent = await ejs.renderFile(this.templatePath, templateData);

      // Prepare email subject
      const subject = `Auto-Checkout Notification - ${formattedAttendance.date}`;

      // Send email
      await emailService.sendEmail({
        to: employeeEmail,
        subject: subject,
        html: emailContent
      });

      logger.info(`✅ Auto-checkout email sent to: ${employeeEmail} (Employee: ${employeeData.employeeId})`);
      return true;

    } catch (error) {
      logger.error(`❌ Error sending auto-checkout email to ${employeeEmail}:`, error);
      return false;
    }
  }

  /**
   * Send bulk auto-checkout notifications
   * @param {Array} notifications - Array of notification data
   */
  async sendBulkNotifications(notifications) {
    const results = {
      sent: 0,
      failed: 0,
      errors: []
    };

    for (const notification of notifications) {
      try {
        const success = await this.sendAutoCheckoutNotification(
          notification.employee,
          notification.attendance,
          notification.shift,
          notification.company,
          notification.businessUnit
        );

        if (success) {
          results.sent++;
        } else {
          results.failed++;
        }
      } catch (error) {
        results.failed++;
        results.errors.push({
          employeeId: notification.employee?.employeeId,
          error: error.message
        });
      }
    }

    logger.info(`📧 Bulk auto-checkout emails: ${results.sent} sent, ${results.failed} failed`);
    return results;
  }

  /**
   * Format attendance data for email display
   * @param {Object} attendance - Raw attendance data
   * @returns {Object} Formatted attendance data
   */
  formatAttendanceData(attendance) {
    return {
      date: moment(attendance.date).format('DD MMM YYYY'),
      clockInTime: this.formatDateTime(attendance.clockInTime),
      clockOutTime: this.formatDateTime(attendance.clockOutTime),
      workHours: this.formatWorkHours(attendance.workHours),
      overtimeHours: attendance.overtimeHours ? this.formatWorkHours(attendance.overtimeHours) : null,
      modeOfWork: attendance.modeOfWork || 'WFO'
    };
  }

  /**
   * Format time for display (HH:mm format)
   * @param {string} time - Time string
   * @returns {string} Formatted time
   */
  formatTime(time) {
    try {
      if (!time) return 'N/A';
      return moment(time, 'HH:mm:ss').format('HH:mm');
    } catch (error) {
      return time || 'N/A';
    }
  }

  /**
   * Format datetime for display
   * @param {string|Date} datetime - Datetime
   * @returns {string} Formatted datetime
   */
  formatDateTime(datetime) {
    try {
      if (!datetime) return 'N/A';
      return moment(datetime).format('DD MMM YYYY, HH:mm');
    } catch (error) {
      return datetime || 'N/A';
    }
  }

  /**
   * Format work hours for display
   * @param {number} hours - Work hours
   * @returns {string} Formatted work hours
   */
  formatWorkHours(hours) {
    try {
      if (hours === null || hours === undefined) return '0.00';
      return parseFloat(hours).toFixed(2);
    } catch (error) {
      return '0.00';
    }
  }

  /**
   * Get email statistics
   * @returns {Object} Email service statistics
   */
  getStats() {
    return {
      templatePath: this.templatePath,
      templateExists: require('fs').existsSync(this.templatePath),
      lastProcessed: new Date().toISOString()
    };
  }
}

module.exports = new AutoCheckoutEmailService();
