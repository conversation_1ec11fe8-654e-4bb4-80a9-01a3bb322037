'use strict';

const cron = require('node-cron');
const shiftSwapService = require('../rota/shiftSwapService');

/**
 * Shift Swap Expiration Cron Job
 *
 * Runs every hour to:
 * 1. Expire overdue swap requests
 * 2. Send reminder notifications for upcoming deadlines
 */
class ShiftSwapExpirationCron {
  constructor() {
    this.shiftSwapService = shiftSwapService;
    this.isRunning = false;
  }

  /**
   * Start the cron job
   */
  start() {
    console.log('🚀 Starting Shift Swap Expiration Cron Job...');
    
    // Run every hour at minute 15
    this.cronJob = cron.schedule('15 * * * *', async () => {
      if (this.isRunning) {
        console.log('⏳ Shift swap expiration job already running, skipping...');
        return;
      }

      this.isRunning = true;
      
      try {
        console.log(`🔄 [${new Date().toISOString()}] Running shift swap expiration job...`);
        
        // 1. Expire overdue requests
        const expirationResults = await this.shiftSwapService.expireOverdueSwapRequests();
        
        const expiredCount = expirationResults.filter(r => r.success).length;
        const failedCount = expirationResults.filter(r => !r.success).length;
        
        console.log(`✅ Expired ${expiredCount} overdue swap requests, ${failedCount} failed`);
        
        // 2. Send reminder notifications (every 4 hours)
        const currentHour = new Date().getHours();
        if (currentHour % 4 === 0) {
          console.log('📧 Sending deadline reminder notifications...');
          
          // This would need to be enhanced to iterate through all companies
          // For now, we'll skip reminders in the global cron
          console.log('⚠️ Reminder notifications require company-specific context - skipping in global cron');
        }
        
        console.log(`✅ [${new Date().toISOString()}] Shift swap expiration job completed`);
        
      } catch (error) {
        console.error(`❌ [${new Date().toISOString()}] Error in shift swap expiration job:`, error);
      } finally {
        this.isRunning = false;
      }
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.cronJob.start();
    console.log('✅ Shift Swap Expiration Cron Job started successfully');
  }

  /**
   * Stop the cron job
   */
  stop() {
    if (this.cronJob) {
      this.cronJob.stop();
      console.log('🛑 Shift Swap Expiration Cron Job stopped');
    }
  }

  /**
   * Get cron job status
   */
  getStatus() {
    return {
      isScheduled: this.cronJob ? this.cronJob.scheduled : false,
      isRunning: this.isRunning,
      nextRun: this.cronJob ? 'Every hour at minute 15' : null
    };
  }

  /**
   * Run the job manually (for testing)
   */
  async runManually() {
    if (this.isRunning) {
      throw new Error('Job is already running');
    }

    this.isRunning = true;
    
    try {
      console.log('🔄 Running shift swap expiration job manually...');
      
      const expirationResults = await this.shiftSwapService.expireOverdueSwapRequests();
      
      console.log('✅ Manual shift swap expiration job completed');
      return {
        success: true,
        expiredCount: expirationResults.filter(r => r.success).length,
        failedCount: expirationResults.filter(r => !r.success).length,
        results: expirationResults
      };
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Run expiration for specific company (for testing)
   * @param {Object} tenantContext - Tenant context
   */
  async runForCompany(tenantContext) {
    try {
      console.log(`🔄 Running shift swap expiration for company ${tenantContext.companyId}...`);
      
      // 1. Expire overdue requests
      const expirationResults = await this.shiftSwapService.expireOverdueSwapRequests(tenantContext);
      
      // 2. Send reminder notifications
      const reminderResults = await this.shiftSwapService.sendDeadlineReminders(tenantContext);
      
      return {
        success: true,
        expiredCount: expirationResults.filter(r => r.success).length,
        reminderCount: reminderResults.filter(r => r.success).length,
        expirationResults,
        reminderResults
      };
    } catch (error) {
      console.error(`❌ Error running swap expiration for company ${tenantContext.companyId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = ShiftSwapExpirationCron;
