'use strict';

const cron = require('node-cron');
const ShiftCompletionService = require('../rota/shiftCompletionService');

/**
 * Shift Completion Cron Job
 * 
 * Runs every hour to auto-complete shifts that have passed their completion time
 * Grace period: 2 hours after shift end time
 */
class ShiftCompletionCron {
  constructor() {
    this.shiftCompletionService = new ShiftCompletionService();
    this.isRunning = false;
  }

  /**
   * Start the cron job
   */
  start() {
    console.log('🚀 Starting Shift Completion Cron Job...');
    
    // Run every hour at minute 0
    this.cronJob = cron.schedule('0 * * * *', async () => {
      if (this.isRunning) {
        console.log('⏳ Shift completion job already running, skipping...');
        return;
      }

      this.isRunning = true;
      
      try {
        console.log(`🔄 [${new Date().toISOString()}] Running shift completion job...`);
        
        const results = await this.shiftCompletionService.autoCompleteShifts();
        
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        
        console.log(`✅ [${new Date().toISOString()}] Shift completion job completed: ${successful} successful, ${failed} failed`);
        
        // Log any failures
        if (failed > 0) {
          const failures = results.filter(r => !r.success);
          console.error('❌ Failed completions:', failures);
        }
        
      } catch (error) {
        console.error(`❌ [${new Date().toISOString()}] Shift completion job failed:`, error);
      } finally {
        this.isRunning = false;
      }
    }, {
      scheduled: false, // Don't start immediately
      timezone: 'UTC' // Use UTC for consistency
    });

    this.cronJob.start();
    console.log('✅ Shift Completion Cron Job started (runs every hour)');
  }

  /**
   * Stop the cron job
   */
  stop() {
    if (this.cronJob) {
      this.cronJob.stop();
      console.log('🛑 Shift Completion Cron Job stopped');
    }
  }

  /**
   * Get cron job status
   */
  getStatus() {
    return {
      isScheduled: this.cronJob ? this.cronJob.scheduled : false,
      isRunning: this.isRunning,
      nextRun: this.cronJob ? this.cronJob.nextDate() : null
    };
  }

  /**
   * Run the job manually (for testing)
   */
  async runManually() {
    if (this.isRunning) {
      throw new Error('Job is already running');
    }

    this.isRunning = true;
    
    try {
      console.log('🔄 Running shift completion job manually...');
      const results = await this.shiftCompletionService.autoCompleteShifts();
      console.log('✅ Manual shift completion job completed');
      return results;
    } finally {
      this.isRunning = false;
    }
  }
}

module.exports = ShiftCompletionCron;
