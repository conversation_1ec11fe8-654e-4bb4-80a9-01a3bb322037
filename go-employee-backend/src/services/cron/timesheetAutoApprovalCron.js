'use strict';

const cron = require('node-cron');
const moment = require('moment-timezone');
const { UserTimesheet, Employee, Task, Project, Company, BusinessUnit, sequelize } = require('../../data/models');
const logger = require('../../common/logging');
const emailService = require('../email/emailService');
const timesheetApprovalService = require('../project/timesheetApprovalService');
const { Op, Sequelize } = require('sequelize');

/**
 * Timesheet Auto Approval Cron Service
 * Automatically approves yesterday's pending timesheets to ensure project calculations are not impacted
 */
class TimesheetAutoApprovalCron {
  constructor() {
    this.cronJob = null;
    this.isRunning = false;
    this.stats = {
      lastRun: null,
      totalProcessed: 0,
      totalApproved: 0,
      totalErrors: 0,
      errors: []
    };
  }

  /**
   * Start the cron job
   */
  start() {
    // Run daily at 2:00 AM UTC (7:30 AM IST) to process yesterday's timesheets
    this.cronJob = cron.schedule('0 2 * * *', async () => {
      if (this.isRunning) {
        logger.warn('⏳ Timesheet auto-approval job already running, skipping...');
        return;
      }

      this.isRunning = true;
      
      try {
        logger.info(`🔄 [${new Date().toISOString()}] Running timesheet auto-approval job...`);
        
        const results = await this.autoApproveYesterdayTimesheets();
        
        logger.info(`✅ [${new Date().toISOString()}] Timesheet auto-approval job completed:`);
        logger.info(`   Total Processed: ${results.totalProcessed}`);
        logger.info(`   Total Approved: ${results.totalApproved}`);
        logger.info(`   Total Errors: ${results.totalErrors}`);
        
        // Update stats
        this.stats.lastRun = new Date();
        this.stats.totalProcessed += results.totalProcessed;
        this.stats.totalApproved += results.totalApproved;
        this.stats.totalErrors += results.totalErrors;
        
        // Send summary email if there were approvals or errors
        if (results.totalApproved > 0 || results.totalErrors > 0) {
          await this.sendSummaryEmail(results);
        }
        
      } catch (error) {
        logger.error(`❌ [${new Date().toISOString()}] Timesheet auto-approval job failed:`, error);
        this.stats.totalErrors++;
        this.stats.errors.push({
          timestamp: new Date(),
          error: error.message
        });
      } finally {
        this.isRunning = false;
      }
    }, {
      scheduled: false, // Don't start immediately
      timezone: 'UTC' // Use UTC for consistency
    });

    this.cronJob.start();
    logger.info('✅ Timesheet Auto-Approval Cron Job started (runs daily at 2:00 AM UTC)');
  }

  /**
   * Stop the cron job
   */
  stop() {
    if (this.cronJob) {
      this.cronJob.stop();
      logger.info('🛑 Timesheet Auto-Approval Cron Job stopped');
    }
  }

  /**
   * Get cron job status
   */
  getStatus() {
    return {
      isScheduled: this.cronJob ? this.cronJob.scheduled : false,
      isRunning: this.isRunning,
      nextRun: this.cronJob ? 'Daily at 2:00 AM UTC' : null,
      stats: this.stats
    };
  }

  /**
   * Auto-approve yesterday's pending timesheets
   */
  async autoApproveYesterdayTimesheets() {
    const results = {
      totalProcessed: 0,
      totalApproved: 0,
      totalErrors: 0,
      approvedTimesheets: [],
      errors: []
    };

    try {
      // Calculate yesterday's date
      const yesterday = moment().subtract(1, 'day').format('YYYY-MM-DD');
      
      logger.info(`🔍 Processing timesheets for date: ${yesterday}`);

      // Find all pending timesheets from yesterday
      const pendingTimesheets = await UserTimesheet.findAll({
      where: {
          date: { [Op.lte]: '2025-07-01' },
          status: 'Pending',
          business_unit_id:10
        },
        include: [
          {
            model: Employee,
            as: 'employee',
            attributes: ['id', 'firstName', 'lastName', 'contactEmail', 'employeeId'],
            include: [
              {
                model: Company,
                as: 'company',
                attributes: ['id', 'name']
              },
              {
                model: BusinessUnit,
                as: 'businessUnit',
                attributes: ['id', 'name']
              }
            ]
          },
          {
            model: Task,
            as: 'task',
            attributes: ['id', 'taskName']
          },
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'project_name', 'approvalCode']
          }
        ]
      });

      logger.info(`📋 Found ${pendingTimesheets.length} pending timesheets for ${yesterday}`);

      if (pendingTimesheets.length === 0) {
        return results;
      }

      // Process each timesheet
      for (const timesheet of pendingTimesheets) {
        results.totalProcessed++;
        
        try {
          const approvalResult = await this.autoApproveTimesheet(timesheet);
          
          if (approvalResult.success) {
            results.totalApproved++;
            results.approvedTimesheets.push({
              id: timesheet.id,
              employeeId: timesheet.employee?.employeeId,
              employeeName: `${timesheet.employee?.firstName} ${timesheet.employee?.lastName}`,
              projectName: timesheet.project?.project_name,
              taskName: timesheet.task?.taskName,
              duration: timesheet.duration,
              date: timesheet.date
            });
            
            logger.info(`✅ Auto-approved timesheet ${timesheet.id} for employee ${timesheet.employee?.employeeId}`);
          } else {
            results.totalErrors++;
            results.errors.push({
              timesheetId: timesheet.id,
              employeeId: timesheet.employee?.employeeId,
              error: approvalResult.error
            });
            
            logger.error(`❌ Failed to auto-approve timesheet ${timesheet.id}: ${approvalResult.error}`);
          }
          
        } catch (error) {
          results.totalErrors++;
          results.errors.push({
            timesheetId: timesheet.id,
            employeeId: timesheet.employee?.employeeId,
            error: error.message
          });
          
          logger.error(`❌ Error processing timesheet ${timesheet.id}:`, error);
        }
      }

      return results;

    } catch (error) {
      logger.error('❌ Error in autoApproveYesterdayTimesheets:', error);
      throw error;
    }
  }

  /**
   * Auto-approve a single timesheet using the exact same logic as manual approval
   */
  async autoApproveTimesheet(timesheet) {
    try {
      // ✅ USE EXACT SAME LOGIC AS MANUAL APPROVAL - Just bypass approver check
      const tenantContext = {
        companyId: 5,
        businessUnitId: 10,
        userId: null, // System user
        employeeId: null // Will be set by autoApproveTimesheet method
      };
      // const transaction = await sequelize.transaction();

      const approvalData = {
        status: 'Approved',
        comment: 'Auto-approved by system (daily cron job)'
      };

      // ✅ CALL THE EXACT SAME METHOD AS MANUAL APPROVAL
      const result = await timesheetApprovalService.autoApproveTimesheet(
        timesheet.id,
        approvalData,
        tenantContext,
        // transaction
      );

      // Send notification email to employee
      setImmediate(async () => {
        try {
          // await this.sendApprovalNotificationEmail(timesheet);
        } catch (emailError) {
          logger.error('Error sending approval notification email:', emailError);
        }
      });

      return { success: true, result };

    } catch (error) {
      logger.error(`Error in autoApproveTimesheet for timesheet ${timesheet.id}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send approval notification email to employee
   */
  async sendApprovalNotificationEmail(timesheet) {
    try {
      if (!timesheet.employee?.contactEmail) {
        return;
      }
      // console.log(",,,,,,,,,,,,,,,",timesheet.project.dataValues.id,"----------------")
      const emailData = {
        employeeName: `${timesheet.employee.firstName} ${timesheet.employee.lastName}`,
        timesheetId: timesheet.id,
        timesheet_dates: moment(timesheet.date).format('DD MMM YYYY'),
        task_duration: timesheet.duration,
        project_names: timesheet.project?.dataValues.project_name || 'N/A',
        taskName: timesheet.task?.taskName || 'N/A',
        status_message: 'Approved',
        comment: 'Auto-approved by system (daily cron job)',
        approvedAt: moment().format('DD MMM YYYY, hh:mm A'),
        heading_message:'Timesheet Auto-Approved by Scheduler'
      };
      
      await emailService.sendMail(
        timesheet.employee?.contactEmail,
        emailData,
        'Timesheet Auto-Approved',
        'timesheet_notification.ejs'
      );

      logger.info(`📧 Approval notification sent to ${timesheet.employee.contactEmail} for timesheet ${timesheet.id}`);

    } catch (error) {
      logger.error('Error sending approval notification email:', error);
    }
  }

  /**
   * Send daily summary email to HR/Admin
   */
  async sendSummaryEmail(results) {
    try {
      // Get all HR managers and admins
      const hrManagers = await Employee.findAll({
        where: {
          status: 'active'
        },
        include: [
          {
            model: require('../../data/models').User,
            as: 'user',
            where: {
              status: 'active'
            },
            attributes: ['email']
          }
        ],
        attributes: ['firstName', 'lastName', 'contactEmail']
      });

      if (hrManagers.length === 0) {
        return;
      }

      const yesterday = moment().subtract(1, 'day').format('DD MMM YYYY');
      
      const emailData = {
        date: yesterday,
        totalProcessed: results.totalProcessed,
        totalApproved: results.totalApproved,
        totalErrors: results.totalErrors,
        approvedTimesheets: results.approvedTimesheets,
        errors: results.errors,
        generatedAt: moment().format('DD MMM YYYY, hh:mm A')
      };

      // Send to all HR managers
      for (const hrManager of hrManagers) {
        const email = hrManager.contactEmail || hrManager.user?.email;
        if (email) {
          await emailService.sendMail(
            email,
            emailData,
            `Timesheet Auto-Approval Summary - ${yesterday}`,
            'timesheet_auto_approval_summary.ejs'
          );
        }
      }

      logger.info(`📧 Summary email sent to ${hrManagers.length} HR managers`);

    } catch (error) {
      logger.error('Error sending summary email:', error);
    }
  }

  /**
   * Manual trigger for testing
   */
  async manualTrigger(targetDate = null) {
    if (this.isRunning) {
      throw new Error('Cron job is already running');
    }

    this.isRunning = true;
    
    try {
      logger.info('🧪 Manual trigger for timesheet auto-approval');
      
      // Override yesterday calculation if targetDate provided
      if (targetDate) {
        const originalSubtract = moment.prototype.subtract;
        moment.prototype.subtract = function() {
          return moment(targetDate);
        };
        
        const results = await this.autoApproveYesterdayTimesheets();
        
        // Restore original method
        moment.prototype.subtract = originalSubtract;
        
        return results;
      } else {
        return await this.autoApproveYesterdayTimesheets();
      }
      
    } finally {
      this.isRunning = false;
    }
  }
}

module.exports = TimesheetAutoApprovalCron;
