'use strict';

const { Employee, User, BusinessUnit, Attendance, Leave, ShiftType, Department, Designation, Role, CheckinReminderLog } = require('../../data/models');
const { Op } = require('sequelize');
const moment = require('moment-timezone');
const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');
const ejs = require('ejs');
const emailService = require('../email/emailService');

class NotCheckinEmailService {

  /**
   * 🚀 NEW: Send shift-based check-in reminders
   * This method sends reminders based on shift timing and prevents duplicates
   * @param {string} shiftCategory - Category of shifts to process (morning, ist, european, evening)
   */
  async sendShiftBasedCheckinReminders(shiftCategory = 'all') {
    try {
      console.log(`🚀 Starting shift-based check-in reminders for category: ${shiftCategory}...`);

      // Get all business units
      const businessUnits = await BusinessUnit.findAll({
        where: { status: 'active' },
        attributes: ['id', 'name', 'companyId', 'timezone']
      });

      let totalProcessed = 0;
      let totalReminders = 0;

      for (const businessUnit of businessUnits) {
        const result = await this.processBusinessUnitShiftReminders(businessUnit, shiftCategory);
        totalProcessed += result.employeesProcessed;
        totalReminders += result.remindersSent;
      }

      console.log(`✅ Shift-based check-in reminders completed:`);
      console.log(`   Category: ${shiftCategory}`);
      console.log(`   Employees processed: ${totalProcessed}`);
      console.log(`   Reminders sent: ${totalReminders}`);

      return {
        success: true,
        shiftCategory,
        employeesProcessed: totalProcessed,
        remindersSent: totalReminders
      };

    } catch (error) {
      console.error('❌ Error in shift-based check-in reminders:', error);
      throw error;
    }
  }

  /**
   * Process shift-based reminders for a specific business unit
   */
  async processBusinessUnitShiftReminders(businessUnit, shiftCategory) {
    try {
      const timezone = businessUnit.timezone || 'Asia/Kolkata';
      const today = moment().tz(timezone).format('YYYY-MM-DD');
      const currentTime = moment().tz(timezone);

      console.log(`📊 Processing shift reminders for BU: ${businessUnit.name}, Category: ${shiftCategory}`);

      // Get shift time ranges based on category
      const shiftTimeRanges = this.getShiftTimeRanges(shiftCategory);

      let totalEmployeesProcessed = 0;
      let totalRemindersSent = 0;

      // Process each shift time range
      for (const timeRange of shiftTimeRanges) {
        const result = await this.processShiftTimeRange(
          businessUnit,
          timeRange,
          today,
          currentTime,
          timezone
        );

        totalEmployeesProcessed += result.employeesProcessed;
        totalRemindersSent += result.remindersSent;
      }

      return {
        employeesProcessed: totalEmployeesProcessed,
        remindersSent: totalRemindersSent
      };

    } catch (error) {
      console.error(`❌ Error processing shift reminders for BU ${businessUnit.name}:`, error);
      return { employeesProcessed: 0, remindersSent: 0 };
    }
  }

  /**
   * Get shift time ranges based on category
   */
  getShiftTimeRanges(shiftCategory) {
    const allRanges = {
      morning: [
        { name: 'Early Morning', start: '06:00:00', end: '10:00:00', gracePeriod: 2 }
      ],
      ist: [
        { name: 'IST Standard', start: '09:00:00', end: '11:00:00', gracePeriod: 2 }
      ],
      european: [
        { name: 'European', start: '10:30:00', end: '12:00:00', gracePeriod: 2 }
      ],
      evening: [
        { name: 'Evening', start: '16:00:00', end: '19:00:00', gracePeriod: 2 }
      ],
      all: [
        { name: 'Early Morning', start: '06:00:00', end: '10:00:00', gracePeriod: 2 },
        { name: 'IST Standard', start: '09:00:00', end: '11:00:00', gracePeriod: 2 },
        { name: 'European', start: '10:30:00', end: '12:00:00', gracePeriod: 2 },
        { name: 'Evening', start: '16:00:00', end: '19:00:00', gracePeriod: 2 }
      ]
    };

    return allRanges[shiftCategory] || allRanges.all;
  }

  /**
   * Process employees in a specific shift time range
   */
  async processShiftTimeRange(businessUnit, timeRange, today, currentTime, timezone) {
    try {
      console.log(`🔍 Processing time range: ${timeRange.name} (${timeRange.start}-${timeRange.end})`);

      // Get employees in shifts that fall within this time range
      const employees = await this.getEmployeesInShiftTimeRange(
        businessUnit.id,
        timeRange,
        today
      );

      console.log(`📋 Found ${employees.length} employees in ${timeRange.name} shifts`);

      let employeesProcessed = 0;
      let remindersSent = 0;

      for (const employee of employees) {
        employeesProcessed++;

        // Check if reminder should be sent based on shift timing
        const shouldSend = await this.shouldSendShiftReminder(
          employee,
          currentTime,
          timezone,
          timeRange.gracePeriod
        );

        if (!shouldSend.send) {
          console.log(`⏭️ Skipping ${employee.name}: ${shouldSend.reason}`);
          continue;
        }

        // Check if reminder already sent today for this shift
        const alreadySent = await CheckinReminderLog.isReminderAlreadySent(
          employee.id,
          today,
          employee.shiftTypeId
        );

        if (alreadySent) {
          console.log(`⏭️ Reminder already sent to ${employee.name} today for this shift`);
          continue;
        }

        // Send reminder email
        await this.sendShiftBasedReminderEmail(employee, businessUnit, today);

        // Log the sent reminder
        await CheckinReminderLog.logSentReminder({
          employeeId: employee.id,
          businessUnitId: businessUnit.id,
          shiftTypeId: employee.shiftTypeId,
          reminderDate: today,
          shiftStartTime: employee.shiftStartTime,
          employeeEmail: employee.email,
          reminderType: 'SHIFT_START'
        });

        remindersSent++;
        console.log(`📧 Reminder sent to ${employee.name} (${employee.email})`);
      }

      console.log(`✅ ${timeRange.name} processing completed: ${remindersSent}/${employeesProcessed} reminders sent`);

      return {
        employeesProcessed,
        remindersSent
      };

    } catch (error) {
      console.error(`❌ Error processing time range ${timeRange.name}:`, error);
      return { employeesProcessed: 0, remindersSent: 0 };
    }
  }

  /**
   * Main function to send daily not check-in report
   */
  async sendDailyNotCheckinReport() {
    try {
      console.log('🚀 Starting daily not check-in report generation...');
      
      // Get all business units
      const businessUnits = await BusinessUnit.findAll({
        where: { status: 'active' },
        attributes: ['id', 'name', 'companyId', 'timezone']
      });

      for (const businessUnit of businessUnits) {
        await this.processBusinessUnit(businessUnit);
      }

      console.log('✅ Daily not check-in report completed successfully');
    } catch (error) {
      console.error('❌ Error in daily not check-in report:', error);
      throw error;
    }
  }

  /**
   * Process each business unit separately
   */
  async processBusinessUnit(businessUnit) {
    try {
      const timezone = businessUnit.timezone || 'Asia/Kolkata';
      const today = moment().tz(timezone).format('YYYY-MM-DD');
      
      console.log(`📊 Processing business unit: ${businessUnit.name} for date: ${today}`);

      // Get employees who haven't checked in today
      const notCheckedInEmployees = await this.getNotCheckedInEmployees(businessUnit.id, today, timezone);
      
      if (notCheckedInEmployees.length === 0) {
        console.log(`✅ No employees found without check-in for business unit: ${businessUnit.name}`);
        return;
      }

      console.log(`📋 Found ${notCheckedInEmployees.length} employees without check-in`);

      // Generate Excel report
      const excelFilePath = await this.generateExcelReport(notCheckedInEmployees, businessUnit, today);

      // Get HR managers for this business unit
      const hrManagers = await this.getHRManagers(businessUnit.id);

      if (hrManagers.length === 0) {
        console.log(`⚠️ No HR managers found for business unit: ${businessUnit.name}`);
        return;
      }

      // Send email to HR managers and employees
      await this.sendEmailReport(hrManagers, notCheckedInEmployees, excelFilePath, businessUnit, today);

      // Clean up temporary file
      if (fs.existsSync(excelFilePath)) {
        fs.unlinkSync(excelFilePath);
      }

    } catch (error) {
      console.error(`❌ Error processing business unit ${businessUnit.name}:`, error);
    }
  }

  /**
   * 🚀 NEW: Get employees in specific shift time range who haven't checked in
   */
  async getEmployeesInShiftTimeRange(businessUnitId, timeRange, today) {
    try {
      // Get employees on leave today (to exclude them)
      const date = new Date(today); // e.g., "2025-07-20"
      const day = date.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6

      if (day === 0 ||day === 6) 
        return [];
      const employeeIdsOnLeave = await Leave.findAll({
        where: {
          businessUnitId: businessUnitId,
          status: 'approved',
          startDate: { [Op.lte]: today },
          endDate: { [Op.gte]: today }
        },
        attributes: ['employeeId'],
        raw: true
      }).then(leaves => leaves.map(leave => leave.employeeId));

      // Get employees in shifts that fall within the time range and haven't checked in
      const employees = await Employee.findAll({
        where: {
          businessUnitId: businessUnitId,
          status: 'active',
          id: { [Op.notIn]: employeeIdsOnLeave }
        },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'firstName', 'lastName', 'email'],
            where: { status: 'active' },
            required: false
          },
          {
            model: ShiftType,
            as: 'shiftType',
            attributes: ['id', 'name', 'startTime', 'endTime'],
            where: {
              startTime: {
                [Op.between]: [timeRange.start, timeRange.end]
              }
            },
            required: true // Only get employees with shifts in this time range
          },
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: Designation,
            as: 'designation',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: Attendance,
            as: 'attendances',
            where: {
              date: today,
              clockInTime: { [Op.ne]: null }
            },
            required: false // LEFT JOIN - we want employees WITHOUT attendance
          }
        ]
      });

      // Filter employees who don't have attendance record for today
      const employeesWithoutCheckin = employees
        .filter(employee => !employee.attendances || employee.attendances.length === 0)
        .map(employee => ({
          id: employee.id,
          name: `${employee.firstName} ${employee.lastName}`,
          email: employee.contactEmail || employee.user?.email,
          employeeCode: employee.employeeId,
          designation: employee.designation?.name || 'N/A',
          department: employee.department?.name || 'N/A',
          shiftTypeId: employee.shiftType?.id,
          shiftStartTime: employee.shiftType?.startTime,
          shift: employee.shiftType ?
            `${employee.shiftType.name} (${employee.shiftType.startTime} - ${employee.shiftType.endTime})` :
            'Standard Shift',
          status: 'Not Checked In',
          date: today
        }));

      return employeesWithoutCheckin;

    } catch (error) {
      console.error('❌ Error getting employees in shift time range:', error);
      throw error;
    }
  }

  /**
   * 🚀 NEW: Check if reminder should be sent based on shift timing
   */
  async shouldSendShiftReminder(employee, currentTime, timezone, gracePeriodHours = 2) {
    try {
      if (!employee.shiftStartTime) {
        return {
          send: false,
          reason: 'No shift start time defined'
        };
      }

      const today = currentTime.format('YYYY-MM-DD');
      const shiftStartDateTime = moment.tz(`${today} ${employee.shiftStartTime}`, timezone);
      const reminderTime = shiftStartDateTime.clone().add(gracePeriodHours, 'hours');

      // Only send reminder if current time is after shift start + grace period
      if (currentTime.isBefore(reminderTime)) {
        const minutesUntilReminder = reminderTime.diff(currentTime, 'minutes');
        return {
          send: false,
          reason: `Too early - ${minutesUntilReminder} minutes until reminder time`
        };
      }

      // Don't send reminder too late (e.g., more than 4 hours after shift start)
      const maxReminderTime = shiftStartDateTime.clone().add(gracePeriodHours + 4, 'hours');
      if (currentTime.isAfter(maxReminderTime)) {
        return {
          send: false,
          reason: 'Too late - shift started more than 6 hours ago'
        };
      }

      return {
        send: true,
        reason: 'Within reminder window'
      };

    } catch (error) {
      console.error('❌ Error checking if should send shift reminder:', error);
      return {
        send: false,
        reason: 'Error in timing validation'
      };
    }
  }

  /**
   * 🚀 NEW: Send shift-based reminder email
   */
  async sendShiftBasedReminderEmail(employee, businessUnit, date) {
    try {
      if (!employee.email) {
        console.log(`⚠️ No email found for employee: ${employee.name}`);
        return;
      }

      const subject = `⏰ Check-in Reminder - ${employee.shift} - ${date}`;

      // Render employee reminder email using EJS template
      const templateData = {
        employee: employee,
        date: date,
        businessUnit: businessUnit
      };

      const emailBody = await ejs.renderFile(
        path.join(__dirname, '../../views/emails/checkin-reminder.ejs'),
        templateData
      );

      await emailService.sendEmail({
        to: employee.email,
        subject: subject,
        html: emailBody
      });

      console.log(`📧 Shift-based reminder sent to: ${employee.email}`);

    } catch (error) {
      console.error(`❌ Error sending shift-based reminder to ${employee.email}:`, error);
      throw error;
    }
  }

  /**
   * Get employees who haven't checked in today - OPTIMIZED VERSION
   */
  async getNotCheckedInEmployees(businessUnitId, today, timezone) {
    try {
      // Get employees on leave today (to exclude them)
      const employeeIdsOnLeave = await Leave.findAll({
        where: {
          businessUnitId: businessUnitId,
          status: 'approved',
          startDate: { [Op.lte]: today },
          endDate: { [Op.gte]: today }
        },
        attributes: ['employeeId'],
        raw: true
      }).then(leaves => leaves.map(leave => leave.employeeId));

      console.log(`📋 Found ${employeeIdsOnLeave.length} employees on leave today`);

      // Single optimized query to get employees who haven't checked in
      const notCheckedInEmployees = await Employee.findAll({
        where: {
          businessUnitId: businessUnitId,
          status: 'active',
          id: { [Op.notIn]: employeeIdsOnLeave } // Exclude employees on leave
        },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'firstName', 'lastName', 'email'],
            where: { status: 'active' },
            required: false
          },
          {
            model: ShiftType,
            as: 'shiftType',
            attributes: ['id', 'name', 'startTime', 'endTime'],
            required: false
          },
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: Designation,
            as: 'designation',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: Attendance,
            as: 'attendances',
            where: {
              date: today,
              clockInTime: { [Op.ne]: null }
            },
            required: false, // LEFT JOIN - we want employees WITHOUT attendance
            attributes: ['id', 'clockInTime']
          }
        ]
      });

      // Filter employees who don't have attendance record for today
      const employeesWithoutCheckin = notCheckedInEmployees
        .filter(employee => !employee.attendances || employee.attendances.length === 0)
        .map(employee => ({
          id: employee.id,
          name: `${employee.firstName} ${employee.lastName}`,
          email: employee.contactEmail || employee.user?.email,
          employeeCode: employee.employeeId,
          designation: employee.designation?.name || 'N/A',
          department: employee.department?.name || 'N/A',
          shift: employee.shiftType ?
            `${employee.shiftType.name} (${employee.shiftType.startTime} - ${employee.shiftType.endTime})` :
            'Standard Shift',
          status: 'Not Checked In',
          date: today
        }));

      console.log(`📊 Found ${employeesWithoutCheckin.length} employees without check-in`);
      return employeesWithoutCheckin;

    } catch (error) {
      console.error('❌ Error getting not checked in employees:', error);
      throw error;
    }
  }

  /**
   * Get HR managers for business unit
   */
  async getHRManagers(businessUnitId) {
    try {
      // First get the business unit to find company ID
      const businessUnit = await BusinessUnit.findOne({
        where: { id: businessUnitId },
        attributes: ['id', 'companyId']
      });

      if (!businessUnit) {
        console.log(`⚠️ Business unit ${businessUnitId} not found`);
        return [];
      }

      // Find HR managers in this company who have employees in this business unit
      const hrManagers = await User.findAll({
        where: {
          status: 'active',
          companyId: businessUnit.companyId
        },
        include: [
          {
            model: Employee,
            as: 'employee',
            where: {
              businessUnitId: businessUnitId,
              status: 'active'
            },
            attributes: ['id', 'firstName', 'lastName'],
            required: true // INNER JOIN - only users who have employee records
          },
          {
            model: Role,
            as: 'roles',
            where: {
              name: { [Op.in]: ['HR_Manager', 'hr_manager', 'HR Manager', 'hr', 'HR'] }
            },
            through: { attributes: [] }, // Exclude user_roles junction table attributes
            attributes: ['id', 'name'],
            required: true // INNER JOIN - only users who have HR roles
          }
        ],
        attributes: ['id', 'firstName', 'lastName', 'email']
      });

      console.log(`📋 Found ${hrManagers.length} HR managers for business unit ${businessUnitId}`);
      return hrManagers;
    } catch (error) {
      console.error('❌ Error getting HR managers:', error);
      throw error;
    }
  }

  /**
   * Generate Excel report
   */
  async generateExcelReport(employees, businessUnit, date) {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Not Checked In Report');

      // Add headers
      worksheet.columns = [
        { header: 'Employee Code', key: 'employeeCode', width: 15 },
        { header: 'Name', key: 'name', width: 25 },
        { header: 'Email', key: 'email', width: 30 },
        { header: 'Designation', key: 'designation', width: 20 },
        { header: 'Department', key: 'department', width: 20 },
        { header: 'Shift', key: 'shift', width: 25 },
        { header: 'Status', key: 'status', width: 15 },
        { header: 'Date', key: 'date', width: 12 }
      ];

      // Style headers
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6E6FA' }
      };

      // Add data
      employees.forEach(employee => {
        const row = worksheet.addRow(employee);
        
        // Color code status
        if (employee.status === 'Not Checked In') {
          row.getCell('status').fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFF6B6B' }
          };
        }
      });

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        column.width = Math.max(column.width, 10);
      });

      // Save file
      const fileName = `not-checkin-report-${businessUnit.name.replace(/\s+/g, '-')}-${date}.xlsx`;
      const filePath = path.join(__dirname, '../../../temp', fileName);
      
      // Ensure temp directory exists
      const tempDir = path.dirname(filePath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      await workbook.xlsx.writeFile(filePath);
      return filePath;
    } catch (error) {
      console.error('❌ Error generating Excel report:', error);
      throw error;
    }
  }

  /**
   * Send email report using EJS templates
   */
  async sendEmailReport(hrManagers, employees, excelFilePath, businessUnit, date) {
    try {
      const subject = `Daily Not Check-in Report - ${businessUnit.name} - ${date}`;

      // Render HR report email using EJS template
      const hrTemplateData = {
        businessUnit: businessUnit,
        employees: employees,
        date: date
      };

      const hrEmailBody = await ejs.renderFile(
        path.join(__dirname, '../../views/emails/not-checkin-report.ejs'),
        hrTemplateData
      );

      // Send to HR managers
      for (const hrManager of hrManagers) {
        await emailService.sendEmail({
          to: hrManager.email,
          subject: subject,
          html: hrEmailBody,
          attachments: [
            {
              filename: path.basename(excelFilePath),
              path: excelFilePath
            }
          ]
        });

        console.log(`📧 Email sent to HR Manager: ${hrManager.email}`);
      }

      // Send to employees (notification about missing check-in)
      for (const employee of employees) {
        if (employee.email) {
          const employeeSubject = `Check-in Reminder - ${date}`;

          // Render employee reminder email using EJS template
          const employeeTemplateData = {
            employee: employee,
            date: date
          };

          const employeeEmailBody = await ejs.renderFile(
            path.join(__dirname, '../../views/emails/checkin-reminder.ejs'),
            employeeTemplateData
          );

          await emailService.sendEmail({
            to: employee.email,
            subject: employeeSubject,
            html: employeeEmailBody
          });

          console.log(`📧 Reminder sent to employee: ${employee.email}`);
        }
      }

    } catch (error) {
      console.error('❌ Error sending email report:', error);
      throw error;
    }
  }
}

module.exports = new NotCheckinEmailService();
