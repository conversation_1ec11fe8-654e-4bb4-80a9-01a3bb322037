'use strict';

const cron = require('node-cron');
const logger = require('../../common/logging');
const config = require('../../../config');
const { updateMilestoneStatus } = require('./milestoneStatusCron');
const { fillEmptyRowsOSBillable } = require('./billingCycleCron');
const { updateEstimatedFieldsTnmOSBillable } = require('./estimatedFieldsCron');
const { updateProjectFinancials, updateProjectProfitLoss } = require('./projectFinancialsCron');
const OvertimeCronService = require('../overtime/overtimeCronService');
const lopCalculationJob = require('../../jobs/lopCalculationJob');
// const lopShiftCronService = require('../lop/lopShiftCronService'); // Deleted - using optimized version
const optimizedLopCronService = require('../lop/optimizedLopCronService');
// const autoCheckoutCronService = require('../attendance/autoCheckoutCronService'); // Deleted - using optimized version
const optimizedAutoCheckoutService = require('../attendance/optimizedAutoCheckoutCronService');
const notCheckinEmailService = require('./notCheckinEmailService');
const autoRejectExpiredRequestsService = require('./autoRejectExpiredRequestsService');
const TimesheetAutoApprovalCron = require('./timesheetAutoApprovalCron');

// Initialize overtime cron service
const overtimeCronService = new OvertimeCronService();

/**
 * Initialize all cron jobs
 *
 * This function sets up and schedules all cron jobs for the application.
 * Each job is scheduled with its own frequency and has error handling.
 */
const initCronJobs = async () => {
  logger.info('Initializing cron jobs...');

  // Update milestone statuses : Run crons after 5:30am Indian Time to avoid clash with UTC
  cron.schedule('40 05 * * *', async () => {
    try {
      logger.info('Running cron job: updateMilestoneStatus');
      async () => {
        await updateMilestoneStatus();
      }
      logger.info('Completed cron job: updateMilestoneStatus');
    } catch (error) {
      logger.error(`Error in updateMilestoneStatus cron job: ${error.message}`, error);
    }
  },
  {
    timezone: 'Asia/Kolkata'
  });

  // Fill empty billing cycle rows 
  cron.schedule('45 05 * * *', async () => {
    try {
      logger.info('Running cron job: fillEmptyRowsOSBillable');
        await fillEmptyRowsOSBillable();
      logger.info('Completed cron job: fillEmptyRowsOSBillable');
    } catch (error) {
      logger.error(`Error in fillEmptyRowsOSBillable cron job: ${error.message}`, error);
    }
  },
  {
    timezone: 'Asia/Kolkata'
  });

  // Update estimated fields
  cron.schedule('50 05 * * *', async () => {
    try {
      logger.info('Running cron job: updateEstimatedFieldsTnmOSBillable');
      await updateEstimatedFieldsTnmOSBillable();
      logger.info('Completed cron job: updateEstimatedFieldsTnmOSBillable');
    } catch (error) {
      logger.error(`Error in updateEstimatedFieldsTnmOSBillable cron job: ${error.message}`, error);
    }
  },
  {
    timezone: 'Asia/Kolkata'
  });

  // Update project financials - Monthly Cost data of each Project
  cron.schedule('55 05 * * *', async () => {
    try {
      logger.info('\n== Running cron job: updateProjectFinancials');
      await updateProjectFinancials();
      logger.info('\n== Completed cron job: updateProjectFinancials');
    } catch (error) {
      logger.error(`\n== Error in updateProjectFinancials cron job: ${error.message}`, error);
    }
  },
  {
    timezone: 'Asia/Kolkata'
  });

  // Update project profit/loss - ProfitLoss of each Project
  cron.schedule('00 06 * * *', async () => {
    try {
      logger.info('\n== Running cron job: updateProjectProfitLoss');
      await updateProjectProfitLoss();
      logger.info('\n== Completed cron job: updateProjectProfitLoss');
    } catch (error) {
      logger.error(`\n== Error in updateProjectProfitLoss cron job: ${error.message}`, error);
    }
  },
  {
    timezone: 'Asia/Kolkata'
  });

  // Initialize overtime calculation cron jobs
  try {
    logger.info('🚀 Initializing overtime cron jobs...');
    // await overtimeCronService.initializeOvertimeCronJobs();
    logger.info('✅ Overtime cron jobs initialized successfully');
  } catch (error) {
    logger.error('❌ Error initializing overtime cron jobs:', error);
  }

  // Initialize optimized auto-checkout cron jobs (3 consolidated jobs instead of 1000s)
  if (config.cron.autoCheckout.enabled) {
    try {
      logger.info('🚀 Initializing optimized auto-checkout cron jobs...');
      logger.info(`📋 Environment: ${config.app.env}, Auto-checkout enabled: ${config.cron.autoCheckout.enabled}`);
      // await optimizedAutoCheckoutService.initialize();
      logger.info('✅ Optimized auto-checkout cron jobs initialized successfully');
    } catch (error) {
      logger.error('❌ Error initializing optimized auto-checkout cron jobs:', error);
    }
  } else {
    logger.info('⏭️ Auto-checkout cron jobs skipped (disabled in current environment)');
    logger.info(`📋 Current environment: ${config.app.env}, Auto-checkout enabled: ${config.cron.autoCheckout.enabled}`);
  }

  // Initialize optimized LOP calculation cron jobs (3 consolidated jobs instead of 1000s)
  if (config.cron.lop.enabled) {
    try {
      logger.info('🚀 Initializing optimized LOP calculation cron jobs...');
      logger.info(`📋 Environment: ${config.app.env}, LOP cron enabled: ${config.cron.lop.enabled}`);
      // await optimizedLopCronService.initialize();
      logger.info('✅ Optimized LOP calculation cron jobs initialized successfully');
    } catch (error) {
      logger.error('❌ Error initializing optimized LOP calculation cron jobs:', error);
    }
  } else {
    logger.info('⏭️ LOP calculation cron jobs skipped (disabled in current environment)');
    logger.info(`📋 Current environment: ${config.app.env}, LOP cron enabled: ${config.cron.lop.enabled}`);
  }

  // Initialize shift completion cron jobs
  try {
    logger.info('🚀 Initializing shift completion cron jobs...');
    const ShiftCompletionCron = require('./shiftCompletionCron');
    const shiftCompletionCron = new ShiftCompletionCron();
    shiftCompletionCron.start();
    logger.info('✅ Shift completion cron jobs initialized successfully');
  } catch (error) {
    logger.error('❌ Error initializing shift completion cron jobs:', error);
  }

  // Initialize shift swap expiration cron jobs
  try {
    logger.info('🚀 Initializing shift swap expiration cron jobs...');
    const ShiftSwapExpirationCron = require('./shiftSwapExpirationCron');
    const shiftSwapExpirationCron = new ShiftSwapExpirationCron();
    shiftSwapExpirationCron.start();
    logger.info('✅ Shift swap expiration cron jobs initialized successfully');
  } catch (error) {
    logger.error('❌ Error initializing shift swap expiration cron jobs:', error);
  }

  // 🚀 NEW: Multiple shift-based check-in reminder crons
  if (config.cron.notCheckinEmail.enabled) {

    // Morning shifts reminder (6:00-10:00 AM shifts) - Send at 11:00 AM IST (5:30 AM UTC)
    cron.schedule('30 05 * * *', async () => {
      try {
        logger.info('🚀 Running morning shifts check-in reminders...');
        await notCheckinEmailService.sendShiftBasedCheckinReminders('morning');
        logger.info('✅ Morning shifts check-in reminders completed successfully');
      } catch (error) {
        logger.error('❌ Error in morning shifts check-in reminders:', error);
      }
    }, {
      timezone: 'UTC'
    });

    // IST shifts reminder (9:00-11:00 AM shifts) - Send at 11:30 AM IST (6:00 AM UTC)
    cron.schedule('0 06 * * *', async () => {
      try {
        logger.info('🚀 Running IST shifts check-in reminders...');
        await notCheckinEmailService.sendShiftBasedCheckinReminders('ist');
        logger.info('✅ IST shifts check-in reminders completed successfully');
      } catch (error) {
        logger.error('❌ Error in IST shifts check-in reminders:', error);
      }
    }, {
      timezone: 'UTC'
    });

    // European shifts reminder (10:30-12:00 PM shifts) - Send at 1:00 PM IST (7:30 AM UTC)
    cron.schedule('30 07 * * *', async () => {
      try {
        logger.info('🚀 Running European shifts check-in reminders...');
        // await notCheckinEmailService.sendShiftBasedCheckinReminders('european');
        logger.info('✅ European shifts check-in reminders completed successfully');
      } catch (error) {
        logger.error('❌ Error in European shifts check-in reminders:', error);
      }
    }, {
      timezone: 'UTC'
    });

    // Evening shifts reminder (4:00-7:00 PM shifts) - Send at 7:00 PM IST (1:30 PM UTC)
    cron.schedule('30 13 * * *', async () => {
      try {
        logger.info('🚀 Running evening shifts check-in reminders...');
        // await notCheckinEmailService.sendShiftBasedCheckinReminders('evening');
        logger.info('✅ Evening shifts check-in reminders completed successfully');
      } catch (error) {
        logger.error('❌ Error in evening shifts check-in reminders:', error);
      }
    }, {
      timezone: 'UTC'
    });

    // 🚀 LEGACY: Keep original daily report for HR (now runs at 2:00 PM IST)
    cron.schedule('30 08 * * *', async () => {
      try {
        logger.info('🚀 Running daily not check-in HR report...');
        // await notCheckinEmailService.sendDailyNotCheckinReport();
        logger.info('✅ Daily not check-in HR report completed successfully');
      } catch (error) {
        logger.error('❌ Error in daily not check-in HR report:', error);
      }
    }, {
      timezone: 'UTC'
    });

    logger.info('✅ Multiple shift-based check-in reminder cron jobs scheduled:');
    logger.info('   - Morning shifts: 11:00 AM IST (5:30 AM UTC)');
    logger.info('   - IST shifts: 11:30 AM IST (6:00 AM UTC)');
    logger.info('   - European shifts: 1:00 PM IST (7:30 AM UTC)');
    logger.info('   - Evening shifts: 7:00 PM IST (1:30 PM UTC)');
    logger.info('   - HR daily report: 2:00 PM IST (8:30 AM UTC)');

  } else {
    logger.info('⏭️ Shift-based check-in reminder cron jobs skipped (disabled in current environment)');
  }

  // Auto-reject expired leave and WFH requests - 2:00 AM IST (8:30 PM UTC previous day)
  if (config.cron.autoRejectExpired.enabled) {
    cron.schedule('30 20 * * *', async () => {
      try {
        logger.info('🚀 Running auto-reject expired requests job...');
        const result = await autoRejectExpiredRequestsService.autoRejectExpiredRequests();
        logger.info(`✅ Auto-reject job completed. Rejected ${result.rejectedLeaves} leaves and ${result.rejectedWFHs} WFH requests.`);
      } catch (error) {
        logger.error('❌ Error in auto-reject expired requests job:', error);
      }
    }, {
      timezone: 'UTC'
    });
    logger.info('✅ Auto-reject expired requests cron job scheduled');
  } else {
    logger.info('⏭️ Auto-reject expired requests cron job skipped (disabled in current environment)');
  }

  // Initialize timesheet auto-approval cron job
  try {
    logger.info('🚀 Initializing timesheet auto-approval cron job...');
    const timesheetAutoApprovalCron = new TimesheetAutoApprovalCron();
    timesheetAutoApprovalCron.start();
    logger.info('✅ Timesheet auto-approval cron job initialized successfully (runs daily at 2:00 AM UTC)');
  } catch (error) {
    logger.error('❌ Error initializing timesheet auto-approval cron job:', error);
  }

  logger.info('All cron jobs initialized successfully');
};

module.exports = {
  initCronJobs,
  overtimeCronService,
  lopCalculationJob,
  // lopShiftCronService, // Deleted - using optimized version
  optimizedAutoCheckoutService,
  optimizedLopCronService
};