'use strict';

const { Leave, Work<PERSON>romHome, Employee, User, BusinessUnit } = require('../../data/models');
const { Op } = require('sequelize');
const moment = require('moment-timezone');
const emailService = require('../email/emailService');

class AutoRejectExpiredRequestsService {
  /**
   * Main function to auto-reject expired leave and WFH requests
   */
  async autoRejectExpiredRequests() {
    try {
      console.log('🕒 Starting auto-reject expired requests job...');
      
      const today = moment().format('YYYY-MM-DD');
      const cutoffDate = moment().subtract(15, 'days').format('YYYY-MM-DD');
      
      console.log(`📅 Today: ${today}`);
      console.log(`📅 Cutoff date (15 days ago): ${cutoffDate}`);
      
      // Process expired leave requests
      const expiredLeaves = await this.processExpiredLeaves(cutoffDate);
      
      // Process expired WFH requests
      const expiredWFHs = await this.processExpiredWFHs(cutoffDate);
      
      console.log(`✅ Auto-reject job completed. Rejected ${expiredLeaves} leaves and ${expiredWFHs} WFH requests.`);
      
      return {
        rejectedLeaves: expiredLeaves,
        rejectedWFHs: expiredWFHs,
        processedAt: today
      };
      
    } catch (error) {
      console.error('❌ Error in auto-reject expired requests job:', error);
      throw error;
    }
  }

  /**
   * Process expired leave requests
   */
  async processExpiredLeaves(cutoffDate) {
    try {
      console.log('🏖️ Processing expired leave requests...');
      
      // Find pending leave requests where end_date is 15+ days ago
      const expiredLeaves = await Leave.findAll({
        where: {
          status: 'pending',
          endDate: {
            [Op.lte]: cutoffDate
          }
        },
        include: [
          {
            model: Employee,
            as: 'employee',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'email', 'firstName', 'lastName']
              },
              {
                model: BusinessUnit,
                as: 'businessUnit',
                attributes: ['id', 'name', 'timezone']
              }
            ],
            attributes: ['id', 'firstName', 'lastName', 'employeeId']
          }
        ]
      });

      console.log(`📋 Found ${expiredLeaves.length} expired leave requests to reject`);

      let rejectedCount = 0;
      
      for (const leave of expiredLeaves) {
        try {
          // Update leave status to rejected
          await leave.update({
            status: 'rejected',
            rejectionReason: 'Automatically rejected - Request expired (15+ days after end date)'
          });

          // Send notification email to employee
          await this.sendRejectionNotification(leave, 'leave');
          
          rejectedCount++;
          console.log(`✅ Rejected leave request ID: ${leave.id} for employee: ${leave.employee.firstName} ${leave.employee.lastName}`);
          
        } catch (error) {
          console.error(`❌ Error rejecting leave ID ${leave.id}:`, error);
        }
      }

      return rejectedCount;
    } catch (error) {
      console.error('❌ Error processing expired leaves:', error);
      throw error;
    }
  }

  /**
   * Process expired WFH requests
   */
  async processExpiredWFHs(cutoffDate) {
    try {
      console.log('🏠 Processing expired WFH requests...');
      
      // Find pending WFH requests where end_date is 15+ days ago
      const expiredWFHs = await WorkFromHome.findAll({
        where: {
          status: 'pending',
          endDate: {
            [Op.lte]: cutoffDate
          }
        },
        include: [
          {
            model: Employee,
            as: 'employee',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'email', 'firstName', 'lastName']
              },
              {
                model: BusinessUnit,
                as: 'businessUnit',
                attributes: ['id', 'name', 'timezone']
              }
            ],
            attributes: ['id', 'firstName', 'lastName', 'employeeId']
          }
        ]
      });

      console.log(`📋 Found ${expiredWFHs.length} expired WFH requests to reject`);

      let rejectedCount = 0;
      
      for (const wfh of expiredWFHs) {
        try {
          // Update WFH status to rejected
          await wfh.update({
            status: 'rejected',
            rejectionReason: 'Automatically rejected - Request expired (15+ days after end date)'
          });

          // Send notification email to employee
          await this.sendRejectionNotification(wfh, 'wfh');
          
          rejectedCount++;
          console.log(`✅ Rejected WFH request ID: ${wfh.id} for employee: ${wfh.employee.firstName} ${wfh.employee.lastName}`);
          
        } catch (error) {
          console.error(`❌ Error rejecting WFH ID ${wfh.id}:`, error);
        }
      }

      return rejectedCount;
    } catch (error) {
      console.error('❌ Error processing expired WFHs:', error);
      throw error;
    }
  }

  /**
   * Send rejection notification email to employee
   */
  async sendRejectionNotification(request, type) {
    try {
      const employee = request.employee;
      const user = employee.user;
      
      if (!user || !user.email) {
        console.log(`⚠️ No email found for employee ${employee.firstName} ${employee.lastName}`);
        return;
      }

      const requestType = type === 'leave' ? 'Leave' : 'Work From Home';
      const subject = `${requestType} Request Auto-Rejected - ${request.startDate} to ${request.endDate}`;
      
      const emailBody = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="margin: 0; color: #721c24;">🚫 ${requestType} Request Rejected</h2>
          </div>
          
          <p>Dear ${employee.firstName} ${employee.lastName},</p>
          
          <p>Your ${requestType.toLowerCase()} request has been automatically rejected due to expiration.</p>
          
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Request Details:</h3>
            <p><strong>Type:</strong> ${requestType}</p>
            <p><strong>Period:</strong> ${moment(request.startDate).format('DD MMM YYYY')} to ${moment(request.endDate).format('DD MMM YYYY')}</p>
            <p><strong>Reason:</strong> ${request.reason || 'Not specified'}</p>
            <p><strong>Rejection Reason:</strong> Automatically rejected - Request expired (15+ days after end date)</p>
          </div>
          
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <strong>📝 Note:</strong> Requests are automatically rejected if not approved within 15 days after the end date. 
            Please submit new requests in advance for future dates.
          </div>
          
          <p>If you have any questions, please contact your HR department.</p>
          
          <p>Best regards,<br>HRMS System</p>
        </div>
      `;

      await emailService.sendEmail({
        to: user.email,
        subject: subject,
        html: emailBody
      });
      
      console.log(`📧 Rejection notification sent to: ${user.email}`);
      
    } catch (error) {
      console.error('❌ Error sending rejection notification:', error);
    }
  }
}

module.exports = new AutoRejectExpiredRequestsService();
