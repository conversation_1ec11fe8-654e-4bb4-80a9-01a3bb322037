const { WorkFromHome, WfhType, Employee, Department, Company, BusinessUnit } = require('../../data/models');
const { Op, fn, col, literal } = require('sequelize');
const moment = require('moment');

/**
 * WFH Dashboard Service
 * Provides comprehensive WFH dashboard analytics and metrics
 */
class WfhDashboardService {

  /**
   * Get comprehensive WFH dashboard overview
   * @param {Object} filters - Filter criteria
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Dashboard overview data
   */
  async getOverview(filters = {}, tenantContext) {
    const { period = 'month' } = filters;
    const baseWhere = this.buildBaseWhere(tenantContext);
    
    // Get date range based on period
    const dateRange = this.getDateRange(period);

    // Get all metrics in parallel
    const [
      keyMetrics,
      topWfhTypes,
      monthlyTrend,
      upcomingWfh,
      pendingApprovals
    ] = await Promise.all([
      this.getKeyMetrics(baseWhere, dateRange.start, dateRange.end),
      this.getTopWfhTypes(baseWhere, dateRange.start, dateRange.end, 5),
      this.getMonthlyTrend(baseWhere, 6),
      this.getUpcomingWfh(baseWhere, 5),
      this.getPendingApprovals(baseWhere, 5)
    ]);

    return {
      keyMetrics,
      topWfhTypes,
      monthlyTrend,
      upcomingWfh,
      pendingApprovals
    };
  }

  /**
   * Get key WFH metrics
   * @param {Object} baseWhere - Base where conditions
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<Object>} Key metrics
   */
  async getKeyMetrics(baseWhere, startDate, endDate) {
    const today = moment().format('YYYY-MM-DD');

    // Get total employees count
    const totalEmployees = await Employee.count({
      where: {
        companyId: baseWhere.companyId,
        ...(baseWhere.businessUnitId && { businessUnitId: baseWhere.businessUnitId }),
        status:'active'
      }
    });

    // Get WFH data for the period
    const wfhData = await WorkFromHome.findAll({
      attributes: [
        'status',
        [fn('COUNT', col('id')), 'count'],
        [fn('AVG', col('total_days')), 'avgDuration']
      ],
      where: {
        ...baseWhere,
        startDate: { [Op.between]: [startDate, endDate] }
      },
      group: ['status'],
      raw: true
    });

    // Get employees on WFH today from approved WFH requests
    const onWfhTodayFromRequests = await WorkFromHome.count({
      where: {
        ...baseWhere,
        status: 'approved',
        startDate: { [Op.lte]: today },
        endDate: { [Op.gte]: today }
      }
    });

    // Get employees who checked in as WFH today from attendance records
    // This integrates with attendance data where mode_of_work = 'WFH'
    const { Attendance } = require('../../data/models');
    const wfhCheckInsToday = await Attendance.count({
      where: {
        companyId: baseWhere.companyId,
        ...(baseWhere.businessUnitId && { businessUnitId: baseWhere.businessUnitId }),
        date: today,
        modeOfWork: 'WFH',
        clockInTime: { [Op.ne]: null }
      },
      distinct: true,
      col: 'employeeId'
    });

    // Combine WFH from requests and check-ins (use the higher count to avoid under-reporting)
    const onWfhToday = Math.max(onWfhTodayFromRequests, wfhCheckInsToday);

    // Calculate metrics
    const totalRequests = wfhData.reduce((sum, item) => sum + parseInt(item.count), 0);
    const approvedRequests = wfhData.find(item => item.status === 'approved')?.count || 0;
    const pendingRequests = wfhData.find(item => item.status === 'pending')?.count || 0;
    const approvalRate = totalRequests > 0 ? ((approvedRequests / totalRequests) * 100).toFixed(1) : 0;
    const averageDuration = wfhData.find(item => item.avgDuration)?.avgDuration || 0;

    // Calculate utilization rate (approved WFH days vs total working days)
    const workingDaysInPeriod = this.getWorkingDays(startDate, endDate);
    const totalWfhDays = await WorkFromHome.sum('total_days', {
      where: {
        ...baseWhere,
        status: 'approved',
        startDate: { [Op.between]: [startDate, endDate] }
      }
    }) || 0;
    
    const utilizationRate = totalEmployees > 0 && workingDaysInPeriod > 0 ? 
      ((totalWfhDays / (totalEmployees * workingDaysInPeriod)) * 100).toFixed(1) : 0;

    return {
      totalEmployees,
      onWfhToday,
      pendingRequests: parseInt(pendingRequests),
      approvalRate: parseFloat(approvalRate),
      wfhUtilization: parseFloat(utilizationRate),
      averageDuration: parseFloat(averageDuration).toFixed(1)
    };
  }

  /**
   * Get employees on WFH today (helper method for today's data)
   * @param {Object} baseWhere - Base where conditions
   * @returns {Promise<number>} Count of employees on WFH today
   */
  async getOnWfhToday(baseWhere) {
    const today = moment().format('YYYY-MM-DD');

    return await WorkFromHome.count({
      where: {
        ...baseWhere,
        status: 'approved',
        startDate: { [Op.lte]: today },
        endDate: { [Op.gte]: today }
      }
    });
  }

  /**
   * Get top WFH types
   * @param {Object} baseWhere - Base where conditions
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @param {number} limit - Number of types to return
   * @returns {Promise<Array>} Top WFH types
   */
  async getTopWfhTypes(baseWhere, startDate, endDate, limit = 5) {
    const wfhTypes = await WorkFromHome.findAll({
      attributes: [
        'wfhTypeId',
        [fn('COUNT', col('WorkFromHome.id')), 'count'],
        [fn('SUM', col('total_days')), 'totalDays']
      ],
      include: [
        {
          model: WfhType,
          as: 'wfhType',
          attributes: ['id', 'name', 'color'],
          required: true
        }
      ],
      where: {
        ...baseWhere,
        startDate: { [Op.between]: [startDate, endDate] }
      },
      group: ['wfhTypeId', 'wfhType.id', 'wfhType.name', 'wfhType.color'],
      order: [[fn('COUNT', col('WorkFromHome.id')), 'DESC']],
      limit,
      raw: true
    });

    const totalRequests = wfhTypes.reduce((sum, type) => sum + parseInt(type.count), 0);

    return wfhTypes.map(type => ({
      typeId: type.wfhTypeId,
      type: type['wfhType.name'],
      count: parseInt(type.count),
      percentage: totalRequests > 0 ? ((parseInt(type.count) / totalRequests) * 100).toFixed(1) : 0,
      totalDays: parseInt(type.totalDays || 0),
      color: type['wfhType.color'] || '#3b82f6'
    }));
  }

  /**
   * Get monthly WFH trend
   * @param {Object} baseWhere - Base where conditions
   * @param {number} months - Number of months to include
   * @returns {Promise<Array>} Monthly trend data
   */
  async getMonthlyTrend(baseWhere, months = 6) {
    const endDate = moment().endOf('month');
    const startDate = moment().subtract(months - 1, 'months').startOf('month');

    const trends = await WorkFromHome.findAll({
      attributes: [
        [fn('DATE_TRUNC', 'month', col('start_date')), 'month'],
        [fn('COUNT', col('id')), 'totalRequests'],
        [fn('SUM', literal("CASE WHEN status = 'approved' THEN 1 ELSE 0 END")), 'approved'],
        [fn('SUM', literal("CASE WHEN status = 'rejected' THEN 1 ELSE 0 END")), 'rejected'],
        [fn('SUM', literal("CASE WHEN status = 'pending' THEN 1 ELSE 0 END")), 'pending']
      ],
      where: {
        ...baseWhere,
        startDate: {
          [Op.between]: [startDate.format('YYYY-MM-DD'), endDate.format('YYYY-MM-DD')]
        }
      },
      group: [fn('DATE_TRUNC', 'month', col('start_date'))],
      order: [[fn('DATE_TRUNC', 'month', col('start_date')), 'ASC']],
      raw: true
    });

    // Fill in missing months with zero data
    const result = [];
    for (let i = 0; i < months; i++) {
      const monthDate = moment().subtract(months - 1 - i, 'months');
      const monthKey = monthDate.format('YYYY-MM-01');
      
      const existingData = trends.find(trend => 
        moment(trend.month).format('YYYY-MM-01') === monthKey
      );

      result.push({
        month: monthDate.format('MMM'),
        fullMonth: monthDate.format('MMMM YYYY'),
        requests: parseInt(existingData?.totalRequests || 0),
        approved: parseInt(existingData?.approved || 0),
        rejected: parseInt(existingData?.rejected || 0),
        pending: parseInt(existingData?.pending || 0)
      });
    }

    return result;
  }

  /**
   * Get upcoming WFH requests
   * @param {Object} baseWhere - Base where conditions
   * @param {number} limit - Number of records to return
   * @returns {Promise<Array>} Upcoming WFH requests
   */
  async getUpcomingWfh(baseWhere, limit = 5) {
    const today = moment().format('YYYY-MM-DD');

    const upcomingWfh = await WorkFromHome.findAll({
      attributes: [
        'id', 'startDate', 'endDate', 'totalDays', 'status'
      ],
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'firstName', 'lastName'],
          include: [
            {
              model: Department,
              as: 'department',
              attributes: ['name']
            }
          ]
        },
        {
          model: WfhType,
          as: 'wfhType',
          attributes: ['name', 'color']
        }
      ],
      where: {
        ...baseWhere,
        status: 'approved',
        startDate: { [Op.gte]: today }
      },
      order: [['startDate', 'ASC']],
      limit,
      raw: true
    });

    return upcomingWfh.map(wfh => ({
      id: wfh.id,
      employeeId: wfh['employee.id'],
      employeeName: `${wfh['employee.firstName']} ${wfh['employee.lastName']}`,
      department: wfh['employee.department.name'] || 'Unknown',
      wfhType: wfh['wfhType.name'],
      wfhTypeColor: wfh['wfhType.color'] || '#3b82f6',
      startDate: wfh.startDate,
      endDate: wfh.endDate,
      duration: parseFloat(wfh.totalDays),
      status: wfh.status
    }));
  }

  /**
   * Get pending WFH approvals
   * @param {Object} baseWhere - Base where conditions
   * @param {number} limit - Number of records to return
   * @returns {Promise<Array>} Pending approvals
   */
  async getPendingApprovals(baseWhere, limit = 5) {
    const pendingApprovals = await WorkFromHome.findAll({
      attributes: [
        'id', 'startDate', 'endDate', 'totalDays', 'reason', 'createdAt'
      ],
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'firstName', 'lastName'],
          where:{
            status: 'active'
          },
          include: [
            {
              model: Department,
              as: 'department',
              attributes: ['name']
            }
          ]
        },
        {
          model: WfhType,
          as: 'wfhType',
          attributes: ['name', 'color']
        }
      ],
      where: {
        ...baseWhere,
        status: 'pending'
      },
      order: [['createdAt', 'DESC']],
      limit,
      raw: true
    });

    return pendingApprovals.map(wfh => ({
      id: wfh.id,
      employeeId: wfh['employee.id'],
      employeeName: `${wfh['employee.firstName']} ${wfh['employee.lastName']}`,
      department: wfh['employee.department.name'] || 'Unknown',
      wfhTypeName: wfh['wfhType.name'],
      wfhTypeColor: wfh['wfhType.color'] || '#3b82f6',
      startDate: wfh.startDate,
      endDate: wfh.endDate,
      duration: parseFloat(wfh.totalDays),
      status: 'pending',
      requestDate: wfh.createdAt,
      reason: wfh.reason
    }));
  }

  /**
   * Build base where conditions for tenant isolation
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Base where conditions
   */
  buildBaseWhere(tenantContext) {
    const where = {
      companyId: tenantContext.companyId
    };

    if (tenantContext.businessUnitId) {
      where.businessUnitId = tenantContext.businessUnitId;
    }

    return where;
  }

  /**
   * Get date range based on period (aligned with leave dashboard)
   * @param {string} period - Period (month, quarter, year)
   * @returns {Object} Date range
   */
  getDateRange(period) {
    const now = moment();
    let start, end;

    switch (period) {
      case 'quarter':
        const quarter = Math.floor(now.month() / 3);
        start = moment().quarter(quarter + 1).startOf('quarter');
        end = moment().quarter(quarter + 1).endOf('quarter');
        break;
      case 'year':
        start = moment().startOf('year');
        end = moment().endOf('year');
        break;
      case 'month':
      default:
        start = moment().startOf('month');
        end = moment().endOf('month');
        break;
    }

    return {
      start: start.format('YYYY-MM-DD'),
      end: end.format('YYYY-MM-DD')
    };
  }

  /**
   * Get WFH types analytics
   * @param {Object} filters - Filter criteria
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} WFH types analytics
   */
  async getWfhTypesAnalytics(filters = {}, tenantContext) {
    const { period = 'month', limit = 5 } = filters;
    const baseWhere = this.buildBaseWhere(tenantContext);
    const dateRange = this.getDateRange(period);

    const wfhTypesData = await this.getTopWfhTypes(baseWhere, dateRange.start, dateRange.end, limit);

    // Get total statistics
    const totalStats = await WorkFromHome.findAll({
      attributes: [
        [fn('COUNT', col('id')), 'totalRequests'],
        [fn('SUM', col('total_days')), 'totalDays'],
        [fn('AVG', col('total_days')), 'avgDuration']
      ],
      where: {
        ...baseWhere,
        startDate: { [Op.between]: [dateRange.start, dateRange.end] }
      },
      raw: true
    });

    const stats = totalStats[0] || {};

    return {
      wfhTypes: wfhTypesData,
      summary: {
        totalRequests: parseInt(stats.totalRequests || 0),
        totalDays: parseInt(stats.totalDays || 0),
        averageDuration: parseFloat(stats.avgDuration || 0).toFixed(1),
        period: period
      }
    };
  }

  /**
   * Get WFH trends
   * @param {Object} filters - Filter criteria
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} WFH trends data
   */
  async getWfhTrends(filters = {}, tenantContext) {
    const { months = 6 } = filters;
    const baseWhere = this.buildBaseWhere(tenantContext);

    const monthlyData = await this.getMonthlyTrend(baseWhere, months);

    // Calculate trend direction
    const recentMonths = monthlyData.slice(-2);
    let trend = 'stable';
    if (recentMonths.length === 2) {
      const [prev, current] = recentMonths;
      if (current.requests > prev.requests) trend = 'increasing';
      else if (current.requests < prev.requests) trend = 'decreasing';
    }

    // Calculate averages
    const totalRequests = monthlyData.reduce((sum, month) => sum + month.requests, 0);
    const avgRequestsPerMonth = monthlyData.length > 0 ? (totalRequests / monthlyData.length).toFixed(1) : 0;

    return {
      monthlyData,
      summary: {
        totalRequests,
        averagePerMonth: parseFloat(avgRequestsPerMonth),
        trend,
        months: months
      }
    };
  }

  /**
   * Get WFH utilization metrics
   * @param {Object} filters - Filter criteria
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} WFH utilization data
   */
  async getWfhUtilization(filters = {}, tenantContext) {
    const { period = 'month' } = filters;
    const baseWhere = this.buildBaseWhere(tenantContext);
    const dateRange = this.getDateRange(period);

    // Get department-wise utilization
    const departmentUtilization = await WorkFromHome.findAll({
      attributes: [
        'departmentId',
        [fn('COUNT', col('WorkFromHome.id')), 'requests'],
        [fn('SUM', col('total_days')), 'totalDays'],
        [fn('COUNT', fn('DISTINCT', col('employee_id'))), 'uniqueEmployees']
      ],
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name'],
          required: false
        }
      ],
      where: {
        ...baseWhere,
        status: 'approved',
        startDate: { [Op.between]: [dateRange.start, dateRange.end] }
      },
      group: ['departmentId', 'department.id', 'department.name'],
      order: [[fn('COUNT', col('WorkFromHome.id')), 'DESC']],
      raw: true
    });

    const formattedDepartments = departmentUtilization.map(dept => ({
      departmentId: dept.departmentId,
      departmentName: dept['department.name'] || 'Unknown Department',
      requests: parseInt(dept.requests),
      totalDays: parseInt(dept.totalDays || 0),
      uniqueEmployees: parseInt(dept.uniqueEmployees),
      avgDaysPerEmployee: dept.uniqueEmployees > 0 ?
        (parseInt(dept.totalDays || 0) / parseInt(dept.uniqueEmployees)).toFixed(1) : 0
    }));

    // Get overall metrics
    const overallMetrics = await this.getKeyMetrics(baseWhere, dateRange.start, dateRange.end);

    return {
      departments: formattedDepartments,
      overall: overallMetrics,
      period: period
    };
  }

  /**
   * Get WFH summary
   * @param {Object} filters - Filter criteria
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} WFH summary data
   */
  async getWfhSummary(filters = {}, tenantContext) {
    const { period = 'week' } = filters;
    const baseWhere = this.buildBaseWhere(tenantContext);

    let dateRange, groupBy, dateFormat;

    if (period === 'week') {
      dateRange = {
        start: moment().subtract(7, 'days').format('YYYY-MM-DD'),
        end: moment().format('YYYY-MM-DD')
      };
      groupBy = fn('DATE', col('start_date'));
      dateFormat = 'YYYY-MM-DD';
    } else {
      dateRange = this.getDateRange('month');
      groupBy = fn('DATE_TRUNC', 'week', col('start_date'));
      dateFormat = 'YYYY-MM-DD';
    }

    const summaryData = await WorkFromHome.findAll({
      attributes: [
        [groupBy, 'period'],
        [fn('COUNT', col('id')), 'totalRequests'],
        [fn('SUM', literal("CASE WHEN status = 'approved' THEN 1 ELSE 0 END")), 'approved'],
        [fn('SUM', literal("CASE WHEN status = 'pending' THEN 1 ELSE 0 END")), 'pending'],
        [fn('SUM', literal("CASE WHEN status = 'rejected' THEN 1 ELSE 0 END")), 'rejected']
      ],
      where: {
        ...baseWhere,
        startDate: { [Op.between]: [dateRange.start, dateRange.end] }
      },
      group: [groupBy],
      order: [[groupBy, 'ASC']],
      raw: true
    });

    const totalRequests = summaryData.reduce((sum, item) => sum + parseInt(item.totalRequests), 0);
    const totalApproved = summaryData.reduce((sum, item) => sum + parseInt(item.approved), 0);
    const approvalRate = totalRequests > 0 ? ((totalApproved / totalRequests) * 100).toFixed(1) : 0;

    return {
      summaryData: summaryData.map(item => ({
        period: moment(item.period).format(period === 'week' ? 'MMM DD' : 'MMM DD'),
        date: moment(item.period).format('YYYY-MM-DD'),
        totalRequests: parseInt(item.totalRequests),
        approved: parseInt(item.approved),
        pending: parseInt(item.pending),
        rejected: parseInt(item.rejected),
        approvalRate: item.totalRequests > 0 ?
          ((parseInt(item.approved) / parseInt(item.totalRequests)) * 100).toFixed(1) : 0
      })),
      summary: {
        totalRequests,
        totalApproved,
        approvalRate: parseFloat(approvalRate),
        period: period
      }
    };
  }

  /**
   * Get WFH analytics
   * @param {Object} filters - Filter criteria
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Advanced WFH analytics
   */
  async getWfhAnalytics(filters = {}, tenantContext) {
    const { period = 'month' } = filters;
    const baseWhere = this.buildBaseWhere(tenantContext);
    const dateRange = this.getDateRange(period);

    const [
      departmentAnalytics,
      wfhTypesAnalytics,
      utilizationMetrics
    ] = await Promise.all([
      this.getWfhUtilization(filters, tenantContext),
      this.getWfhTypesAnalytics(filters, tenantContext),
      this.getKeyMetrics(baseWhere, dateRange.start, dateRange.end)
    ]);

    return {
      departments: departmentAnalytics.departments,
      wfhTypes: wfhTypesAnalytics.wfhTypes,
      utilization: utilizationMetrics,
      period: period
    };
  }

  /**
   * Get employee WFH details
   * @param {Object} filters - Filter criteria
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Employee WFH details
   */
  async getEmployeeDetails(filters = {}, tenantContext) {
    const { limit = 10 } = filters;
    const baseWhere = this.buildBaseWhere(tenantContext);
    const today = moment().format('YYYY-MM-DD');

    // Get employees currently on WFH
    const currentWfh = await WorkFromHome.findAll({
      attributes: ['id', 'startDate', 'endDate', 'totalDays'],
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'firstName', 'lastName'],
          include: [
            {
              model: Department,
              as: 'department',
              attributes: ['name']
            }
          ]
        },
        {
          model: WfhType,
          as: 'wfhType',
          attributes: ['name', 'color']
        }
      ],
      where: {
        ...baseWhere,
        status: 'approved',
        startDate: { [Op.lte]: today },
        endDate: { [Op.gte]: today }
      },
      order: [['startDate', 'ASC']],
      limit,
      raw: true
    });

    // Get recent WFH activity
    const recentActivity = await WorkFromHome.findAll({
      attributes: ['id', 'startDate', 'endDate', 'status', 'createdAt'],
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'firstName', 'lastName'],
          include: [
            {
              model: Department,
              as: 'department',
              attributes: ['name']
            }
          ]
        },
        {
          model: WfhType,
          as: 'wfhType',
          attributes: ['name', 'color']
        }
      ],
      where: baseWhere,
      order: [['createdAt', 'DESC']],
      limit,
      raw: true
    });

    return {
      currentWfh: currentWfh.map(wfh => ({
        id: wfh.id,
        employeeId: wfh['employee.id'],
        employeeName: `${wfh['employee.firstName']} ${wfh['employee.lastName']}`,
        department: wfh['employee.department.name'] || 'Unknown',
        wfhType: wfh['wfhType.name'],
        wfhTypeColor: wfh['wfhType.color'] || '#3b82f6',
        startDate: wfh.startDate,
        endDate: wfh.endDate,
        duration: parseFloat(wfh.totalDays)
      })),
      recentActivity: recentActivity.map(wfh => ({
        id: wfh.id,
        employeeId: wfh['employee.id'],
        employeeName: `${wfh['employee.firstName']} ${wfh['employee.lastName']}`,
        department: wfh['employee.department.name'] || 'Unknown',
        wfhType: wfh['wfhType.name'],
        wfhTypeColor: wfh['wfhType.color'] || '#3b82f6',
        startDate: wfh.startDate,
        endDate: wfh.endDate,
        status: wfh.status,
        requestDate: wfh.createdAt
      }))
    };
  }

  /**
   * Calculate working days between two dates
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {number} Number of working days
   */
  getWorkingDays(startDate, endDate) {
    const start = moment(startDate);
    const end = moment(endDate);
    let workingDays = 0;

    while (start.isSameOrBefore(end)) {
      if (start.day() !== 0 && start.day() !== 6) { // Not Sunday (0) or Saturday (6)
        workingDays++;
      }
      start.add(1, 'day');
    }

    return workingDays;
  }
}

module.exports = WfhDashboardService;
