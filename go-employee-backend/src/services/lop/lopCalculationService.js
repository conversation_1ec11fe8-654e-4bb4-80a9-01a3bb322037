'use strict';

const {
  LOPSettings,
  LOPRecord,
  CustomDeductionType,
  Employee,
  EmployeeSalary,
  Attendance,
  Leave,
  WorkFromHome,
  Holiday,
  BusinessUnit,
  Company,
  sequelize
} = require('../../data/models');

const moment = require('moment-timezone');
const { Op } = require('sequelize');
const logger = require('../../common/logging');
const lopNotificationService = require('./lopNotificationService');

/**
 * Enhanced LOP Calculation Service
 * Builds upon the existing cal.js.js logic with comprehensive business unit support
 */
class LOPCalculationService {
  
  /**
   * 🚀 NEW: Calculate LOP for dynamic date range (month start to yesterday)
   * @param {number} companyId - Company ID
   * @param {number} businessUnitId - Business Unit ID (optional)
   * @param {string} endDate - End date (defaults to yesterday)
   * @returns {Promise<Object>} Calculation results
   */
  async calculateDynamicRangeLOP(companyId, businessUnitId = null, endDate = null) {
    try {
      const targetEndDate = endDate || moment().subtract(1, 'day').format('YYYY-MM-DD');
      const targetStartDate = moment(targetEndDate).startOf('month').format('YYYY-MM-DD');

      logger.info(`🔄 Starting dynamic range LOP calculation for company ${companyId}, BU ${businessUnitId}`);
      logger.info(`📅 Date range: ${targetStartDate} to ${targetEndDate}`);

      // Get LOP settings with business unit hierarchy
      const lopSettings = await this.getLOPSettings(companyId, businessUnitId);
      if (!lopSettings) {
        logger.warn(`⚠️ No LOP settings found for company ${companyId}, BU ${businessUnitId}`);
        return { processed: 0, created: 0, updated: 0, removed: 0, errors: [] };
      }

      // Get employees to process
      const employees = await this.getEmployeesForLOPCalculation(companyId, businessUnitId, targetEndDate);
      logger.info(`👥 Found ${employees.length} employees to process for dynamic LOP calculation`);

      let totalProcessed = 0;
      let totalCreated = 0;
      let totalUpdated = 0;
      let totalRemoved = 0;
      const errors = [];

      // Process each employee for the entire date range
      for (const employee of employees) {
        try {
          const result = await this.processEmployeeRegularizationLOP(
            employee.id,
            targetStartDate,
            targetEndDate,
            lopSettings
          );

          totalProcessed++;
          totalCreated += result.recordsCreated || 0;
          totalUpdated += result.recordsUpdated || 0;
          totalRemoved += result.recordsRemoved || 0;

        } catch (error) {
          logger.error(`❌ Error processing employee ${employee.id}: ${error.message}`);
          errors.push({ employeeId: employee.id, error: error.message });
        }
      }

      logger.info(`✅ Dynamic range LOP calculation completed: ${totalProcessed} employees processed`);
      return {
        processed: totalProcessed,
        created: totalCreated,
        updated: totalUpdated,
        removed: totalRemoved,
        errors
      };

    } catch (error) {
      logger.error(`❌ Dynamic range LOP calculation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Calculate daily LOP for all employees in a company/business unit
   * @param {number} companyId - Company ID
   * @param {number} businessUnitId - Business Unit ID (optional)
   * @param {string} date - Date to calculate LOP for (YYYY-MM-DD)
   * @returns {Promise<Object>} Calculation results
   */
  async calculateDailyLOP(companyId, businessUnitId = null, date = null) {
    try {
      const targetDate = date || moment().subtract(1, 'day').format('YYYY-MM-DD');
      logger.info(`🔄 Starting daily LOP calculation for company ${companyId}, BU ${businessUnitId}, date ${targetDate}`);

      // Get LOP settings with business unit hierarchy
      const lopSettings = await this.getLOPSettings(companyId, businessUnitId);
      if (!lopSettings) {
        logger.warn(`⚠️ No LOP settings found for company ${companyId}, BU ${businessUnitId}`);
        return { processed: 0, created: 0, errors: [] };
      }

      // Get employees to process
      const employees = await this.getEmployeesForLOPCalculation(companyId, businessUnitId, targetDate);
      logger.info(`👥 Found ${employees.length} employees to process for LOP calculation`);

      const results = {
        processed: 0,
        created: 0,
        errors: [],
        details: []
      };

      // Process each employee
      for (const employee of employees) {
        try {
          const employeeResult = await this.calculateEmployeeLOP(
            employee.id,
            targetDate,
            targetDate,
            lopSettings,
            false // Don't apply monthly boundaries for daily cron calculations
          );
          
          results.processed++;
          results.created += employeeResult.recordsCreated;
          results.details.push({
            employeeId: employee.id,
            employeeName: `${employee.firstName} ${employee.lastName}`,
            recordsCreated: employeeResult.recordsCreated,
            totalDeduction: employeeResult.totalDeduction
          });

        } catch (error) {
          logger.error(`❌ Error processing LOP for employee ${employee.id}:`, error);
          results.errors.push({
            employeeId: employee.id,
            error: error.message
          });
        }
      }

      logger.info(`✅ Daily LOP calculation completed. Processed: ${results.processed}, Created: ${results.created}, Errors: ${results.errors.length}`);
      return results;

    } catch (error) {
      logger.error('❌ Daily LOP calculation failed:', error);
      throw error;
    }
  }

  /**
   * Calculate LOP for a specific employee over a date range
   * @param {number} employeeId - Employee ID
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @param {Object} lopSettings - LOP settings (optional, will fetch if not provided)
   * @param {boolean} applyMonthlyBoundaries - Whether to apply monthly boundary rules (default: true)
   * @returns {Promise<Object>} Calculation results
   */
  async calculateEmployeeLOP(employeeId, startDate, endDate, lopSettings = null, applyMonthlyBoundaries = true) {
    try {
      logger.info(`🔄 Calculating LOP for employee ${employeeId} from ${startDate} to ${endDate}`);

      // 🚀 NEW: Enhanced date range calculation with monthly boundary rules (only for monthly calculations)
      if (applyMonthlyBoundaries) {
        const adjustedDateRange = this.calculateMonthlyDateRange(startDate, endDate);
        startDate = adjustedDateRange.startDate;
        endDate = adjustedDateRange.endDate;
      }

      // Get employee details
      const employee = await Employee.findByPk(employeeId, {
        attributes: ['id', 'employeeId', 'firstName', 'lastName', 'joiningDate', 'companyId', 'businessUnitId', 'confirmationDate', 'allotedWfhPerWeek'],
        include: [
          {
            model: EmployeeSalary,
            as: 'salaries',
            attributes: ['id', 'basicSalary', 'grossSalary', 'ctc', 'isActive'],
            where: {
              isActive: true
            },
            required: false
          },
          {
            model: BusinessUnit,
            as: 'businessUnit',
            attributes: ['id', 'name', 'timezone'],
            required: false
          }
        ]
      });

      if (!employee) {
        throw new Error(`Employee ${employeeId} not found`);
      }

      // Check if employee has joined before the calculation period
      if (employee.joiningDate) {
        const joiningDate = moment(employee.joiningDate).format('YYYY-MM-DD');
        const calculationStartDate = moment(startDate).format('YYYY-MM-DD');

        if (joiningDate > calculationStartDate) {
          logger.info(`⏭️ Skipping LOP calculation for employee ${employeeId} - joining date (${joiningDate}) is after calculation start date (${calculationStartDate})`);
          return {
            recordsCreated: 0,
            totalDeduction: 0,
            violations: [],
            message: `Employee joined on ${joiningDate}, calculation period starts from ${calculationStartDate}`
          };
        }

        // Adjust start date if it's before joining date
        if (joiningDate > startDate) {
          startDate = joiningDate;
          logger.info(`📅 Adjusted calculation start date to joining date: ${joiningDate} for employee ${employeeId}`);
        }
      }

      // Get LOP settings if not provided
      if (!lopSettings) {
        lopSettings = await this.getLOPSettings(employee.companyId, employee.businessUnitId);
      }

      if (!lopSettings) {
        throw new Error(`No LOP settings found for employee ${employeeId}`);
      }

      // Check if employee is exempt (probation period, etc.)
      if (await this.isEmployeeExempt(employee, lopSettings)) {
        logger.info(`👤 Employee ${employeeId} is exempt from LOP calculations`);
        return { recordsCreated: 0, totalDeduction: 0, violations: [] };
      }

      // Get attendance data for the period
      const attendanceData = await this.getEmployeeAttendanceData(employeeId, startDate, endDate);
      
      // 🚀 NEW: Detect violations using enhanced logic with priority-based calculation
      const violations = await this.detectAttendanceViolationsEnhanced(
        employee,
        attendanceData,
        lopSettings,
        startDate,
        endDate
      );

      // Calculate deductions with maximum deduction cap per day
      const lopRecords = [];
      let totalDeduction = 0;

      // Group violations by date for deduction cap calculation
      const violationsByDate = violations.reduce((acc, violation) => {
        if (!acc[violation.date]) {
          acc[violation.date] = [];
        }
        acc[violation.date].push(violation);
        return acc;
      }, {});

      // Process each date separately to apply daily deduction cap
      for (const [date, dayViolations] of Object.entries(violationsByDate)) {
        // TODO: Implement salary-based calculation later
        // 🚀 FIXED: Use actual employee daily salary for cap calculation
        const dailySalary = await this.getEmployeeDailySalary(employee);
        let dailyDeductionTotal = 0;
        const dailyRecords = [];

        // Calculate deductions for all violations on this date
        for (const violation of dayViolations) {
          const deductionAmount = await this.calculateDeductionAmount(
            violation,
            employee,
            lopSettings
          );

          if (deductionAmount > 0) {
            dailyDeductionTotal += deductionAmount;
            dailyRecords.push({
              violation,
              deductionAmount,
              originalAmount: deductionAmount
            });
          }
        }

        // 🚀 FIXED: Apply maximum deduction cap (100% of daily salary)
        if (dailyDeductionTotal > dailySalary) {
          logger.info(`⚖️ Daily deduction cap applied for ${date}: Original ₹${dailyDeductionTotal}, Capped at ₹${dailySalary}`);

          // Proportionally reduce deductions to fit within daily salary
          const reductionFactor = dailySalary / dailyDeductionTotal;

          for (const record of dailyRecords) {
            record.deductionAmount = Math.round(record.originalAmount * reductionFactor * 100) / 100;
          }

          dailyDeductionTotal = dailySalary;
        }

        // Create LOP records for this date
        for (const record of dailyRecords) {
          const lopRecord = await this.createLOPRecord(
            employee,
            record.violation,
            record.deductionAmount,
            lopSettings
          );

          lopRecords.push(lopRecord);
          totalDeduction += record.deductionAmount;
        }
      }

      logger.info(`✅ LOP calculation completed for employee ${employeeId}. Records created: ${lopRecords.length}, Total deduction: ${totalDeduction}`);

      return {
        recordsCreated: lopRecords.length,
        totalDeduction,
        violations,
        lopRecords
      };

    } catch (error) {
      logger.error(`❌ Employee LOP calculation failed for ${employeeId}:`, error);
      throw error;
    }
  }

  /**
   * 🚀 NEW: Process employee LOP with regularization updates
   * @param {number} employeeId - Employee ID
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {Object} lopSettings - LOP settings
   * @returns {Promise<Object>} Processing results
   */
  async processEmployeeRegularizationLOP(employeeId, startDate, endDate, lopSettings) {
    try {
      logger.info(`🔄 Processing regularization LOP for employee ${employeeId} from ${startDate} to ${endDate}`);

      // Get existing LOP records for this period
      const existingRecords = await LOPRecord.findAll({
        where: {
          employeeId,
          date: {
            [Op.between]: [startDate, endDate]
          },
          status: { [Op.ne]: 'cancelled' } // Exclude already cancelled records
        }
      });

      // Calculate fresh violations for the period
      const freshResult = await this.calculateEmployeeLOP(employeeId, startDate, endDate, lopSettings, true);

      // Compare and update records
      const updateResult = await this.reconcileLOPRecords(
        employeeId,
        existingRecords,
        freshResult.violations,
        lopSettings
      );

      return {
        recordsCreated: updateResult.created,
        recordsUpdated: updateResult.updated,
        recordsRemoved: updateResult.removed,
        totalDeduction: updateResult.totalDeduction
      };

    } catch (error) {
      logger.error(`❌ Error processing regularization LOP for employee ${employeeId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🚀 NEW: Reconcile existing LOP records with fresh violations
   * @param {number} employeeId - Employee ID
   * @param {Array} existingRecords - Existing LOP records
   * @param {Array} freshViolations - Fresh violations calculated
   * @param {Object} lopSettings - LOP settings
   * @returns {Promise<Object>} Reconciliation results
   */
  async reconcileLOPRecords(employeeId, existingRecords, freshViolations, lopSettings) {
    let created = 0;
    let updated = 0;
    let removed = 0;
    let totalDeduction = 0;

    try {
      // Create maps for easier comparison
      const existingMap = new Map();
      existingRecords.forEach(record => {
        const key = `${record.date}_${record.deductionType}`;
        existingMap.set(key, record);
      });

      const freshMap = new Map();
      freshViolations.forEach(violation => {
        const key = `${violation.date}_${violation.type}`;
        freshMap.set(key, violation);
      });

      // Find records to remove (exist in DB but not in fresh violations)
      for (const [key, record] of existingMap) {
        if (!freshMap.has(key)) {
          await this.removeLOPRecord(record, 'Resolved by regularization');
          removed++;
          logger.info(`🗑️ Removed LOP record: ${record.deductionType} for ${record.date} (regularized)`);
        }
      }

      // Find violations to create (exist in fresh but not in DB)
      for (const [key, violation] of freshMap) {
        if (!existingMap.has(key)) {
          const deductionAmount = await this.calculateDeductionAmount(violation, { id: employeeId }, lopSettings);
          if (deductionAmount > 0) {
            const employee = await Employee.findByPk(employeeId, {
              include: [
                { model: EmployeeSalary, as: 'salaries', where: { isActive: true }, required: false },
                { model: BusinessUnit, as: 'businessUnit', include: [{ model: Company, as: 'company' }] }
              ]
            });

            const lopRecord = await this.createLOPRecord(employee, violation, deductionAmount, lopSettings);
            if (lopRecord) {
              created++;
              totalDeduction += deductionAmount;
              logger.info(`✅ Created new LOP record: ${violation.type} for ${violation.date}, ₹${deductionAmount}`);
            }
          }
        }
      }

      return { created, updated, removed, totalDeduction };

    } catch (error) {
      logger.error(`❌ Error reconciling LOP records for employee ${employeeId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🚀 NEW: Remove LOP record with audit trail
   * @param {Object} lopRecord - LOP record to remove
   * @param {string} reason - Reason for removal
   * @returns {Promise<void>}
   */
  async removeLOPRecord(lopRecord, reason = 'Regularization') {
    try {
      // Update record status to indicate removal
      await lopRecord.update({
        status: 'cancelled',
        notes: `${lopRecord.notes || ''}\n[${moment().format('YYYY-MM-DD HH:mm:ss')}] ${reason}`.trim(),
        metadata: {
          ...lopRecord.metadata,
          cancelledAt: moment().toISOString(),
          cancellationReason: reason
        }
      });

      // Send notification about record removal
      await this.sendRegularizationNotification(lopRecord, 'removed', reason);

      logger.info(`🗑️ LOP record ${lopRecord.id} marked as cancelled: ${reason}`);

    } catch (error) {
      logger.error(`❌ Error removing LOP record ${lopRecord.id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🚀 NEW: Send regularization notification
   * @param {Object} lopRecord - LOP record
   * @param {string} action - Action taken (removed, updated)
   * @param {string} reason - Reason for action
   * @returns {Promise<void>}
   */
  async sendRegularizationNotification(lopRecord, action, reason) {
    try {
      // Get employee details
      const employee = await Employee.findByPk(lopRecord.employeeId, {
        include: [
          { model: Employee, as: 'reportingManager', include: [{ model: Employee, as: 'user' }] },
          { model: BusinessUnit, as: 'businessUnit', include: [{ model: Company, as: 'company' }] }
        ]
      });

      if (!employee) return;

      // Send notification using existing notification service
      await lopNotificationService.sendEmployeeNotification(
        employee,
        lopRecord,
        `LOP Record ${action.charAt(0).toUpperCase() + action.slice(1)}`,
        `LOP record for ${lopRecord.deductionType} on ${lopRecord.date} has been ${action}. Reason: ${reason}`
      );

      await lopNotificationService.sendHRNotification(
        employee,
        lopRecord,
        `LOP Record ${action.charAt(0).toUpperCase() + action.slice(1)}`,
        `LOP record for ${lopRecord.deductionType} on ${lopRecord.date} has been ${action}. Reason: ${reason}`
      );

      await lopNotificationService.sendManagerNotification(
        employee,
        lopRecord,
        `LOP Record ${action.charAt(0).toUpperCase() + action.slice(1)}`,
        `LOP record for ${lopRecord.deductionType} on ${lopRecord.date} has been ${action}. Reason: ${reason}`
      );

    } catch (error) {
      logger.error(`❌ Error sending regularization notification: ${error.message}`);
      // Don't throw error as this is not critical
    }
  }

  /**
   * Get LOP settings with business unit hierarchy
   * @param {number} companyId - Company ID
   * @param {number} businessUnitId - Business Unit ID
   * @returns {Promise<Object>} LOP settings
   */
  async getLOPSettings(companyId, businessUnitId = null) {
    try {
      let lopSettings = null;

      // 1. Try business unit-specific settings first
      if (businessUnitId) {
        lopSettings = await LOPSettings.findOne({
          where: {
            companyId,
            businessUnitId,
            isActive: true,
            effectiveFrom: { [Op.lte]: new Date() },
            [Op.or]: [
              { effectiveTo: null },
              { effectiveTo: { [Op.gte]: new Date() } }
            ]
          }
        });

        if (lopSettings) {
          logger.info(`📋 Using business unit-specific LOP settings for BU ${businessUnitId}`);
          return lopSettings;
        }
      }

      // 2. Try company-level settings
      lopSettings = await LOPSettings.findOne({
        where: {
          companyId,
          businessUnitId: null, // Company-level
          isActive: true,
          effectiveFrom: { [Op.lte]: new Date() },
          [Op.or]: [
            { effectiveTo: null },
            { effectiveTo: { [Op.gte]: new Date() } }
          ]
        }
      });

      if (lopSettings) {
        logger.info(`📋 Using company-level LOP settings for company ${companyId}`);
        return lopSettings;
      }

      logger.warn(`⚠️ No LOP settings found for company ${companyId}, BU ${businessUnitId}`);
      return null;

    } catch (error) {
      logger.error('❌ Error fetching LOP settings:', error);
      throw error;
    }
  }

  /**
   * Get employees for LOP calculation
   * @param {number} companyId - Company ID
   * @param {number} businessUnitId - Business Unit ID
   * @param {string} date - Target date
   * @returns {Promise<Array>} List of employees
   */
  async getEmployeesForLOPCalculation(companyId, businessUnitId, date) {
    const where = {
      companyId,
      status: 'active',
      // Only include employees who joined before the target date
      joiningDate: { [Op.lte]: date }
    };

    if (businessUnitId) {
      where.businessUnitId = businessUnitId;
    }

    return await Employee.findAll({
      where,
      attributes: ['id', 'employeeId', 'firstName', 'lastName', 'joiningDate', 'companyId', 'businessUnitId'],
      include: [
        {
          model: EmployeeSalary,
          as: 'salaries',
          attributes: ['id', 'basicSalary', 'grossSalary', 'ctc', 'isActive'],
          where: { isActive: true },
          required: false
        }
      ]
    });
  }

  /**
   * Check if employee is exempt from LOP calculations
   * @param {Object} employee - Employee object
   * @param {Object} lopSettings - LOP settings
   * @returns {Promise<boolean>} Whether employee is exempt
   */
  async isEmployeeExempt(employee, lopSettings) {
    // 🚀 COMMENTED: Probation period exemption check completely disabled
    // Abhi sabke liye LOP kar rahe hain jo active hain
    /*
    if (lopSettings.probationPeriodExemption) {
      // If confirmationDate is null, employee is still in probation
      if (!employee.confirmationDate) {
        return true;
      }
    }
    */

    // Add other exemption rules here
    return false;
  }

  /**
   * Get comprehensive attendance data for an employee
   * @param {number} employeeId - Employee ID
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {Promise<Object>} Attendance data
   */
  async getEmployeeAttendanceData(employeeId, startDate, endDate) {
    const [attendance, leaves, workFromHomeRequests, holidays] = await Promise.all([
      // Attendance records
      Attendance.findAll({
        where: {
          employeeId,
          date: { [Op.between]: [startDate, endDate] }
        },
        order: [['date', 'ASC']]
      }),

      // Leave records
      Leave.findAll({
        where: {
          employeeId,
          [Op.or]: [
            {
              startDate: { [Op.between]: [startDate, endDate] }
            },
            {
              endDate: { [Op.between]: [startDate, endDate] }
            },
            {
              [Op.and]: [
                { startDate: { [Op.lte]: startDate } },
                { endDate: { [Op.gte]: endDate } }
              ]
            }
          ]
        }
      }),

      // WFH requests
      WorkFromHome.findAll({
        where: {
          employeeId,
          [Op.or]: [
            {
              startDate: { [Op.between]: [startDate, endDate] }
            },
            {
              endDate: { [Op.between]: [startDate, endDate] }
            },
            {
              [Op.and]: [
                { startDate: { [Op.lte]: startDate } },
                { endDate: { [Op.gte]: endDate } }
              ]
            }
          ]
        }
      }),

      // Holidays
      Holiday.findAll({
        where: {
          date: { [Op.between]: [startDate, endDate] }
        }
      })
    ]);

    return {
      attendance,
      leaves,
      wfhRequests: workFromHomeRequests,
      holidays
    };
  }



  /**
   * Detect violations for a specific date
   * @param {string} date - Date to check
   * @param {Object} attendanceRecord - Attendance record
   * @param {boolean} hasApprovedLeave - Has approved leave
   * @param {boolean} hasApprovedWFH - Has approved WFH
   * @param {Object} deductionTypes - Deduction types configuration
   * @param {Object} employee - Employee object
   * @returns {Array} List of violations for the date
   */
  async detectDailyViolations(date, attendanceRecord, hasApprovedLeave, hasApprovedWFH, deductionTypes, employee) {
    const violations = [];

    // Skip if on approved leave
    if (hasApprovedLeave) {
      return violations;
    }

    // 🚀 FIXED: Get employee's actual shift times (with timezone support)
    const shiftTimes = await this.getEmployeeShiftTimes(employee, date);

    // 1. Check for missed check-in (complete absence)
    if (!attendanceRecord && deductionTypes.missed_checkin?.enabled) {
      violations.push({
        type: 'missed_checkin',
        date,
        description: 'Complete absence - no check-in recorded',
        violationDetails: {
          expectedCheckIn: shiftTimes.startTime,
          actualCheckIn: null,
          gracePeriod: deductionTypes.missed_checkin.gracePeriod || 0,
          timezone: shiftTimes.timezone
        }
      });
      return violations; // If no check-in, no need to check other violations
    }

    if (attendanceRecord) {
      // 2. Check for late check-in
      if (deductionTypes.late_checkin?.enabled && attendanceRecord.clockInTime) {
        const violation = this.checkLateCheckIn(attendanceRecord, deductionTypes.late_checkin, date, shiftTimes);
        if (violation) violations.push(violation);
      }

      // 3. Check for missed check-out
      if (deductionTypes.missed_checkout?.enabled && !attendanceRecord.clockOutTime) {
        violations.push({
          type: 'missed_checkout',
          date,
          description: 'Missed checkout - no check-out recorded',
          violationDetails: {
            checkInTime: attendanceRecord.clockInTime,
            expectedCheckOut: shiftTimes.endTime,
            actualCheckOut: null,
            gracePeriod: deductionTypes.missed_checkout.gracePeriod || 0,
            timezone: shiftTimes.timezone
          }
        });
      }

      // 4. Check for insufficient hours
      if (deductionTypes.insufficient_hours?.enabled && attendanceRecord.workHours) {
        const violation = this.checkInsufficientHours(attendanceRecord, deductionTypes.insufficient_hours, date, shiftTimes);
        if (violation) violations.push(violation);
      }

      // 5. Check for unauthorized WFH
      if (deductionTypes.unapproved_wfh?.enabled &&
          attendanceRecord.modeOfWork === 'WFH' && !hasApprovedWFH) {
        violations.push({
          type: 'unapproved_wfh',
          date,
          description: 'Unauthorized work from home',
          violationDetails: {
            modeOfWork: attendanceRecord.modeOfWork,
            hasApprovedWFH: false,
            timezone: shiftTimes.timezone
          }
        });
      }
    }

    return violations;
  }

  /**
   * Check for late check-in violation
   * 🚀 FIXED: Use actual shift times and calculate violation minutes
   */
  checkLateCheckIn(attendanceRecord, lateCheckinConfig, date, shiftTimes) {
    try {
      // Check if already marked as delayed in attendance
      if (attendanceRecord.checkInStatus === 'delayed_in') {
        const actualCheckIn = moment(attendanceRecord.clockInTime);
        const expectedCheckIn = moment(`${date} ${shiftTimes.startTime}`, 'YYYY-MM-DD HH:mm:ss');

        // Calculate violation minutes
        const violationMinutes = actualCheckIn.diff(expectedCheckIn, 'minutes');
        const gracePeriod = lateCheckinConfig.gracePeriod || 15;

        // Only create violation if beyond grace period
        if (violationMinutes > gracePeriod) {
          return {
            type: 'late_checkin',
            date,
            description: `Late check-in by ${violationMinutes} minutes (beyond ${gracePeriod} min grace period)`,
            violationDetails: {
              expectedCheckIn: shiftTimes.startTime,
              actualCheckIn: actualCheckIn.format('HH:mm:ss'),
              gracePeriod,
              violationMinutes,
              timezone: shiftTimes.timezone
            }
          };
        }
      }
      return null;
    } catch (error) {
      console.error(`❌ Error checking late check-in for ${date}:`, error.message);
      return null;
    }
  }

  /**
   * Check for insufficient hours violation
   * 🚀 FIXED: Use shift-based required hours and grace period
   */
  checkInsufficientHours(attendanceRecord, insufficientHoursConfig, date, shiftTimes) {
    try {
      // Get required hours from shift times or config
      const requiredHours = shiftTimes.requiredHours || insufficientHoursConfig.minimumHours || 8;
      const actualHours = parseFloat(attendanceRecord.workHours) || 0;
      const gracePeriod = insufficientHoursConfig.gracePeriod || 30; // 30 minutes grace

      // Calculate shortfall in minutes
      const shortfallMinutes = (requiredHours - actualHours) * 60;

      // Only create violation if shortfall is beyond grace period
      if (actualHours < requiredHours && shortfallMinutes > gracePeriod) {
        return {
          type: 'insufficient_hours',
          date,
          description: `Working ${actualHours} hours, required ${requiredHours} hours (shortfall: ${(shortfallMinutes/60).toFixed(1)} hours)`,
          violationDetails: {
            requiredHours,
            actualHours,
            shortfallHours: requiredHours - actualHours,
            shortfallMinutes,
            gracePeriod,
            timezone: shiftTimes.timezone,
            violationHours: requiredHours - actualHours // For hourly deduction calculation
          }
        };
      }
      return null;
    } catch (error) {
      console.error(`❌ Error checking insufficient hours for ${date}:`, error.message);
      return null;
    }
  }

  /**
   * Calculate deduction amount for a violation
   * 🚀 FIXED: Proper percentage calculation with daily salary
   */
  async calculateDeductionAmount(violation, employee, lopSettings) {
    const deductionConfig = lopSettings.deductionTypes[violation.type];
    if (!deductionConfig || !deductionConfig.enabled) {
      return 0;
    }

    // Get employee's daily salary
    const dailySalary = await this.getEmployeeDailySalary(employee);

    let deductionAmount = 0;
    const amount = parseFloat(deductionConfig.amount) || 0;

    switch (deductionConfig.calculationMethod) {
      case 'percentage':
        // 🚀 FIXED: Calculate percentage of daily salary
        deductionAmount = (dailySalary * amount) / 100;
        break;

      case 'fixed':
        // Fixed amount regardless of salary
        deductionAmount = amount;
        break;

      case 'hourly':
        // Per hour deduction (multiply by violation hours if available)
        const violationHours = violation.violationDetails?.violationHours || 1;
        deductionAmount = amount * violationHours;
        break;

      case 'daily_salary':
        // Full daily salary deduction
        deductionAmount = dailySalary;
        break;

      case 'tiered':
        // Tiered calculation based on violation severity
        deductionAmount = this.calculateTieredDeduction(violation, deductionConfig, dailySalary);
        break;

      default:
        // Default to fixed amount
        deductionAmount = amount;
    }

    return Math.round(deductionAmount * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Calculate tiered deduction amount
   */
  calculateTieredDeduction(violation, deductionConfig, dailySalary) {
    if (!deductionConfig.tiers || !violation.violationDetails.violationMinutes) {
      return 0;
    }

    const violationMinutes = violation.violationDetails.violationMinutes;
    
    for (const tier of deductionConfig.tiers) {
      if (violationMinutes >= tier.min && (tier.max === null || violationMinutes <= tier.max)) {
        return (dailySalary * tier.rate) / 100;
      }
    }

    return 0;
  }

  /**
   * 🚀 FIXED: Calculate delay minutes for late check-in with proper datetime handling
   */
  calculateDelayMinutes(actualCheckIn, expectedCheckIn, date = null) {
    if (!actualCheckIn || !expectedCheckIn) return 0;

    try {
      // Parse actual check-in time (full datetime)
      const actual = moment(actualCheckIn);

      // Create expected check-in datetime
      let expected;
      if (date) {
        // Combine date with expected time
        expected = moment(`${date} ${expectedCheckIn}`, 'YYYY-MM-DD HH:mm:ss');
      } else {
        // Use same date as actual check-in
        const actualDate = actual.format('YYYY-MM-DD');
        expected = moment(`${actualDate} ${expectedCheckIn}`, 'YYYY-MM-DD HH:mm:ss');
      }

      if (actual.isAfter(expected)) {
        const delayMinutes = actual.diff(expected, 'minutes');
        return delayMinutes;
      }

      return 0;
    } catch (error) {
      logger.error(`Error calculating delay minutes: ${error.message}`);
      return 0;
    }
  }

  /**
   * 🚀 NEW: Detect missed checkout using isManualEntry field
   */
  detectMissedCheckout(attendanceRecord) {
    if (!attendanceRecord) return null;

    // Case 1: No checkout time at all
    if (!attendanceRecord.clockOutTime) {
      return {
        checkoutType: 'no_checkout',
        description: 'No checkout time recorded - complete miss'
      };
    }

    // Case 2: Manual checkout (potentially missed auto-checkout)
    if (attendanceRecord.isManualEntry === false) {
      return {
        checkoutType: 'manual_checkout',
        description: 'Manual checkout detected - potentially missed auto-checkout'
      };
    }

    // Case 3: Automatic checkout occurred (no violation)
    return null;
  }

  /**
   * 🚀 NEW: Calculate working hours with break time handling
   */
  calculateWorkingHours(attendanceRecord) {
    if (!attendanceRecord || !attendanceRecord.clockInTime || !attendanceRecord.clockOutTime) {
      return {
        totalHours: 0,
        workingHours: 0,
        breakTime: 0,
        isInsufficient: false
      };
    }

    try {
      const checkIn = moment(attendanceRecord.clockInTime, 'HH:mm:ss');
      const checkOut = moment(attendanceRecord.clockOutTime, 'HH:mm:ss');

      // Calculate total time between check-in and check-out
      const totalMinutes = checkOut.diff(checkIn, 'minutes');
      const totalHours = totalMinutes / 60;

      // Standard break time (60 minutes for now - placeholder for future enhancement)
      const standardBreakMinutes = 60;
      const workingMinutes = Math.max(0, totalMinutes - standardBreakMinutes);
      const workingHours = workingMinutes / 60;

      // Standard required hours (8 hours)
      const requiredHours = 8;
      const isInsufficient = workingHours < requiredHours;

      return {
        totalHours: Math.round(totalHours * 100) / 100,
        workingHours: Math.round(workingHours * 100) / 100,
        breakTime: standardBreakMinutes / 60,
        isInsufficient,
        shortfallHours: isInsufficient ? requiredHours - workingHours : 0
      };
    } catch (error) {
      logger.warn(`Error calculating working hours: ${error.message}`);
      return {
        totalHours: 0,
        workingHours: 0,
        breakTime: 0,
        isInsufficient: false
      };
    }
  }

  /**
   * Get employee's daily salary
   * 🚀 FIXED: Calculate actual daily salary from employee data
   */
  async getEmployeeDailySalary(employee) {
    try {
      // If employee has salary information
      if (employee.salaries && employee.salaries.length > 0) {
        const activeSalary = employee.salaries[0];
        if (activeSalary.basicSalary) {
          // Calculate daily salary: Monthly basic salary / 30 days
          const dailySalary = parseFloat(activeSalary.basicSalary) / 30;
          return Math.round(dailySalary * 100) / 100; // Round to 2 decimal places
        }
      }

      // Fallback: Use default daily salary
      const defaultDailySalary = 1000;
      return defaultDailySalary;
    } catch (error) {
      logger.error(`Error calculating daily salary for employee ${employee.employeeId || employee.id}:`, error.message);
      return 1000; // Fallback amount
    }
  }

  /**
   * Get employee's shift times based on their assigned shift
   * 🚀 UPDATED: Dynamic shift times from ShiftTypeAssignment table with timezone support
   */
  async getEmployeeShiftTimes(employee, date) {
    try {
      // Import models at the top of the file to avoid circular dependency issues
      const db = require('../../data/models');
      const { ShiftTypeAssignment, ShiftType, BusinessUnit, Timezone } = db;

      // Try to get shift assignment for the specific employee only
      let shiftAssignment = await ShiftTypeAssignment.findOne({
        where: {
          companyId: employee.companyId,
          entityType: 'employee',
          entityId: employee.id,
          isActive: true
        },
        include: [{
          model: ShiftType,
          as: 'shiftType',
          include: [{
            model: Timezone,
            as: 'timezone',
            attributes: ['id', 'name', 'offset', 'abbreviation']
          }, {
            model: BusinessUnit,
            as: 'businessUnit',
            attributes: ['id', 'name', 'timezone'] // timezone is a string field, not association
          }]
        }]
      });

      // If shift assignment found, use it
      if (shiftAssignment && shiftAssignment.shiftType) {
        const shift = shiftAssignment.shiftType;

        // Determine timezone priority: shift timezone > business unit timezone > default
        let timezone = 'Asia/Kolkata'; // Default fallback

        if (shift.timezone?.name) {
          timezone = shift.timezone.name;
        } else if (shift.businessUnit?.timezone) {
          timezone = shift.businessUnit.timezone;
        } else if (employee.businessUnit?.timezone) {
          timezone = employee.businessUnit.timezone;
        }

        // Calculate required hours
        const startMoment = moment(shift.startTime, 'HH:mm:ss');
        const endMoment = moment(shift.endTime, 'HH:mm:ss');

        // Handle overnight shifts
        if (endMoment.isBefore(startMoment)) {
          endMoment.add(1, 'day');
        }

        const requiredHours = endMoment.diff(startMoment, 'hours', true);
        const breakHours = (shift.breakDuration || 0) / 60; // Convert minutes to hours
        const workingHours = Math.max(0, requiredHours - breakHours);

        return {
          startTime: shift.startTime,
          endTime: shift.endTime,
          timezone: timezone,
          requiredHours: Math.round(workingHours * 100) / 100, // Round to 2 decimal places
          breakDuration: shift.breakDuration || 0,
          shiftName: shift.name,
          shiftId: shift.id,
          assignmentType: shiftAssignment.entityType,
          source: 'shift_assignment'
        };
      }

      // Fallback: Use employee's direct shift assignment (legacy)
      if (employee.shiftTypeId) {
        const directShift = await ShiftType.findByPk(employee.shiftTypeId, {
          include: [{
            model: Timezone,
            as: 'timezone',
            attributes: ['id', 'name', 'offset', 'abbreviation']
          }, {
            model: BusinessUnit,
            as: 'businessUnit',
            attributes: ['id', 'name', 'timezone'] // timezone is a string field
          }]
        });

        if (directShift) {
          let timezone = 'Asia/Kolkata';

          if (directShift.timezone?.name) {
            timezone = directShift.timezone.name;
          } else if (directShift.businessUnit?.timezone) {
            timezone = directShift.businessUnit.timezone;
          } else if (employee.businessUnit?.timezone) {
            timezone = employee.businessUnit.timezone;
          }

          const startMoment = moment(directShift.startTime, 'HH:mm:ss');
          const endMoment = moment(directShift.endTime, 'HH:mm:ss');

          if (endMoment.isBefore(startMoment)) {
            endMoment.add(1, 'day');
          }

          const requiredHours = endMoment.diff(startMoment, 'hours', true);
          const breakHours = (directShift.breakDuration || 0) / 60;
          const workingHours = Math.max(0, requiredHours - breakHours);

          return {
            startTime: directShift.startTime,
            endTime: directShift.endTime,
            timezone: timezone,
            requiredHours: Math.round(workingHours * 100) / 100,
            breakDuration: directShift.breakDuration || 0,
            shiftName: directShift.name,
            shiftId: directShift.id,
            assignmentType: 'direct',
            source: 'employee_shift'
          };
        }
      }

      // Final fallback: Default shift times with business unit timezone
      let fallbackTimezone = 'Asia/Kolkata';
      if (employee.businessUnit?.timezone) {
        fallbackTimezone = employee.businessUnit.timezone;
      }

      return {
        startTime: '09:00:00',
        endTime: '18:00:00',
        timezone: fallbackTimezone,
        requiredHours: 9,
        breakDuration: 60,
        shiftName: 'Default Shift',
        shiftId: null,
        assignmentType: 'default',
        source: 'fallback'
      };

    } catch (error) {
      logger.error(`Error getting shift times for employee ${employee.employeeId || employee.id}:`, error.message);

      // Return default shift times on error
      return {
        startTime: '09:00:00',
        endTime: '18:00:00',
        timezone: 'Asia/Kolkata',
        requiredHours: 9,
        breakDuration: 60,
        shiftName: 'Default Shift',
        shiftId: null,
        assignmentType: 'error',
        source: 'error_fallback'
      };
    }
  }

  /**
   * Create LOP record
   */
  async createLOPRecord(employee, violation, deductionAmount, lopSettings) {
    // TODO: Implement salary-based calculation later
    const dailySalary = 1000; // Fixed amount for now
    // const dailySalary = await this.getEmployeeDailySalary(employee);

    // 🚀 Check for existing record to prevent duplicates
    const existingRecord = await LOPRecord.findOne({
      where: {
        companyId: employee.companyId,
        businessUnitId: employee.businessUnitId,
        employeeId: employee.id,
        date: violation.date,
        deductionType: violation.type
      }
    });

    if (existingRecord) {
      logger.info(`⏭️ LOP record already exists for employee ${employee.id} on ${violation.date} for ${violation.type}`);
      return existingRecord;
    }

    const lopRecord = await LOPRecord.create({
      companyId: employee.companyId,
      businessUnitId: employee.businessUnitId,
      employeeId: employee.id,
      lopSettingsId: lopSettings.id,
      date: violation.date,
      deductionType: violation.type,
      deductionCategory: 'standard',
      violationDetails: violation.violationDetails,
      calculationMethod: lopSettings.deductionTypes[violation.type].calculationMethod,
      baseAmount: dailySalary,
      calculationRate: lopSettings.deductionTypes[violation.type].amount,
      deductionAmount,
      originalAmount: deductionAmount,
      status: lopSettings.autoApprovalEnabled && deductionAmount <= lopSettings.autoApprovalThreshold ? 'approved' : 'pending',
      approvalLevel: lopSettings.autoApprovalEnabled && deductionAmount <= lopSettings.autoApprovalThreshold ? 'auto' : null,
      notes: violation.description
    });

    // Send notification if enabled
    try {
      // await lopNotificationService.notifyLOPCalculated(lopRecord, lopSettings);
    } catch (notificationError) {
      logger.warn('⚠️ Failed to send LOP calculation notification:', notificationError);
    }

    return lopRecord;
  }

  /**
   * Calculate monthly date range with boundary rules
   * @param {string} startDate - Original start date
   * @param {string} endDate - Original end date
   * @returns {Object} Adjusted date range object
   */
  calculateMonthlyDateRange(startDate, endDate) {
    const start = moment(startDate);
    const end = moment(endDate);
    const today = moment();

    // Ensure we're working within month boundaries
    const startMonth = start.format('YYYY-MM');
    const endMonth = end.format('YYYY-MM');
    const currentMonth = today.format('YYYY-MM');

    // If date range spans multiple months, limit to current month being processed
    if (startMonth !== endMonth) {
      // Limit to the month of startDate
      const adjustedEndDate = start.clone().endOf('month').format('YYYY-MM-DD');
      return {
        startDate: start.startOf('month').format('YYYY-MM-DD'),
        endDate: adjustedEndDate
      };
    }

    // For current month, calculate up to previous day (unless today is last day of month)
    if (startMonth === currentMonth) {
      const monthStart = today.clone().startOf('month');
      const isLastDayOfMonth = today.isSame(today.clone().endOf('month'), 'day');
      const isFirstDayOfMonth = today.isSame(monthStart, 'day');

      if (isFirstDayOfMonth) {
        // If today is 1st of the month, no LOP calculation yet (no previous days)
        return {
          startDate: monthStart.format('YYYY-MM-DD'),
          endDate: monthStart.format('YYYY-MM-DD') // Same day, will result in no working days
        };
      } else if (isLastDayOfMonth) {
        // Include today if it's the last day of the month
        return {
          startDate: monthStart.format('YYYY-MM-DD'),
          endDate: today.format('YYYY-MM-DD')
        };
      } else {
        // Calculate up to previous day
        return {
          startDate: monthStart.format('YYYY-MM-DD'),
          endDate: today.clone().subtract(1, 'day').format('YYYY-MM-DD')
        };
      }
    }

    // For past/future months, use complete month
    return {
      startDate: start.startOf('month').format('YYYY-MM-DD'),
      endDate: start.clone().endOf('month').format('YYYY-MM-DD')
    };
  }

  /**
   * Enhanced violation detection with priority-based calculation and weekly WFH quota
   * @param {Object} employee - Employee object
   * @param {Object} attendanceData - Attendance data
   * @param {Object} lopSettings - LOP settings
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {Promise<Array>} Enhanced violations list
   */
  async detectAttendanceViolationsEnhanced(employee, attendanceData, lopSettings, startDate, endDate) {
    const violations = [];
    const deductionTypes = lopSettings.deductionTypes;

    // Get holidays for the period
    const holidays = await Holiday.findAll({
      where: {
        companyId: employee.companyId,
        date: {
          [Op.between]: [startDate, endDate]
        }
      }
    });

    const holidayDates = new Set(holidays.map(h => moment(h.date).format('YYYY-MM-DD')));

    // 🚀 NEW: Priority-based violation detection
    // Step 1: Detect 100% deduction violations first
    const highPriorityViolations = await this.detectHighPriorityViolations(
      employee, attendanceData, deductionTypes, startDate, endDate, holidayDates
    );

    // Step 2: Detect other violations (only for dates without 100% deductions)
    const lowPriorityViolations = await this.detectLowPriorityViolations(
      employee, attendanceData, deductionTypes, startDate, endDate, holidayDates, highPriorityViolations
    );

    // Combine all violations
    violations.push(...highPriorityViolations, ...lowPriorityViolations);

    // 🚀 NEW: Apply weekly WFH quota logic
    const finalViolations = await this.applyWeeklyWFHQuotaEnhanced(violations, employee, attendanceData, startDate, endDate);

    return finalViolations;
  }

  /**
   * Enhanced violation detection with weekly WFH quota and priority system
   * @param {Object} employee - Employee object
   * @param {Object} attendanceData - Attendance data
   * @param {Object} lopSettings - LOP settings
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {Promise<Array>} Enhanced violations list
   */
  async detectAttendanceViolations(employee, attendanceData, lopSettings, startDate, endDate) {
    const violations = [];
    const deductionTypes = lopSettings.deductionTypes;

    // Get holidays for the period
    const holidays = await Holiday.findAll({
      where: {
        companyId: employee.companyId,
        date: {
          [Op.between]: [startDate, endDate]
        }
      }
    });

    const holidayDates = new Set(holidays.map(h => moment(h.date).format('YYYY-MM-DD')));

    // Process each date in the period
    const currentDate = moment(startDate);
    const endMoment = moment(endDate);

    while (currentDate.isSameOrBefore(endMoment)) {
      const dateStr = currentDate.format('YYYY-MM-DD');

      // Skip weekends and holidays (static week-off days: Sunday=0, Saturday=6)
      if (this.isWorkingDay(currentDate, [0, 6], holidayDates)) {
        // Check for approved leave
        const hasApprovedLeave = attendanceData.leaves.some(leave =>
          moment(dateStr).isBetween(leave.fromDate, leave.toDate, null, '[]') &&
          leave.status === 'approved'
        );

        // Check for approved WFH
        const hasApprovedWFH = attendanceData.wfhRequests.some(wfh =>
          moment(dateStr).isBetween(wfh.fromDate, wfh.toDate, null, '[]') &&
          wfh.status === 'approved'
        );

        // Get attendance record for this date
        const attendanceRecord = attendanceData.attendance.find(att =>
          moment(att.date).format('YYYY-MM-DD') === dateStr
        );

        // Detect violations for this date
        const dateViolations = await this.detectDailyViolations(
          dateStr,
          attendanceRecord,
          hasApprovedLeave,
          hasApprovedWFH,
          deductionTypes,
          employee
        );

        violations.push(...dateViolations);
      }

      currentDate.add(1, 'day');
    }

    // Apply weekly WFH quota logic
    const enhancedViolations = await this.applyWeeklyWFHQuota(violations, employee, attendanceData, startDate, endDate);

    // Apply violation priority system and deduction cap
    const finalViolations = this.applyViolationPriorityAndCap(enhancedViolations);

    return finalViolations;
  }

  /**
   * Check if a date is a working day
   * @param {moment} date - Date to check
   * @param {Array} weekOffDays - Week off days (0=Sunday, 6=Saturday) - static for now
   * @param {Set} holidayDates - Set of holiday dates
   * @returns {boolean} True if working day
   */
  isWorkingDay(date, weekOffDays = [0, 6], holidayDates = new Set()) {
    const dayOfWeek = date.day();
    const dateStr = date.format('YYYY-MM-DD');

    // Static week-off days: Sunday (0) and Saturday (6)
    const staticWeekOffDays = [0, 6];
    return !staticWeekOffDays.includes(dayOfWeek) && !holidayDates.has(dateStr);
  }

  /**
   * Apply weekly WFH quota logic to remove violations within allowance
   * @param {Array} violations - List of violations
   * @param {Object} employee - Employee object
   * @param {Object} attendanceData - Attendance data
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {Promise<Array>} Filtered violations
   */
  async applyWeeklyWFHQuota(violations, employee, attendanceData, startDate, endDate) {
    const weeklyWFHQuota = employee.allotedWfhPerWeek || 2; // Default 2 days per week
    const filteredViolations = [];

    // Group violations by week and type
    const weeklyGroups = {};

    violations.forEach(violation => {
      const weekStart = moment(violation.date).startOf('week').format('YYYY-MM-DD');

      if (!weeklyGroups[weekStart]) {
        weeklyGroups[weekStart] = {
          unapproved_wfh: [],
          other: []
        };
      }

      if (violation.type === 'unapproved_wfh') {
        weeklyGroups[weekStart].unapproved_wfh.push(violation);
      } else {
        weeklyGroups[weekStart].other.push(violation);
      }
    });

    // Process each week
    Object.keys(weeklyGroups).forEach(weekStart => {
      const weekGroup = weeklyGroups[weekStart];

      // Add all non-WFH violations
      filteredViolations.push(...weekGroup.other);

      // Apply WFH quota logic
      const unapprovedWFHViolations = weekGroup.unapproved_wfh;

      if (unapprovedWFHViolations.length > weeklyWFHQuota) {
        // Keep violations that exceed the weekly quota
        // Sort by date and keep the excess violations
        const sortedWFHViolations = unapprovedWFHViolations.sort((a, b) =>
          moment(a.date).diff(moment(b.date))
        );

        // Keep violations beyond the quota
        const excessViolations = sortedWFHViolations.slice(weeklyWFHQuota);
        filteredViolations.push(...excessViolations);

        logger.info(`📊 Applied WFH quota for week ${weekStart}: ${unapprovedWFHViolations.length} violations, ${excessViolations.length} kept after quota (${weeklyWFHQuota} allowed)`);
      } else {
        // All WFH violations are within quota, remove them
        logger.info(`✅ All WFH violations for week ${weekStart} are within quota (${unapprovedWFHViolations.length}/${weeklyWFHQuota})`);
      }
    });

    return filteredViolations;
  }

  /**
   * Apply violation priority system and maximum deduction cap
   * @param {Array} violations - List of violations
   * @returns {Array} Prioritized violations with deduction cap
   */
  applyViolationPriorityAndCap(violations) {
    // Define violation priority order (1 = highest priority)
    const priorityOrder = {
      'missed_checkin': 1,        // Highest priority - 100% deduction
      'unapproved_leave': 2,      // 100% deduction
      'unapproved_wfh': 3,        // 100% deduction
      'insufficient_hours': 4,    // Variable deduction
      'missed_checkout': 5,       // Fixed or percentage deduction
      'late_checkin': 6           // Lowest priority - tiered deduction
    };

    // Group violations by date
    const violationsByDate = violations.reduce((acc, violation) => {
      if (!acc[violation.date]) {
        acc[violation.date] = [];
      }
      acc[violation.date].push(violation);
      return acc;
    }, {});

    const finalViolations = [];

    // Process each date
    Object.keys(violationsByDate).forEach(date => {
      const dayViolations = violationsByDate[date];

      if (dayViolations.length === 1) {
        // Single violation, keep as is
        finalViolations.push(dayViolations[0]);
      } else {
        // Multiple violations for same date - apply priority logic

        // Sort by priority (lower number = higher priority)
        const sortedViolations = dayViolations.sort((a, b) => {
          const priorityA = priorityOrder[a.type] || 999;
          const priorityB = priorityOrder[b.type] || 999;
          return priorityA - priorityB;
        });

        // Check if highest priority violation is 100% deduction
        const highestPriorityViolation = sortedViolations[0];
        const isFullDeduction = ['missed_checkin', 'unapproved_leave', 'unapproved_wfh'].includes(highestPriorityViolation.type);

        if (isFullDeduction) {
          // If highest priority is 100% deduction, only keep that one
          finalViolations.push(highestPriorityViolation);

          logger.info(`🔄 Applied priority logic for ${date}: Kept ${highestPriorityViolation.type} (100% deduction), removed ${dayViolations.length - 1} lower priority violations`);
        } else {
          // Keep all violations but ensure total doesn't exceed 100%
          // This will be handled in the deduction calculation phase
          finalViolations.push(...sortedViolations);

          logger.info(`⚖️ Multiple violations for ${date}: ${dayViolations.length} violations kept with deduction cap`);
        }
      }
    });

    return finalViolations;
  }

  /**
   * Detect high priority violations (100% deductions)
   * @param {Object} employee - Employee object
   * @param {Object} attendanceData - Attendance data
   * @param {Object} deductionTypes - Deduction types configuration
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {Set} holidayDates - Set of holiday dates
   * @returns {Promise<Array>} High priority violations
   */
  async detectHighPriorityViolations(employee, attendanceData, deductionTypes, startDate, endDate, holidayDates) {
    const violations = [];
    const currentDate = moment(startDate);
    const endMoment = moment(endDate);

    while (currentDate.isSameOrBefore(endMoment)) {
      const dateStr = currentDate.format('YYYY-MM-DD');

      // Skip weekends and holidays
      if (this.isWorkingDay(currentDate, [0, 6], holidayDates)) {
        // Check for approved leave
        const hasApprovedLeave = attendanceData.leaves.some(leave =>
          moment(dateStr).isBetween(leave.fromDate, leave.toDate, null, '[]') &&
          leave.status === 'approved'
        );

        // Check for approved WFH
        const hasApprovedWFH = attendanceData.wfhRequests.some(wfh =>
          moment(dateStr).isBetween(wfh.fromDate, wfh.toDate, null, '[]') &&
          wfh.status === 'approved'
        );

        // Get attendance record for this date
        const attendanceRecord = attendanceData.attendance.find(att =>
          moment(att.date).format('YYYY-MM-DD') === dateStr
        );

        // 🚀 NEW: Priority-based violation detection
        // 100% deductions: missed_checkin, unapproved_leave, unapproved_wfh
        if (!hasApprovedLeave && !hasApprovedWFH && !attendanceRecord) {
          // Complete absence - missed check-in (100% deduction)
          if (deductionTypes.missed_checkin?.enabled) {
            violations.push({
              employeeId: employee.id,
              date: dateStr,
              type: 'missed_checkin',
              violationType: 'missed_checkin',
              description: 'Complete absence - no check-in recorded',
              violationDetails: {
                expectedCheckIn: '09:00:00',
                actualCheckIn: null,
                absenceType: 'complete'
              },
              deductionAmount: parseFloat(deductionTypes.missed_checkin.amount) || 100,
              priority: 1 // Highest priority
            });
          }
        } else if (!hasApprovedLeave && !hasApprovedWFH && attendanceRecord) {
          // 🚀 UPDATED: Check for unauthorized WFH using modeOfWork field
          if (attendanceRecord.modeOfWork === 'WFH' && deductionTypes.unapproved_wfh?.enabled) {
            violations.push({
              employeeId: employee.id,
              date: dateStr,
              type: 'unapproved_wfh',
              violationType: 'unapproved_wfh',
              description: 'Unauthorized work from home - WFH mode without approval',
              violationDetails: {
                modeOfWork: attendanceRecord.modeOfWork,
                approvalStatus: 'not_approved',
                hasApprovedWFH: false,
                checkInTime: attendanceRecord.clockInTime,
                checkOutTime: attendanceRecord.clockOutTime
              },
              deductionAmount: parseFloat(deductionTypes.unapproved_wfh.amount) || 100,
              priority: 3 // High priority as per requirements
            });
          }
        } else if (!hasApprovedLeave && hasApprovedWFH) {
          // This is handled in WFH quota logic
        }

        // Check for unauthorized leave (present but should be on leave)
        if (!hasApprovedLeave && !attendanceRecord && deductionTypes.unapproved_leave?.enabled) {
          // This is already covered by missed_checkin above
        }
      }

      currentDate.add(1, 'day');
    }

    return violations;
  }

  /**
   * Detect low priority violations (partial deductions)
   * @param {Object} employee - Employee object
   * @param {Object} attendanceData - Attendance data
   * @param {Object} deductionTypes - Deduction types configuration
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {Set} holidayDates - Set of holiday dates
   * @param {Array} highPriorityViolations - Already detected high priority violations
   * @returns {Promise<Array>} Low priority violations
   */
  async detectLowPriorityViolations(employee, attendanceData, deductionTypes, startDate, endDate, holidayDates, highPriorityViolations) {
    const violations = [];
    const currentDate = moment(startDate);
    const endMoment = moment(endDate);

    // Create a set of dates with 100% deductions to avoid double penalties
    const highPriorityDates = new Set(highPriorityViolations.map(v => v.date));

    while (currentDate.isSameOrBefore(endMoment)) {
      const dateStr = currentDate.format('YYYY-MM-DD');

      // Skip if this date already has a 100% deduction
      if (highPriorityDates.has(dateStr)) {
        currentDate.add(1, 'day');
        continue;
      }

      // Skip weekends and holidays
      if (this.isWorkingDay(currentDate, [0, 6], holidayDates)) {
        // Check for approved leave
        const hasApprovedLeave = attendanceData.leaves.some(leave =>
          moment(dateStr).isBetween(leave.fromDate, leave.toDate, null, '[]') &&
          leave.status === 'approved'
        );

        // Check for approved WFH
        const hasApprovedWFH = attendanceData.wfhRequests.some(wfh =>
          moment(dateStr).isBetween(wfh.fromDate, wfh.toDate, null, '[]') &&
          wfh.status === 'approved'
        );

        // Get attendance record for this date
        const attendanceRecord = attendanceData.attendance.find(att =>
          moment(att.date).format('YYYY-MM-DD') === dateStr
        );

        // Only process if employee is present (has attendance record)
        if (attendanceRecord && !hasApprovedLeave) {
          // 🚀 UPDATED: Late check-in detection using checkInStatus field
          if (attendanceRecord.checkInStatus === 'delayed_in' && deductionTypes.late_checkin?.enabled) {
            // Calculate actual delay minutes with proper date handling
            const delayMinutes = this.calculateDelayMinutes(attendanceRecord.clockInTime, '09:00:00', dateStr);

            violations.push({
              employeeId: employee.id,
              date: dateStr,
              type: 'late_checkin',
              violationType: 'late_checkin',
              description: 'Late check-in detected via checkInStatus field',
              violationDetails: {
                actualCheckIn: attendanceRecord.clockInTime,
                expectedCheckIn: '09:00:00', // TODO: Get from shift settings
                delayMinutes: delayMinutes,
                violationMinutes: delayMinutes, // 🚀 ADDED: For tiered calculation
                gracePeriod: deductionTypes.late_checkin.gracePeriod || 15,
                checkInStatus: attendanceRecord.checkInStatus,
                modeOfWork: attendanceRecord.modeOfWork || 'Unknown'
              },
              deductionAmount: parseFloat(deductionTypes.late_checkin.amount) || 20,
              priority: 6 // Lowest priority as per requirements
            });
          }

          // 🚀 UPDATED: Missed checkout detection using isManualEntry field
          if (deductionTypes.missed_checkout?.enabled) {
            const hasMissedCheckout = this.detectMissedCheckout(attendanceRecord);

            if (hasMissedCheckout) {
              violations.push({
                employeeId: employee.id,
                date: dateStr,
                type: 'missed_checkout',
                violationType: 'missed_checkout',
                description: hasMissedCheckout.description,
                violationDetails: {
                  checkInTime: attendanceRecord.clockInTime,
                  clockOutTime: attendanceRecord.clockOutTime,
                  expectedCheckOut: '18:00:00', // TODO: Get from shift settings
                  isManualEntry: attendanceRecord.isManualEntry,
                  checkoutType: hasMissedCheckout.checkoutType,
                  modeOfWork: attendanceRecord.modeOfWork || 'Unknown',
                  gracePeriod: deductionTypes.missed_checkout.gracePeriod || 0
                },
                deductionAmount: parseFloat(deductionTypes.missed_checkout.amount) || 50,
                priority: 5 // Medium priority
              });
            }
          }

          // 🚀 UPDATED: Insufficient hours detection with break time handling
          if (deductionTypes.insufficient_hours?.enabled) {
            const workingHoursData = this.calculateWorkingHours(attendanceRecord);

            if (workingHoursData.isInsufficient) {
              violations.push({
                employeeId: employee.id,
                date: dateStr,
                type: 'insufficient_hours',
                violationType: 'insufficient_hours',
                description: `Insufficient work hours: ${workingHoursData.workingHours}/8 hours (after ${workingHoursData.breakTime}h break)`,
                violationDetails: {
                  requiredHours: 8,
                  actualWorkingHours: workingHoursData.workingHours,
                  totalHours: workingHoursData.totalHours,
                  breakTime: workingHoursData.breakTime,
                  shortfallHours: workingHoursData.shortfallHours,
                  checkInTime: attendanceRecord.clockInTime,
                  checkOutTime: attendanceRecord.clockOutTime,
                  modeOfWork: attendanceRecord.modeOfWork || 'Unknown'
                },
                deductionAmount: parseFloat(deductionTypes.insufficient_hours.amount) || 50,
                priority: 4 // Medium priority as per requirements
              });
            }
          }
        }
      }

      currentDate.add(1, 'day');
    }

    return violations;
  }

  /**
   * Apply weekly WFH quota logic with enhanced boundary handling
   * @param {Array} violations - All detected violations
   * @param {Object} employee - Employee object
   * @param {Object} attendanceData - Attendance data
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {Promise<Array>} Filtered violations
   */
  async applyWeeklyWFHQuotaEnhanced(violations, employee, attendanceData, startDate, endDate) {
    const weeklyWFHQuota = employee.allotedWfhPerWeek || 1; // Default 1 day per week
    const filteredViolations = [...violations]; // Start with all violations

    // Group WFH violations by week
    const wfhViolations = violations.filter(v => v.violationType === 'unapproved_wfh');
    const wfhByWeek = new Map();

    wfhViolations.forEach(violation => {
      const date = moment(violation.date);
      const weekStart = date.clone().startOf('isoWeek'); // Monday
      const weekEnd = date.clone().endOf('isoWeek'); // Sunday

      // Ensure week boundaries don't cross month boundaries
      const monthStart = moment(startDate).startOf('month');
      const monthEnd = moment(endDate).endOf('month');

      const adjustedWeekStart = moment.max(weekStart, monthStart);
      const adjustedWeekEnd = moment.min(weekEnd, monthEnd);

      const weekKey = `${adjustedWeekStart.format('YYYY-MM-DD')}_${adjustedWeekEnd.format('YYYY-MM-DD')}`;

      if (!wfhByWeek.has(weekKey)) {
        wfhByWeek.set(weekKey, []);
      }
      wfhByWeek.get(weekKey).push(violation);
    });

    // Process each week
    for (const [weekKey, weekViolations] of wfhByWeek) {
      const [weekStartStr, weekEndStr] = weekKey.split('_');

      // Count approved WFH days in this week
      const approvedWFHDays = attendanceData.wfhRequests.filter(wfh => {
        if (wfh.status !== 'approved') return false;

        const wfhStart = moment(wfh.fromDate);
        const wfhEnd = moment(wfh.toDate);
        const weekStart = moment(weekStartStr);
        const weekEnd = moment(weekEndStr);

        // Check if WFH overlaps with this week
        return wfhStart.isSameOrBefore(weekEnd) && wfhEnd.isSameOrAfter(weekStart);
      }).length;

      // Calculate violations to keep
      const totalWFHDays = approvedWFHDays + weekViolations.length;
      const violationsToKeep = Math.max(0, totalWFHDays - weeklyWFHQuota);

      if (violationsToKeep < weekViolations.length) {
        // Remove some violations (employee gets benefit of quota)
        const violationsToRemove = weekViolations.slice(violationsToKeep);
        violationsToRemove.forEach(violation => {
          const index = filteredViolations.findIndex(v =>
            v.date === violation.date && v.violationType === violation.violationType
          );
          if (index > -1) {
            filteredViolations.splice(index, 1);
          }
        });

      }
    }

    return filteredViolations;
  }
}

module.exports = new LOPCalculationService();
