'use strict';

const cron = require('node-cron');
const {
  Company,
  BusinessUnit,
  Employee,
  AttendanceSettings,
  Attendance,
  OvertimeRequest
} = require('../../data/models');
const OvertimeCalculationService = require('./overtimeCalculationService');
const OvertimeWorkflowService = require('./overtimeWorkflowService');
const logger = require('../../common/logging');
const moment = require('moment-timezone');
const { Op } = require('sequelize');

/**
 * Overtime Cron Service
 * Handles automated overtime calculation scheduling and execution
 */
class OvertimeCronService {
  constructor() {
    this.overtimeCalculationService = new OvertimeCalculationService();
    this.overtimeWorkflowService = new OvertimeWorkflowService();
    this.scheduledJobs = new Map(); // Track scheduled cron jobs
  }

  /**
   * Initialize overtime cron jobs for all companies
   */
  async initializeOvertimeCronJobs() {
    try {
      logger.info('🚀 Initializing overtime cron jobs...');

      // Get all active companies with their attendance settings (which include overtime settings)
      const companies = await Company.findAll({
        where: { status: 'active' },
        include: [
          {
            model: AttendanceSettings,
            as: 'attendanceSettings',
            required: false
          }
        ]
      });

      for (const company of companies) {
        await this.scheduleCompanyOvertimeCron(company);
      }

      logger.info(`✅ Initialized overtime cron jobs for ${companies.length} companies`);

    } catch (error) {
      logger.error('❌ Error initializing overtime cron jobs:', error);
      throw error;
    }
  }

  /**
   * Schedule overtime calculation cron for a specific company
   * @param {Object} company - Company object with attendance settings
   */
  async scheduleCompanyOvertimeCron(company) {
    try {
      const attendanceSettings = company.attendanceSettings;
      
      // Skip if overtime is not enabled
      if (!attendanceSettings || !attendanceSettings.overtimeEnabled) {
        logger.info(`⏭️  Skipping overtime cron for company ${company.id} - overtime disabled`);
        return;
      }

      // Get cron schedule (default: daily at 2 AM)
      const cronSchedule = attendanceSettings.overtimeCronSchedule || '0 2 * * *';
      const calculationMethod = attendanceSettings.overtimeCalculationSchedule || 'daily';

      // Create unique job key
      const jobKey = `overtime_${company.id}`;

      // Stop existing job if it exists
      if (this.scheduledJobs.has(jobKey)) {
        this.scheduledJobs.get(jobKey).stop();
        this.scheduledJobs.delete(jobKey);
      }

      // Schedule new cron job
      const cronJob = cron.schedule(cronSchedule, async () => {
        await this.runOvertimeCalculationForCompany(company.id, calculationMethod);
      }, {
        scheduled: true,
        timezone: company.timezone || 'UTC'
      });

      // Store the job reference
      this.scheduledJobs.set(jobKey, cronJob);

      logger.info(`📅 Scheduled overtime cron for company ${company.id} with schedule: ${cronSchedule} (${calculationMethod})`);

    } catch (error) {
      logger.error(`❌ Error scheduling overtime cron for company ${company.id}:`, error);
    }
  }

  /**
   * Run overtime calculation for a specific company
   * @param {number} companyId - Company ID
   * @param {string} calculationMethod - Calculation method (daily, weekly, monthly)
   */
  async runOvertimeCalculationForCompany(companyId, calculationMethod = 'daily') {
    try {
      logger.info(`🔄 Running ${calculationMethod} overtime calculation for company ${companyId}`);

      // Get company with business units
      const company = await Company.findOne({
        where: { id: companyId },
        include: [
          {
            model: BusinessUnit,
            as: 'businessUnits',
            where: { status: 'active' },
            required: false
          }
        ]
      });

      if (!company) {
        logger.error(`❌ Company ${companyId} not found`);
        return;
      }

      // Calculate date range based on method
      const dateRange = this.getCalculationDateRange(calculationMethod, company.timezone);
      
      let totalProcessed = 0;
      let totalCreated = 0;
      let totalErrors = 0;

      // Process each business unit
      for (const businessUnit of company.businessUnits || []) {
        const result = await this.runOvertimeCalculationForBusinessUnit(
          companyId,
          businessUnit.id,
          dateRange,
          calculationMethod
        );
        
        totalProcessed += result.processed;
        totalCreated += result.created;
        totalErrors += result.errors;
      }

      // If no business units, process company-wide
      if (!company.businessUnits || company.businessUnits.length === 0) {
        const result = await this.runOvertimeCalculationForBusinessUnit(
          companyId,
          null,
          dateRange,
          calculationMethod
        );
        
        totalProcessed += result.processed;
        totalCreated += result.created;
        totalErrors += result.errors;
      }

      logger.info(`✅ Completed ${calculationMethod} overtime calculation for company ${companyId}:`);
      logger.info(`   📊 Processed: ${totalProcessed}, Created: ${totalCreated}, Errors: ${totalErrors}`);

    } catch (error) {
      logger.error(`❌ Error running overtime calculation for company ${companyId}:`, error);
    }
  }

  /**
   * Run overtime calculation for a specific business unit
   * @param {number} companyId - Company ID
   * @param {number} businessUnitId - Business Unit ID (null for company-wide)
   * @param {Object} dateRange - Date range for calculation
   * @param {string} calculationMethod - Calculation method
   */
  async runOvertimeCalculationForBusinessUnit(companyId, businessUnitId, dateRange, calculationMethod) {
    try {
      const tenantContext = {
        companyId,
        businessUnitId
      };

      // Get employees in this business unit
      const employeeFilters = {
        companyId,
        status: 'active'
      };

      if (businessUnitId) {
        employeeFilters.businessUnitId = businessUnitId;
      }

      const employees = await Employee.findAll({
        where: employeeFilters,
        attributes: ['id', 'employeeId', 'firstName', 'lastName']
      });

      let processed = 0;
      let created = 0;
      let errors = 0;

      // Process each employee for each date in range
      for (const employee of employees) {
        for (const date of dateRange.dates) {
          try {
            // Check if attendance exists for this date
            const attendanceExists = await Attendance.findOne({
              where: {
                employeeId: employee.id,
                companyId,
                date: date
              }
            });

            if (!attendanceExists) {
              continue; // Skip if no attendance record
            }

            // Check if overtime request already exists
            const existingOvertimeRequest = await OvertimeRequest.findOne({
              where: {
                employeeId: employee.id,
                companyId,
                date: date
              }
            });

            if (existingOvertimeRequest) {
              continue; // Skip if overtime request already exists
            }

            // Calculate overtime for this employee and date
            const overtimeResult = await this.overtimeCalculationService.calculateOvertimeForDate(
              employee.id,
              date,
              tenantContext
            );

            if (overtimeResult && overtimeResult.overtimeData) {
              // Create overtime request
              const overtimeRequest = await this.overtimeCalculationService.createOvertimeRequest(
                overtimeResult.overtimeData,
                tenantContext
              );

              // Start workflow if required and overtime is eligible
              if (overtimeResult.overtimeData.isEligible && 
                  overtimeResult.overtimeData.status === 'pending') {
                try {
                  await this.overtimeWorkflowService.createOvertimeWorkflow(
                    overtimeRequest,
                    tenantContext
                  );
                } catch (workflowError) {
                  logger.warn(`⚠️  Could not create workflow for overtime request ${overtimeRequest.id}:`, workflowError.message);
                }
              }

              created++;
              logger.debug(`✅ Created overtime request for employee ${employee.employeeId} on ${date}`);
            }

            processed++;

          } catch (error) {
            errors++;
            logger.error(`❌ Error processing overtime for employee ${employee.employeeId} on ${date}:`, error.message);
          }
        }
      }

      return { processed, created, errors };

    } catch (error) {
      logger.error(`❌ Error in business unit overtime calculation:`, error);
      return { processed: 0, created: 0, errors: 1 };
    }
  }

  /**
   * Get calculation date range based on method
   * @param {string} method - Calculation method (daily, weekly, monthly)
   * @param {string} timezone - Company timezone
   */
  getCalculationDateRange(method, timezone = 'UTC') {
    const now = moment().tz(timezone);
    let startDate, endDate;

    switch (method) {
      case 'weekly':
        // Previous week (Monday to Sunday)
        startDate = now.clone().subtract(1, 'week').startOf('week');
        endDate = now.clone().subtract(1, 'week').endOf('week');
        break;
      
      case 'monthly':
        // Previous month
        startDate = now.clone().subtract(1, 'month').startOf('month');
        endDate = now.clone().subtract(1, 'month').endOf('month');
        break;
      
      case 'daily':
      default:
        // Previous day
        startDate = now.clone().subtract(1, 'day').startOf('day');
        endDate = now.clone().subtract(1, 'day').endOf('day');
        break;
    }

    // Generate array of dates
    const dates = [];
    const current = startDate.clone();
    
    while (current.isSameOrBefore(endDate, 'day')) {
      dates.push(current.format('YYYY-MM-DD'));
      current.add(1, 'day');
    }

    return {
      startDate: startDate.format('YYYY-MM-DD'),
      endDate: endDate.format('YYYY-MM-DD'),
      dates
    };
  }

  /**
   * Stop all scheduled cron jobs
   */
  stopAllCronJobs() {
    logger.info('🛑 Stopping all overtime cron jobs...');
    
    for (const [jobKey, cronJob] of this.scheduledJobs) {
      cronJob.stop();
      logger.info(`🛑 Stopped cron job: ${jobKey}`);
    }
    
    this.scheduledJobs.clear();
    logger.info('✅ All overtime cron jobs stopped');
  }

  /**
   * Manually trigger overtime calculation for a company
   * @param {number} companyId - Company ID
   * @param {string} calculationMethod - Calculation method
   * @param {string} date - Specific date (optional)
   */
  async manualOvertimeCalculation(companyId, calculationMethod = 'daily', date = null) {
    try {
      logger.info(`🔧 Manual overtime calculation triggered for company ${companyId}`);
      
      if (date) {
        // Calculate for specific date
        const company = await Company.findByPk(companyId);
        const dateRange = {
          startDate: date,
          endDate: date,
          dates: [date]
        };
        
        const result = await this.runOvertimeCalculationForBusinessUnit(
          companyId,
          null,
          dateRange,
          'daily'
        );
        
        logger.info(`✅ Manual calculation completed: ${JSON.stringify(result)}`);
        return result;
      } else {
        // Use standard calculation method
        await this.runOvertimeCalculationForCompany(companyId, calculationMethod);
      }

    } catch (error) {
      logger.error(`❌ Error in manual overtime calculation:`, error);
      throw error;
    }
  }
}

module.exports = OvertimeCronService;
