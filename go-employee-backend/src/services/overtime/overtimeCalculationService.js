'use strict';

const {
  OvertimeRequest,
  Employee,
  ShiftType,
  ShiftTypeAssignment,
  BusinessUnit,
  AttendanceSettings,
  Attendance,
  Holiday,
  sequelize
} = require('../../data/models');
const { NotFoundError, ValidationError } = require('../../common/errors');
const { Op } = require('sequelize');
const moment = require('moment-timezone');
const logger = require('../../common/logging');

/**
 * Overtime Calculation Service
 * Handles overtime calculations with hierarchical shift resolution,
 * timezone-aware calculations, and multi-shift support
 */
class OvertimeCalculationService {

  /**
   * Get overtime settings with business unit hierarchy
   * 1. Business Unit-specific settings (attendance_settings where business_unit_id = X)
   * 2. Company-level settings (attendance_settings where business_unit_id IS NULL)
   */
  async getOvertimeSettings(tenantContext, businessUnitId = null) {
    try {
      let overtimeSettings = null;

      // 1. Try business unit-specific settings first
      if (businessUnitId) {
        overtimeSettings = await AttendanceSettings.findOne({
          where: {
            companyId: tenantContext.companyId,
            businessUnitId: businessUnitId
          },
          include: [
            {
              model: BusinessUnit,
              as: 'businessUnit'
            }
          ]
        });

        if (overtimeSettings) {
          logger.info(`Using business unit-specific attendance settings for BU ${businessUnitId}`);
          return overtimeSettings;
        }
      }

      // 2. Try company-level attendance settings
      overtimeSettings = await AttendanceSettings.findOne({
        where: {
          companyId: tenantContext.companyId,
          businessUnitId: null // Company-level
        }
      });

      if (overtimeSettings) {
        logger.info(`Using company-level attendance settings`);
        return overtimeSettings;
      }

      // 3. Fallback to attendance settings for backward compatibility
      const attendanceSettings = await AttendanceSettings.findOne({
        where: { companyId: tenantContext.companyId }
      });

      if (attendanceSettings) {
        logger.warn(`Falling back to attendance settings for overtime configuration`);
        // Convert attendance settings to overtime settings format
        return {
          overtimeEnabled: attendanceSettings.overtimeEnabled || true,
          overtimeThreshold: 8.0,
          overtimeCalculationMethod: attendanceSettings.overtimeCalculationMethod || 'daily',
          overtimeTiers: [
            { min: 0, max: 2, rate: attendanceSettings.overtimeRate || 1.5, name: 'Regular Overtime' },
            { min: 2, max: null, rate: 2.0, name: 'Premium Overtime' }
          ],
          weekendOvertimeEnabled: true,
          weekendOvertimeRate: attendanceSettings.weekendOvertimeRate || 2.0,
          holidayOvertimeEnabled: true,
          holidayOvertimeRate: attendanceSettings.holidayOvertimeRate || 2.0,
          dailyOvertimeLimit: attendanceSettings.dailyOvertimeLimit || 4.0,
          autoApprovalEnabled: attendanceSettings.autoApprovalEnabled || false,
          autoApprovalThreshold: attendanceSettings.autoApprovalThreshold || 1.0,
          overtimeRoundingMethod: 'round_nearest',
          overtimeMinimumIncrement: 0.25
        };
      }

      // 4. Return default settings if nothing found
      logger.warn(`No overtime settings found, using defaults`);
      return {
        overtimeEnabled: true,
        overtimeThreshold: 8.0,
        overtimeCalculationMethod: 'daily',
        overtimeTiers: [
          { min: 0, max: 2, rate: 1.5, name: 'Regular Overtime' },
          { min: 2, max: null, rate: 2.0, name: 'Premium Overtime' }
        ],
        weekendOvertimeEnabled: true,
        weekendOvertimeRate: 2.0,
        holidayOvertimeEnabled: true,
        holidayOvertimeRate: 2.0,
        dailyOvertimeLimit: 4.0,
        autoApprovalEnabled: false,
        autoApprovalThreshold: 1.0,
        overtimeRoundingMethod: 'round_nearest',
        overtimeMinimumIncrement: 0.25
      };

    } catch (error) {
      logger.error('Error getting overtime settings:', error);
      throw error;
    }
  }

  /**
   * Hierarchical Shift Resolution
   * 1. Employee-specific shift (employees.shift_type_id)
   * 2. Employee-specific assignment (shift_type_assignments where entity_type='employee')
   * 3. Business Unit default shift (business_units.shift_id) - if exists
   * 4. Company default shift (attendance_settings.defaultShiftId)
   */
  async resolveEmployeeShift(employeeId, date, tenantContext) {
    try {
      logger.info(`Resolving shift for employee ${employeeId} on ${date}`);
      
      // Get employee with related data
      const employee = await Employee.findOne({
        where: {
          id: employeeId,
          companyId: tenantContext.companyId
        },
        include: [
          {
            model: ShiftType,
            as: 'shiftType'
          },
          {
            model: BusinessUnit,
            as: 'businessUnit'
          }
        ]
      });

      if (!employee) {
        throw new NotFoundError('Employee not found');
      }

      let resolvedShift = null;
      let resolutionLevel = null;

      // 1. Check employee-specific shift (direct assignment)
      if (employee.shiftTypeId && employee.shiftType) {
        resolvedShift = employee.shiftType;
        resolutionLevel = 'employee_direct';
        logger.info(`Shift resolved at employee level: ${resolvedShift.name}`);
      }

      // 2. Check employee-specific shift assignment
      if (!resolvedShift) {
        const shiftAssignment = await ShiftTypeAssignment.findOne({
          where: {
            companyId: tenantContext.companyId,
            entityType: 'employee',
            entityId: employeeId,
            isActive: true
          },
          include: [{
            model: ShiftType,
            as: 'shiftType'
          }]
        });

        if (shiftAssignment && shiftAssignment.shiftType) {
          resolvedShift = shiftAssignment.shiftType;
          resolutionLevel = 'employee_assignment';
          logger.info(`Shift resolved at employee assignment level: ${resolvedShift.name}`);
        }
      }

      // 3. Check business unit default (if business unit has shift_id field)
      // Note: Current BusinessUnit model doesn't have shift_id, but we'll check for future compatibility
      if (!resolvedShift && employee.businessUnit) {
        // This would be implemented when BusinessUnit model gets shift_id field
        // For now, we'll skip this level
        logger.info('Business unit level shift resolution not available');
      }

      // 4. Check company default shift from attendance settings
      if (!resolvedShift) {
        const attendanceSettings = await AttendanceSettings.findOne({
          where: { companyId: tenantContext.companyId }
        });

        if (attendanceSettings && attendanceSettings.defaultShiftId) {
          const defaultShift = await ShiftType.findOne({
            where: {
              id: attendanceSettings.defaultShiftId,
              companyId: tenantContext.companyId
            }
          });

          if (defaultShift) {
            resolvedShift = defaultShift;
            resolutionLevel = 'company_default';
            logger.info(`Shift resolved at company default level: ${resolvedShift.name}`);
          }
        }
      }

      if (!resolvedShift) {
        throw new ValidationError('No shift could be resolved for employee');
      }

      return {
        shift: resolvedShift,
        resolutionLevel,
        employee
      };

    } catch (error) {
      logger.error('Error resolving employee shift:', error);
      throw error;
    }
  }

  /**
   * Calculate overtime for a specific date
   * Handles timezone-aware calculations and multi-shift support
   */
  async calculateOvertimeForDate(employeeId, date, tenantContext) {
    try {
      logger.info(`Calculating overtime for employee ${employeeId} on ${date}`);

      // Resolve employee shift
      const { shift, resolutionLevel, employee } = await this.resolveEmployeeShift(
        employeeId, 
        date, 
        tenantContext
      );

      // Get attendance records for the date
      const attendanceRecords = await Attendance.findAll({
        where: {
          employeeId,
          companyId: tenantContext.companyId,
          date: date
        },
        order: [['clockInTime', 'ASC']]
      });

      if (!attendanceRecords || attendanceRecords.length === 0) {
        logger.info(`No attendance records found for employee ${employeeId} on ${date}`);
        return null;
      }

      // Get overtime settings with business unit hierarchy
      const overtimeSettings = await this.getOvertimeSettings(tenantContext, employee.businessUnitId);

      // Get shift timezone (from shift's timezone_id or business unit timezone)
      const timezone = await this.getShiftTimezone(shift, employee.businessUnit);

      // Process multiple check-ins/outs
      const workingTime = await this.processMultipleCheckIns(
        attendanceRecords, 
        shift, 
        timezone,
        date
      );

      // Check if it's weekend or holiday
      const { isWeekend, isHoliday, holiday } = await this.checkWeekendHoliday(
        date, 
        tenantContext.companyId,
        employee.businessUnitId
      );

      // Calculate overtime with tiered rates
      const overtimeCalculation = await this.applyOvertimeRules(
        workingTime,
        shift,
        overtimeSettings,
        isWeekend,
        isHoliday
      );

      // Create overtime request data
      const overtimeData = {
        companyId: tenantContext.companyId,
        businessUnitId: employee.businessUnitId,
        employeeId,
        attendanceId: attendanceRecords[0].id, // Primary attendance record
        shiftTypeId: shift.id,
        date,
        requestType: 'auto_generated',
        
        // Time details
        shiftStartTime: shift.startTime,
        shiftEndTime: shift.endTime,
        actualStartTime: workingTime.actualStartTime,
        actualEndTime: workingTime.actualEndTime,
        standardHours: workingTime.standardHours,
        actualHours: workingTime.actualHours,
        overtimeHours: overtimeCalculation.totalOvertimeHours,
        
        // Overtime breakdown
        regularOvertimeHours: overtimeCalculation.regularOvertimeHours,
        premiumOvertimeHours: overtimeCalculation.premiumOvertimeHours,
        weekendHours: isWeekend ? workingTime.actualHours : 0,
        holidayHours: isHoliday ? workingTime.actualHours : 0,
        
        // Rates
        regularOvertimeRate: overtimeCalculation.regularOvertimeRate,
        premiumOvertimeRate: overtimeCalculation.premiumOvertimeRate,
        weekendRate: overtimeCalculation.weekendRate,
        holidayRate: overtimeCalculation.holidayRate,
        totalOvertimePay: overtimeCalculation.totalOvertimePay,
        
        // Status and metadata
        status: overtimeCalculation.shouldAutoApprove ? 'auto_approved' : 'pending',
        timezone,
        isEligible: overtimeCalculation.isEligible,
        exceedsLimit: overtimeCalculation.exceedsLimit,
        calculationMethod: overtimeSettings.overtimeCalculationMethod || 'daily',
        isWeekend,
        isHoliday,
        holidayId: holiday ? holiday.id : null,
        
        // Audit
        createdById: null // System generated
      };

      return {
        overtimeData,
        shiftResolution: {
          shift,
          resolutionLevel
        },
        workingTime,
        calculation: overtimeCalculation
      };

    } catch (error) {
      logger.error('Error calculating overtime for date:', error);
      throw error;
    }
  }

  /**
   * Get timezone for shift calculations
   */
  async getShiftTimezone(shift, businessUnit) {
    // Priority: Shift timezone > Business Unit timezone > UTC
    if (shift.timezoneId) {
      // TODO: Implement timezone lookup from timezones table
      return businessUnit?.timezone || 'UTC';
    }
    return businessUnit?.timezone || 'UTC';
  }

  /**
   * Process multiple check-ins/outs for a day
   * Handles lunch breaks, split shifts, etc.
   */
  async processMultipleCheckIns(attendanceRecords, shift, timezone, date) {
    try {
      let totalWorkingMinutes = 0;
      let actualStartTime = null;
      let actualEndTime = null;

      // Calculate standard shift duration
      const shiftStart = moment.tz(`${date} ${shift.startTime}`, timezone);
      const shiftEnd = moment.tz(`${date} ${shift.endTime}`, timezone);
      
      // Handle overnight shifts
      if (shiftEnd.isBefore(shiftStart)) {
        shiftEnd.add(1, 'day');
      }
      
      const standardMinutes = shiftEnd.diff(shiftStart, 'minutes') - (shift.breakDuration || 0);
      const standardHours = standardMinutes / 60;

      // Process each attendance record
      for (const record of attendanceRecords) {
        if (record.clockInTime && record.clockOutTime) {
          const clockIn = moment.tz(record.clockInTime, timezone);
          const clockOut = moment.tz(record.clockOutTime, timezone);
          
          // Track earliest start and latest end
          if (!actualStartTime || clockIn.isBefore(actualStartTime)) {
            actualStartTime = clockIn.toDate();
          }
          if (!actualEndTime || clockOut.isAfter(actualEndTime)) {
            actualEndTime = clockOut.toDate();
          }
          
          // Add working time for this session
          const sessionMinutes = clockOut.diff(clockIn, 'minutes');
          totalWorkingMinutes += sessionMinutes;
        }
      }

      const actualHours = totalWorkingMinutes / 60;

      return {
        actualStartTime,
        actualEndTime,
        standardHours,
        actualHours,
        totalWorkingMinutes
      };

    } catch (error) {
      logger.error('Error processing multiple check-ins:', error);
      throw error;
    }
  }

  /**
   * Check if date is weekend or holiday
   */
  async checkWeekendHoliday(date, companyId, businessUnitId) {
    try {
      const dateObj = moment(date);

      // Check if weekend (Saturday = 6, Sunday = 0)
      const isWeekend = dateObj.day() === 0 || dateObj.day() === 6;

      // Check if holiday
      const holiday = await Holiday.findOne({
        where: {
          companyId,
          [Op.or]: [
            { businessUnitId },
            { businessUnitId: null } // Company-wide holidays
          ],
          date: date
        }
      });

      return {
        isWeekend,
        isHoliday: !!holiday,
        holiday
      };

    } catch (error) {
      logger.error('Error checking weekend/holiday:', error);
      return { isWeekend: false, isHoliday: false, holiday: null };
    }
  }

  /**
   * Apply overtime rules with tiered rates
   */
  async applyOvertimeRules(workingTime, shift, overtimeSettings, isWeekend, isHoliday) {
    try {
      const { actualHours, standardHours } = workingTime;

      // If no overtime settings or overtime disabled
      if (!overtimeSettings || !overtimeSettings.overtimeEnabled) {
        return {
          totalOvertimeHours: 0,
          regularOvertimeHours: 0,
          premiumOvertimeHours: 0,
          totalOvertimePay: 0,
          regularOvertimeRate: 0,
          premiumOvertimeRate: 0,
          weekendRate: 0,
          holidayRate: 0,
          isEligible: false,
          exceedsLimit: false,
          shouldAutoApprove: false
        };
      }

      let overtimeHours = 0;
      let regularOvertimeHours = 0;
      let premiumOvertimeHours = 0;
      let totalOvertimePay = 0;

      // Get rates from overtime settings with tiered structure
      const overtimeTiers = overtimeSettings.overtimeTiers || [
        { min: 0, max: 2, rate: 1.5, name: 'Regular Overtime' },
        { min: 2, max: null, rate: 2.0, name: 'Premium Overtime' }
      ];
      const regularRate = overtimeTiers[0]?.rate || 1.5;
      const premiumRate = overtimeTiers[1]?.rate || 2.0;
      const weekendRate = overtimeSettings.weekendOvertimeRate || 2.0;
      const holidayRate = overtimeSettings.holidayOvertimeRate || 2.0;

      // Calculate base overtime hours
      if (actualHours > standardHours) {
        overtimeHours = actualHours - standardHours;
      }

      // Apply special rates for weekend/holiday
      if (isWeekend || isHoliday) {
        const specialRate = isHoliday ? holidayRate : weekendRate;
        totalOvertimePay = actualHours * specialRate;

        return {
          totalOvertimeHours: actualHours, // All hours are overtime on weekend/holiday
          regularOvertimeHours: 0,
          premiumOvertimeHours: 0,
          weekendHours: isWeekend ? actualHours : 0,
          holidayHours: isHoliday ? actualHours : 0,
          totalOvertimePay,
          regularOvertimeRate: regularRate,
          premiumOvertimeRate: premiumRate,
          weekendRate,
          holidayRate,
          isEligible: true,
          exceedsLimit: this.checkOvertimeLimits(actualHours, overtimeSettings),
          shouldAutoApprove: this.shouldAutoApprove(actualHours, overtimeSettings)
        };
      }

      // Apply tiered overtime rates for regular days
      if (overtimeHours > 0) {
        // Get overtime tiers from settings
        const tiers = overtimeSettings.overtimeTiers || [
          { min: 0, max: 2, rate: 1.5 },
          { min: 2, max: null, rate: 2.0 }
        ];

        let remainingHours = overtimeHours;

        for (const tier of tiers) {
          if (remainingHours <= 0) break;

          const tierHours = tier.max ?
            Math.min(remainingHours, tier.max - tier.min) :
            remainingHours;

          if (tier.min === 0) {
            regularOvertimeHours = tierHours;
          } else {
            premiumOvertimeHours += tierHours;
          }

          totalOvertimePay += tierHours * tier.rate;
          remainingHours -= tierHours;
        }
      }

      return {
        totalOvertimeHours: overtimeHours,
        regularOvertimeHours,
        premiumOvertimeHours,
        weekendHours: 0,
        holidayHours: 0,
        totalOvertimePay,
        regularOvertimeRate: regularRate,
        premiumOvertimeRate: premiumRate,
        weekendRate,
        holidayRate,
        isEligible: overtimeHours > 0,
        exceedsLimit: this.checkOvertimeLimits(overtimeHours, overtimeSettings),
        shouldAutoApprove: this.shouldAutoApprove(overtimeHours, overtimeSettings)
      };

    } catch (error) {
      logger.error('Error applying overtime rules:', error);
      throw error;
    }
  }

  /**
   * Check if overtime exceeds limits
   */
  checkOvertimeLimits(overtimeHours, overtimeSettings) {
    const dailyLimit = overtimeSettings.dailyOvertimeLimit || 4.0;
    return overtimeHours > dailyLimit;
  }

  /**
   * Check if overtime should be auto-approved
   */
  shouldAutoApprove(overtimeHours, overtimeSettings) {
    if (!overtimeSettings.autoApprovalEnabled) {
      return false;
    }

    const threshold = overtimeSettings.autoApprovalThreshold || 1.0;
    return overtimeHours <= threshold;
  }

  /**
   * Create or update overtime request
   */
  async createOvertimeRequest(overtimeData, tenantContext) {
    try {
      // Check if overtime request already exists for this employee and date
      const existingRequest = await OvertimeRequest.findOne({
        where: {
          companyId: overtimeData.companyId,
          employeeId: overtimeData.employeeId,
          date: overtimeData.date
        }
      });

      if (existingRequest) {
        // Update existing request
        await existingRequest.update(overtimeData);
        logger.info(`Updated overtime request ${existingRequest.id} for employee ${overtimeData.employeeId}`);
        return existingRequest;
      } else {
        // Create new request
        const newRequest = await OvertimeRequest.create(overtimeData);
        logger.info(`Created overtime request ${newRequest.id} for employee ${overtimeData.employeeId}`);
        return newRequest;
      }

    } catch (error) {
      logger.error('Error creating overtime request:', error);
      throw error;
    }
  }

  /**
   * Calculate overtime for multiple employees on a date
   */
  async calculateOvertimeForEmployees(employeeIds, date, tenantContext) {
    const results = [];
    const errors = [];

    for (const employeeId of employeeIds) {
      try {
        const result = await this.calculateOvertimeForDate(employeeId, date, tenantContext);
        if (result) {
          results.push(result);
        }
      } catch (error) {
        logger.error(`Error calculating overtime for employee ${employeeId}:`, error);
        errors.push({ employeeId, error: error.message });
      }
    }

    return { results, errors };
  }
}

module.exports = OvertimeCalculationService;
