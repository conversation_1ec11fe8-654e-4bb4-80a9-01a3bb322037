'use strict';

const { workflowEngine } = require('../workflow');
const { OvertimeRequest, Employee, User, WorkflowInstance, WorkflowStep, WorkflowAssignment } = require('../../data/models');
const { Op } = require('sequelize');
const { NotFoundError, ValidationError } = require('../../common/errors');
const UnifiedNotificationService = require('../notification/unifiedNotificationService');
const logger = require('../../common/logging');

/**
 * Overtime Workflow Service
 * Integrates Overtime module with Approval Workflow Engine
 * 
 * This service extends existing overtime functionality with automatic workflow management
 * following the same patterns as leave, WFH, expense modules
 */
class OvertimeWorkflowService {
  constructor() {
    this.workflowEngine = workflowEngine;
    this.notificationService = new UnifiedNotificationService();
  }

  /**
   * Process overtime approval - CONSISTENT WITH OTHER MODULES
   * @param {number} overtimeRequestId - Overtime request ID
   * @param {Object} approvalData - Approval data
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Updated workflow instance
   */
  async processOvertimeApproval(overtimeRequestId, approvalData, tenantContext) {
    try {
      logger.info(`Processing overtime approval for request ${overtimeRequestId}`);

      // Get overtime request with related data
      const overtimeRequest = await OvertimeRequest.findOne({
        where: {
          id: overtimeRequestId,
          companyId: tenantContext.companyId
        },
        include: [
          {
            model: Employee,
            as: 'employee',
            include: [
              { model: User, as: 'user' }
            ]
          },
          {
            model: WorkflowInstance,
            as: 'workflow'
          }
        ]
      });

      if (!overtimeRequest) {
        throw new NotFoundError('Overtime request not found');
      }

      // If no workflow exists, create one
      if (!overtimeRequest.workflowId) {
        await this.createOvertimeWorkflow(overtimeRequest, tenantContext);
        // Reload with workflow
        await overtimeRequest.reload({
          include: [
            {
              model: Employee,
              as: 'employee',
              include: [{ model: User, as: 'user' }]
            },
            {
              model: WorkflowInstance,
              as: 'workflow'
            }
          ]
        });
      }

      // Process the approval action through workflow engine - FLEXIBLE APPROVAL SYSTEM
      const action = {
        type: 'approve', // Use 'type' for flexible approval system
        comments: approvalData.comments || 'Approved',
        data: approvalData
      };

      console.log(`🔄 Processing approve action for workflow ${overtimeRequest.workflowId} by ${tenantContext.firstName} ${tenantContext.lastName}`);

      const workflowResult = await this.workflowEngine.processApprovalAction(
        overtimeRequest.workflowId,
        {
          type: action.type,
          comments: action.comments,
          userId: tenantContext.userId,
          userName: `${tenantContext.firstName} ${tenantContext.lastName}`,
          userEmail: tenantContext.email
        },
        tenantContext
      );

      // Update overtime request status based on workflow result
      if (workflowResult.finalStatus) {
        await this.updateOvertimeStatus(overtimeRequest, workflowResult.finalStatus, tenantContext.userId);
      }

      // Send notifications
      await this.sendOvertimeNotifications(overtimeRequest, action, workflowResult, tenantContext);

      return {
        overtimeRequest,
        workflowResult,
        success: true
      };

    } catch (error) {
      logger.error('Error processing overtime approval:', error);
      throw error;
    }
  }

  /**
   * Process overtime rejection - CONSISTENT WITH OTHER MODULES
   * @param {number} overtimeRequestId - Overtime request ID
   * @param {Object} rejectionData - Rejection data
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Updated workflow instance
   */
  async processOvertimeRejection(overtimeRequestId, rejectionData, tenantContext) {
    try {
      console.log(`🔄 Processing overtime rejection for overtime ID: ${overtimeRequestId}`);

      // Find the workflow instance for this overtime request
      const workflowInstance = await this.findWorkflowInstanceByOvertime(overtimeRequestId, tenantContext);

      if (!workflowInstance) {
        throw new NotFoundError('Workflow instance not found for this overtime request');
      }

      // Process the rejection action through workflow engine - FLEXIBLE APPROVAL SYSTEM
      const action = {
        type: 'reject', // Use 'type' for flexible approval system
        comments: rejectionData.rejectionReason || 'Rejected',
        data: rejectionData
      };

      console.log(`🔄 Processing reject action for workflow ${workflowInstance.id} by ${tenantContext.firstName} ${tenantContext.lastName}`);

      const workflowResult = await this.workflowEngine.processApprovalAction(
        workflowInstance.id,
        {
          type: action.type,
          comments: action.comments,
          userId: tenantContext.userId,
          userName: `${tenantContext.firstName} ${tenantContext.lastName}`,
          userEmail: tenantContext.email
        },
        tenantContext
      );

      console.log(`✅ Overtime rejection processed successfully for overtime ID: ${overtimeRequestId}`);

      return {
        workflowInstance: workflowResult.workflowInstance,
        workflowResult
      };

    } catch (error) {
      console.error(`❌ Error processing overtime rejection for overtime ID: ${overtimeRequestId}:`, error);
      throw error;
    }
  }

  /**
   * Find workflow instance by overtime ID
   * @param {number} overtimeId - Overtime request ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object|null>} Workflow instance
   */
  async findWorkflowInstanceByOvertime(overtimeId, tenantContext) {
    return await WorkflowInstance.findOne({
      where: {
        entityType: 'overtime',
        entityId: overtimeId,
        companyId: tenantContext.companyId
      }
    });
  }

  /**
   * Bulk approve/reject overtime requests
   * @param {Array} overtimeRequestIds - Array of overtime request IDs
   * @param {Object} action - Bulk action
   * @param {Object} tenantContext - Tenant context
   * @param {number} userId - User ID performing the action
   */
  async bulkApproveReject(overtimeRequestIds, action, tenantContext, userId) {
    const results = [];
    const errors = [];

    for (const requestId of overtimeRequestIds) {
      try {
        const result = await this.processOvertimeApproval(requestId, action, tenantContext, userId);
        results.push({ requestId, success: true, result });
      } catch (error) {
        logger.error(`Error processing bulk action for overtime request ${requestId}:`, error);
        errors.push({ requestId, error: error.message });
      }
    }

    return { results, errors };
  }

  /**
   * Create workflow for overtime request
   * @param {Object} overtimeRequest - Overtime request
   * @param {Object} tenantContext - Tenant context
   */
  async createOvertimeWorkflow(overtimeRequest, tenantContext) {
    try {
      logger.info(`Creating workflow for overtime request ${overtimeRequest.id}`);

      // Prepare entity data for workflow
      const entityData = {
        id: overtimeRequest.id,
        employeeId: overtimeRequest.employeeId,
        date: overtimeRequest.date,
        overtimeHours: overtimeRequest.overtimeHours,
        totalOvertimePay: overtimeRequest.totalOvertimePay,
        reason: overtimeRequest.reason
      };

      // Start workflow
      const workflowInstance = await this.workflowEngine.startWorkflow(
        'overtime',
        overtimeRequest.id,
        entityData,
        tenantContext
      );

      // Update overtime request with workflow ID
      await overtimeRequest.update({
        workflowId: workflowInstance.id
      });

      logger.info(`Created workflow ${workflowInstance.id} for overtime request ${overtimeRequest.id}`);
      return workflowInstance;

    } catch (error) {
      logger.error('Error creating overtime workflow:', error);
      throw error;
    }
  }

  /**
   * Update overtime request status based on workflow result
   * @param {Object} overtimeRequest - Overtime request
   * @param {string} finalStatus - Final workflow status
   * @param {number} userId - User ID
   */
  async updateOvertimeStatus(overtimeRequest, finalStatus, userId) {
    try {
      const statusMap = {
        'approved': 'approved',
        'rejected': 'rejected',
        'cancelled': 'rejected'
      };

      const newStatus = statusMap[finalStatus] || 'pending';
      const updateData = { status: newStatus };

      if (newStatus === 'approved') {
        updateData.approvedById = userId;
        updateData.approvedAt = new Date();
      } else if (newStatus === 'rejected') {
        updateData.rejectedAt = new Date();
      }

      await overtimeRequest.update(updateData);
      logger.info(`Updated overtime request ${overtimeRequest.id} status to ${newStatus}`);

    } catch (error) {
      logger.error('Error updating overtime status:', error);
      throw error;
    }
  }

  /**
   * Send overtime notifications
   * @param {Object} overtimeRequest - Overtime request
   * @param {Object} action - Action taken
   * @param {Object} workflowResult - Workflow result
   * @param {Object} tenantContext - Tenant context
   */
  async sendOvertimeNotifications(overtimeRequest, action, workflowResult, tenantContext) {
    try {
      // Send notification to employee about status update
      if (workflowResult.finalStatus) {
        await this.notificationService.sendOvertimeStatusNotification(
          overtimeRequest,
          workflowResult.finalStatus,
          tenantContext
        );
      }

      // Send notification to next approvers if workflow continues
      if (workflowResult.nextApprovers && workflowResult.nextApprovers.length > 0) {
        await this.notificationService.sendOvertimeApprovalNotification(
          overtimeRequest,
          workflowResult.nextApprovers,
          tenantContext
        );
      }

    } catch (error) {
      logger.error('Error sending overtime notifications:', error);
      // Don't throw error for notification failures
    }
  }

  /**
   * Get overtime approvers for an employee
   * @param {Object} overtimeRequest - Overtime request
   * @param {Object} tenantContext - Tenant context
   */
  async getOvertimeApprovers(overtimeRequest, tenantContext) {
    try {
      // Use the same approver hierarchy as other HRMS modules
      // Employee → Reporting Manager → Team Manager → Department Head → HR
      
      const employee = await Employee.findOne({
        where: {
          id: overtimeRequest.employeeId,
          companyId: tenantContext.companyId
        },
        include: [
          {
            model: Employee,
            as: 'reportingManager',
            include: [{ model: User, as: 'user' }]
          },
          {
            model: Employee,
            as: 'teamManager',
            include: [{ model: User, as: 'user' }]
          }
        ]
      });

      if (!employee) {
        throw new NotFoundError('Employee not found');
      }

      const approvers = [];

      // Add reporting manager
      if (employee.reportingManager && employee.reportingManager.user) {
        approvers.push({
          type: 'reporting_manager',
          employeeId: employee.reportingManager.id,
          userId: employee.reportingManager.user.id,
          email: employee.reportingManager.user.email,
          name: `${employee.reportingManager.firstName} ${employee.reportingManager.lastName}`
        });
      }

      // Add team manager (if different from reporting manager)
      if (employee.teamManager && 
          employee.teamManager.user && 
          employee.teamManager.id !== employee.reportingManager?.id) {
        approvers.push({
          type: 'team_manager',
          employeeId: employee.teamManager.id,
          userId: employee.teamManager.user.id,
          email: employee.teamManager.user.email,
          name: `${employee.teamManager.firstName} ${employee.teamManager.lastName}`
        });
      }

      // Add HR (role-based)
      // HR email mapping based on company and business unit
      const hrEmailMap = {
        '4_8': '<EMAIL>',
        '6_11': '<EMAIL>',
        '5_10': '<EMAIL>'
      };

      const hrKey = `${tenantContext.companyId}_${employee.businessUnitId}`;
      const hrEmail = hrEmailMap[hrKey] || '<EMAIL>';

      approvers.push({
        type: 'role',
        role: 'HR',
        email: hrEmail,
        name: 'HR Team'
      });

      return approvers;

    } catch (error) {
      logger.error('Error getting overtime approvers:', error);
      throw error;
    }
  }

  /**
   * Get overtime requests pending approval for a user - FLEXIBLE APPROVAL SYSTEM
   * @param {number} userId - User ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} pagination - Pagination parameters
   */
  async getPendingOvertimeApprovals(userId, tenantContext, pagination = {}) {
    try {
      // Use the new flexible approval system method - same pattern as leave service
      const pendingAssignments = await WorkflowAssignment.findAll({
        where: {
          assigneeId: tenantContext.employeeId, // Use employeeId for flexible approval
          status: 'pending', // Changed from 'active' to 'pending'
          companyId: tenantContext.companyId
        },
        include: [
          {
            model: WorkflowInstance,
            as: 'instance',
            where: {
              entityType: 'overtime',
              status: 'in_progress'
            },
            attributes: ['id', 'entityId', 'title', 'status']
          }
        ],
        limit: pagination.limit || 20,
        offset: pagination.offset || 0,
        order: [['createdAt', 'DESC']]
      });

      const results = [];

      for (const assignment of pendingAssignments) {
        // Get overtime details
        const overtime = await OvertimeRequest.findByPk(assignment.instance.entityId, {
          include: [
            {
              model: Employee,
              as: 'employee',
              attributes: ['id', 'firstName', 'lastName', 'employeeId', 'contactEmail']
            }
          ]
        });

        if (overtime) {
          results.push(overtime);
        }
      }

      return results;

    } catch (error) {
      logger.error('Error getting pending overtime approvals:', error);
      throw error;
    }
  }
}

module.exports = OvertimeWorkflowService;
