'use strict';

const { UserDevice, User, Employee, Company } = require('../../data/models');
const { NotFoundError, ValidationError } = require('../../common/errors');
const { Op } = require('sequelize');

/**
 * UserDevice Service
 * Handles device management for users
 */
class UserDeviceService {
  /**
   * Find device by device_id and user_id
   * @param {string} deviceId - Device ID
   * @param {number} userId - User ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object|null>} Device record
   */
  async findByDeviceAndUser(deviceId, userId, tenantContext = {}) {
    const whereClause = {
      deviceId,
      userId,
      isActive: true
    };

    if (tenantContext.companyId) {
      whereClause.companyId = tenantContext.companyId;
    }

    return await UserDevice.findOne({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'firstName', 'lastName', 'status']
        }
      ]
    });
  }

  /**
   * Update device token information
   * @param {number} deviceRecordId - Device record ID
   * @param {Object} updateData - Data to update
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Updated device record
   */
  async updateDeviceToken(deviceRecordId, updateData, tenantContext = {}) {
    const whereClause = { id: deviceRecordId };
    
    if (tenantContext.companyId) {
      whereClause.companyId = tenantContext.companyId;
    }

    const [updatedRowsCount, updatedRows] = await UserDevice.update(updateData, {
      where: whereClause,
      returning: true
    });

    if (updatedRowsCount === 0) {
      throw new NotFoundError('Device record not found or update failed');
    }

    return updatedRows[0];
  }

  /**
   * Create or update device registration
   * @param {Object} deviceData - Device data
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Device record
   */
  async registerDevice(deviceData, tenantContext = {}) {
    const {
      userId,
      employeeId,
      deviceId,
      deviceType,
      deviceName,
      deviceModel,
      osVersion,
      appVersion,
      pushToken,
      voipToken,
      timezone,
      language,
      ipAddress,
      userAgent,
      metadata
    } = deviceData;

    // Check if device already exists for this user
    const existingDevice = await this.findByDeviceAndUser(deviceId, userId, tenantContext);

    const deviceRecord = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      userId,
      employeeId,
      deviceId,
      deviceType,
      deviceName,
      deviceModel,
      osVersion,
      appVersion,
      pushToken,
      voipToken,
      isActive: true,
      lastActiveDate: new Date(),
      registrationDate: new Date(),
      timezone,
      language,
      ipAddress,
      userAgent,
      metadata
    };

    if (existingDevice) {
      // Update existing device
      return await this.updateDeviceToken(existingDevice.id, deviceRecord, tenantContext);
    } else {
      // Create new device record
      return await UserDevice.create(deviceRecord);
    }
  }

  /**
   * Get all devices for a user
   * @param {number} userId - User ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Array>} Array of device records
   */
  async getUserDevices(userId, tenantContext = {}) {
    const whereClause = {
      userId,
      isActive: true
    };

    if (tenantContext.companyId) {
      whereClause.companyId = tenantContext.companyId;
    }

    return await UserDevice.findAll({
      where: whereClause,
      order: [['lastActiveDate', 'DESC']]
    });
  }

  /**
   * Deactivate device
   * @param {string} deviceId - Device ID
   * @param {number} userId - User ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<boolean>} Success status
   */
  async deactivateDevice(deviceId, userId, tenantContext = {}) {
    const whereClause = {
      deviceId,
      userId
    };

    if (tenantContext.companyId) {
      whereClause.companyId = tenantContext.companyId;
    }

    const [updatedRowsCount] = await UserDevice.update(
      {
        isActive: false,
        unregistrationDate: new Date()
      },
      { where: whereClause }
    );

    return updatedRowsCount > 0;
  }

  /**
   * Update device last active date
   * @param {string} deviceId - Device ID
   * @param {number} userId - User ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<boolean>} Success status
   */
  async updateLastActive(deviceId, userId, tenantContext = {}) {
    const whereClause = {
      deviceId,
      userId,
      isActive: true
    };

    if (tenantContext.companyId) {
      whereClause.companyId = tenantContext.companyId;
    }

    const [updatedRowsCount] = await UserDevice.update(
      { lastActiveDate: new Date() },
      { where: whereClause }
    );

    return updatedRowsCount > 0;
  }

  /**
   * Get users associated with a device ID
   * @param {string} deviceId - Device ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Array>} Array of users with device info
   */
  async getDeviceUsers(deviceId, tenantContext = {}) {
    const whereClause = {
      deviceId,
      isActive: true
    };

    if (tenantContext.companyId) {
      whereClause.companyId = tenantContext.companyId;
    }

    return await UserDevice.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'firstName', 'lastName', 'status'],
          include: [
            {
              model: Employee,
              as: 'employee',
              attributes: ['id', 'employeeId', 'profileImage']
            },
            {
              model: Company,
              as: 'company',
              attributes: ['id', 'name', 'primaryContactEmail', 'billingAddress', 'billingCity', 'billingState', 'logo']
            }
          ]
        }
      ],
      order: [['lastActiveDate', 'DESC']]
    });
  }
}

module.exports = new UserDeviceService();
