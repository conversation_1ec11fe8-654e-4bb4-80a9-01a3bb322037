const {
  ShiftTemplate,
  ShiftTemplateDayConfig,
  ShiftTemplateDayShift,
  ShiftTemplateDayShiftDesignation,
  RotaShift,
  RotaShiftDesignationRequirement,
  Department,
  BusinessUnit,
  Company,
  User,
  Designation
} = require('../../data/models');
const { Op } = require('sequelize');
const { sequelize } = require('../../data/models');
const { NotFoundError, ValidationError, ConflictError } = require('../../common/errors');

class ShiftTemplateService {
  /**
   * Create new shift template
   * @param {Object} templateData - Template data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Created template
   */
  async createTemplate(templateData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      // Check for duplicate code
      const existingTemplate = await ShiftTemplate.findOne({
        where: {
          code: templateData.code,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        transaction
      });

      if (existingTemplate) {
        throw new ConflictError('Template code already exists');
      }

      // Validate day configurations (now normalized)
      await this.validateDayConfigurations(templateData.dayConfigurations, tenantContext, transaction);

      // Create template (without dayConfigurations JSONB field)
      const { dayConfigurations, ...templateFields } = templateData;
      const template = await ShiftTemplate.create({
        ...templateFields,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        createdById: tenantContext.userId
      }, { transaction });

      // Create normalized day configurations
      if (dayConfigurations) {
        await this.createNormalizedDayConfigurations(template.id, dayConfigurations, tenantContext.userId, transaction);
      }

      await transaction.commit();

      // Return with associations
      return await this.getTemplateById(template.id, tenantContext);

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get all templates with filters
   * @param {Object} filters - Filter options
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Templates with pagination
   */
  async getAllTemplates(filters = {}, tenantContext) {
    const {
      page = 1,
      limit = 10,
      search,
      category,
      status,
      isDefault,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = filters;

    const offset = (page - 1) * limit;
    const whereClause = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId
    };

    // Apply filters
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { code: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    if (category) {
      whereClause.category = category;
    }

    if (status === 'active') {
      whereClause.isActive = true;
    }

    if (typeof isDefault === 'boolean') {
      whereClause.isDefault = isDefault;
    }

    const { count, rows } = await ShiftTemplate.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'updatedBy',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    return {
      templates: rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        totalPages: Math.ceil(count / limit),
        hasNext: page < Math.ceil(count / limit),
        hasPrev: page > 1
      }
    };
  }

  /**
   * Get template by ID
   * @param {number} id - Template ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Template details
   */
  async getTemplateById(id, tenantContext) {
    const template = await ShiftTemplate.findOne({
      where: {
        id,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      },
      include: [
        {
          model: BusinessUnit,
          as: 'businessUnit',
          include: [
            {
              model: Company,
              as: 'company',
              attributes: ['id', 'name']
            }
          ]
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'updatedBy',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        // NEW: Include normalized day configurations
        {
          model: ShiftTemplateDayConfig,
          as: 'dayConfigs',
          include: [
            {
              model: ShiftTemplateDayShift,
              as: 'dayShifts',
              include: [
                {
                  model: ShiftTemplateDayShiftDesignation,
                  as: 'designationRequirements',
                  include: [
                        {
                          model: Designation,
                          as: 'designation',
                          attributes: ['id', 'name']
                        }
                      ]
                },
                {
                  model: RotaShift,
                  as: 'rotaShift',
                  attributes: ['id', 'companyId', 'businessUnitId', 'name', 'description', 'departmentId', 'startTime', 'endTime', 'breakDuration', 'priority', 'notes', 'isActive', 'createdById', 'updatedById', 'createdAt', 'updatedAt'],
                  include: [
                    {
                      model: Department,
                      as: 'department',
                      attributes: ['id', 'name']
                    },
                    // {
                    //   model: RotaShiftDesignationRequirement,
                    //   as: 'designationRequirements',
                    //   include: [
                    //     {
                    //       model: Designation,
                    //       as: 'designation',
                    //       attributes: ['id', 'name']
                    //     }
                    //   ]
                    // }
                  ]
                }
              ]
            }
          ]
        }
      ]
    });

    if (!template) {
      throw new NotFoundError('Template not found');
    }

    // Enrich with RotaShift details
    const enrichedTemplate = await this.enrichTemplateWithShiftDetails(template);

    return enrichedTemplate;
  }

  /**
   * Update template
   * @param {number} id - Template ID
   * @param {Object} updateData - Update data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated template
   */
  async updateTemplate(id, updateData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      const template = await ShiftTemplate.findOne({
        where: {
          id,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        transaction
      });

      if (!template) {
        throw new NotFoundError('Template not found');
      }

      // Check for duplicate code if code is being updated
      if (updateData.code && updateData.code !== template.code) {
        const existingTemplate = await ShiftTemplate.findOne({
          where: {
            code: updateData.code,
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId,
            id: { [Op.ne]: id }
          },
          transaction
        });

        if (existingTemplate) {
          throw new ConflictError('Template code already exists');
        }
      }

      // Validate day configurations if provided
      if (updateData.dayConfigurations) {
        await this.validateDayConfigurations(updateData.dayConfigurations, tenantContext, transaction);
      }

      // Separate day configurations from template fields
      const { dayConfigurations, ...templateFields } = updateData;

      // Update template (without dayConfigurations field)
      await template.update({
        ...templateFields,
        updatedById: tenantContext.userId
      }, { transaction });

      // Update normalized day configurations if provided
      if (dayConfigurations) {
        // Remove existing day configurations
        await this.removeExistingDayConfigurations(template.id, transaction);

        // Create new normalized day configurations
        await this.createNormalizedDayConfigurations(template.id, dayConfigurations, tenantContext.userId, transaction);
      }

      await transaction.commit();

      // Return updated template
      return await this.getTemplateById(id, tenantContext);

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Delete template
   * @param {number} id - Template ID
   * @param {Object} tenantContext - Tenant context
   * @returns {boolean} Success status
   */
  async deleteTemplate(id, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      const template = await ShiftTemplate.findOne({
        where: {
          id,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        transaction
      });

      if (!template) {
        throw new NotFoundError('Template not found');
      }

      // Check if template is being used in any schedules
      // TODO: Add check for RotaSchedule usage when that relationship is established

      await template.destroy({ transaction });
      await transaction.commit();

      return true;

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Clone template
   * @param {number} id - Template ID to clone
   * @param {Object} cloneData - Clone configuration
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Cloned template
   */
  async cloneTemplate(id, cloneData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      const originalTemplate = await ShiftTemplate.findOne({
        where: {
          id,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        transaction
      });

      if (!originalTemplate) {
        throw new NotFoundError('Template not found');
      }

      // Check for duplicate code
      const existingTemplate = await ShiftTemplate.findOne({
        where: {
          code: cloneData.code,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        transaction
      });

      if (existingTemplate) {
        throw new ConflictError('Template code already exists');
      }

      // Create cloned template
      const clonedTemplate = await ShiftTemplate.create({
        name: cloneData.name,
        code: cloneData.code,
        description: cloneData.description || `Cloned from ${originalTemplate.name}`,
        category: originalTemplate.category,
        dayConfigurations: originalTemplate.dayConfigurations,
        templateType: originalTemplate.templateType,
        autoAssignmentEnabled: originalTemplate.autoAssignmentEnabled,
        assignmentRules: originalTemplate.assignmentRules,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        createdById: tenantContext.userId
      }, { transaction });

      await transaction.commit();

      return await this.getTemplateById(clonedTemplate.id, tenantContext);

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Validate day configurations
   * @param {Object} dayConfigurations - Day configurations to validate
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async validateDayConfigurations(dayConfigurations, tenantContext, transaction) {
    if (!dayConfigurations || typeof dayConfigurations !== 'object') {
      throw new ValidationError('Day configurations must be an object');
    }

    const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const rotaShiftIds = new Set();

    for (const [day, config] of Object.entries(dayConfigurations)) {
      if (!validDays.includes(day.toLowerCase())) {
        throw new ValidationError(`Invalid day: ${day}`);
      }

      if (config.isWorkingDay && config.shifts) {
        for (const shift of config.shifts) {
          if (!shift.rotaShiftId) {
            throw new ValidationError(`Missing rotaShiftId for ${day} shift`);
          }
          rotaShiftIds.add(shift.rotaShiftId);

          // ✅ Validate designation overrides if provided
          if (shift.useDesignationOverride) {
            if (!shift.designationOverrides || !Array.isArray(shift.designationOverrides) || shift.designationOverrides.length === 0) {
              throw new ValidationError(`${day} shift has useDesignationOverride=true but no designationOverrides provided`);
            }

            // Check for duplicate designations in overrides
            const designationIds = shift.designationOverrides.map(d => d.designationId);
            const uniqueDesignationIds = new Set(designationIds);

            if (designationIds.length !== uniqueDesignationIds.size) {
              throw new ValidationError(`Duplicate designation IDs in ${day} shift overrides`);
            }

            // Validate required counts are positive
            for (const override of shift.designationOverrides) {
              if (override.requiredCount <= 0) {
                throw new ValidationError(`Invalid required count ${override.requiredCount} for designation ${override.designationId} in ${day} shift`);
              }
            }
          } else {
            // If not using override, designationOverrides should not be provided
            if (shift.designationOverrides && shift.designationOverrides.length > 0) {
              throw new ValidationError(`${day} shift has useDesignationOverride=false but designationOverrides provided`);
            }
          }
        }
      }
    }

    // Validate all referenced RotaShifts exist
    if (rotaShiftIds.size > 0) {
      const existingShifts = await RotaShift.findAll({
        where: {
          id: { [Op.in]: Array.from(rotaShiftIds) },
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId,
          isActive: true
        },
        attributes: ['id'],
        transaction
      });

      const existingShiftIds = new Set(existingShifts.map(s => s.id));
      const missingShiftIds = Array.from(rotaShiftIds).filter(id => !existingShiftIds.has(id));

      if (missingShiftIds.length > 0) {
        throw new ValidationError(`Invalid RotaShift IDs: ${missingShiftIds.join(', ')}`);
      }
    }

    // Validate all referenced Designations exist (from overrides)
    const designationIds = new Set();
    Object.values(dayConfigurations).forEach(dayConfig => {
      if (dayConfig.shifts) {
        dayConfig.shifts.forEach(shift => {
          if (shift.useDesignationOverride && shift.designationOverrides) {
            shift.designationOverrides.forEach(override => {
              designationIds.add(override.designationId);
            });
          }
        });
      }
    });

    if (designationIds.size > 0) {
      const { Designation } = require('../../data/models');
      const existingDesignations = await Designation.findAll({
        where: {
          id: { [Op.in]: Array.from(designationIds) },
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId,
          status: 'active'
        },
        attributes: ['id'],
        transaction
      });

      const existingDesignationIds = new Set(existingDesignations.map(d => d.id));
      const missingDesignationIds = Array.from(designationIds).filter(id => !existingDesignationIds.has(id));

      if (missingDesignationIds.length > 0) {
        throw new ValidationError(`Invalid Designation IDs: ${missingDesignationIds.join(', ')}`);
      }
    }
  }

  /**
   * Enrich template with RotaShift details - UPDATED for normalized structure
   * @param {Object} template - Template object with normalized day configs
   * @returns {Object} Enriched template with JSONB-like structure
   */
  async enrichTemplateWithShiftDetails(template) {
    const templateData = template.toJSON();

    // Convert normalized structure to JSONB-like format for API response
    const dayConfigurations = this.convertNormalizedToJsonbFormat(template);

    if (!dayConfigurations || Object.keys(dayConfigurations).length === 0) {
      return {
        ...templateData,
        dayConfigurations: {}
      };
    }

    // The normalized structure already includes RotaShift details from the query
    // Add effective designation requirements for frontend display
    const enrichedDayConfigurations = {};

    Object.entries(dayConfigurations).forEach(([day, dayConfig]) => {
      enrichedDayConfigurations[day] = {
        ...dayConfig,
        shifts: dayConfig.shifts ? dayConfig.shifts.map(shift => {
          // Calculate effective designation requirements
          let effectiveDesignationRequirements = [];

          if (shift.useDesignationOverride && shift.designationOverrides && shift.designationOverrides.length > 0) {
            // Use template-level overrides - need to convert to full format
            effectiveDesignationRequirements = shift.designationOverrides.map(override => ({
              id: null, // Template overrides don't have IDs
              rotaShiftId: shift.rotaShiftId,
              designationId: override.designationId,
              requiredCount: override.requiredCount,
              createdAt: null,
              updatedAt: null,
              designation: {
                id: override.designationId,
                name: `Designation ${override.designationId}` // Would need to fetch actual name
              }
            }));
          } else if (shift.rotaShiftDetails && shift.rotaShiftDetails.designationRequirements) {
            // Use RotaShift's default designation requirements
            effectiveDesignationRequirements = shift.rotaShiftDetails.designationRequirements;
          }

          return {
            ...shift,
            effectiveDesignationRequirements
          };
        }) : []
      };
    });

    return {
      ...templateData,
      dayConfigurations: enrichedDayConfigurations,
      // availableRotaShifts: rotaShifts.map(shift => shift.toJSON())
    };
  }

  /**
   * Get available RotaShifts for template creation
   * @param {Object} tenantContext - Tenant context
   * @returns {Array} Available RotaShifts
   */
  async getAvailableRotaShifts(tenantContext) {
    const rotaShifts = await RotaShift.findAll({
      where: {
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        isActive: true
      },
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        }
      ],
      order: [['name', 'ASC']]
    });

    return rotaShifts.map(shift => ({
      id: shift.id,
      name: shift.name,
      description: shift.description,
      startTime: shift.startTime,
      endTime: shift.endTime,
      breakDuration: shift.breakDuration,
      department: shift.department,
      priority: shift.priority
    }));
  }

  /**
   * Set template as default
   * @param {number} id - Template ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated template
   */
  async setAsDefault(id, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      // Remove default from all other templates
      await ShiftTemplate.update(
        { isDefault: false },
        {
          where: {
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId,
            isDefault: true
          },
          transaction
        }
      );

      // Set this template as default
      const template = await ShiftTemplate.findOne({
        where: {
          id,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        transaction
      });

      if (!template) {
        throw new NotFoundError('Template not found');
      }

      await template.update({
        isDefault: true,
        updatedById: tenantContext.userId
      }, { transaction });

      await transaction.commit();

      return await this.getTemplateById(id, tenantContext);

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Update template usage count
   * @param {number} id - Template ID
   * @param {Object} tenantContext - Tenant context
   */
  async incrementUsageCount(id, tenantContext) {
    await ShiftTemplate.increment('usageCount', {
      by: 1,
      where: {
        id,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      }
    });

    await ShiftTemplate.update(
      { lastUsedAt: new Date() },
      {
        where: {
          id,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        }
      }
    );
  }

  /**
   * Get effective designation requirements for a template shift
   * @param {Object} templateShift - Template shift configuration
   * @param {Object} tenantContext - Tenant context
   * @returns {Array} Effective designation requirements
   */
  async getEffectiveDesignationRequirements(templateShift, tenantContext) {
    const { RotaShiftDesignationRequirement } = require('../../data/models');

    if (templateShift.useDesignationOverride && templateShift.designationOverrides) {
      // ✅ Use template overrides
      console.log(`Using template designation overrides for shift ${templateShift.rotaShiftId}`);
      return templateShift.designationOverrides.map(override => ({
        designationId: override.designationId,
        requiredCount: override.requiredCount,
        source: 'template_override'
      }));
    } else {
      // ✅ Use RotaShift default designation requirements
      console.log(`Using RotaShift default designations for shift ${templateShift.rotaShiftId}`);

      const defaultRequirements = await RotaShiftDesignationRequirement.findAll({
        where: {
          rotaShiftId: templateShift.rotaShiftId
        },
        attributes: ['designationId', 'requiredCount']
      });

      return defaultRequirements.map(req => ({
        designationId: req.designationId,
        requiredCount: req.requiredCount,
        source: 'rota_shift_default'
      }));
    }
  }

  /**
   * Generate shift instances from template for a specific date range
   * @param {number} templateId - Template ID
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @param {Object} tenantContext - Tenant context
   * @returns {Array} Generated shift instances data
   */
  async generateShiftInstancesFromTemplate(templateId, startDate, endDate, tenantContext) {
    const template = await this.getTemplateById(templateId, tenantContext);

    if (!template.dayConfigurations) {
      throw new ValidationError('Template has no day configurations');
    }

    const instances = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Increment usage count
    await this.incrementUsageCount(templateId, tenantContext);

    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      const dayName = date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
      const dayConfig = template.dayConfigurations[dayName];

      if (dayConfig && dayConfig.isWorkingDay && dayConfig.shifts) {
        for (const templateShift of dayConfig.shifts) {
          // Get effective designation requirements
          const designationRequirements = await this.getEffectiveDesignationRequirements(
            templateShift,
            tenantContext
          );

          // Calculate total required count
          const totalRequiredCount = designationRequirements.reduce(
            (sum, req) => sum + req.requiredCount,
            0
          );

          instances.push({
            rotaShiftId: templateShift.rotaShiftId,
            date: date.toISOString().split('T')[0],
            dayName: dayName,
            priority: templateShift.priority || 1,
            designationRequirements: designationRequirements,
            totalRequiredCount: totalRequiredCount,
            isFlexible: templateShift.isFlexible || false,
            notes: templateShift.notes || null,
            templateId: templateId,
            templateSource: designationRequirements[0]?.source || 'unknown'
          });
        }
      }
    }

    console.log(`Generated ${instances.length} shift instances from template ${template.name}`);
    return instances;
  }

  /**
   * Create normalized day configurations from JSONB structure
   * @param {number} templateId - Template ID
   * @param {Object} dayConfigurations - Day configurations object
   * @param {number} userId - User ID for audit
   * @param {Object} transaction - Database transaction
   */
  async createNormalizedDayConfigurations(templateId, dayConfigurations, userId, transaction) {
    console.log(`🔄 Creating normalized day configurations for template ${templateId}`);

    for (const [dayOfWeek, dayConfig] of Object.entries(dayConfigurations)) {
      // Create day config record
      const dayConfigRecord = await ShiftTemplateDayConfig.create({
        shiftTemplateId: templateId,
        dayOfWeek: dayOfWeek.toLowerCase(),
        isWorkingDay: dayConfig.isWorkingDay !== false,
        dayType: dayConfig.dayType || 'regular',
        priority: dayConfig.priority || 1,
        notes: dayConfig.notes || null,
        createdById: userId
      }, { transaction });

      // Create day shifts if any
      if (dayConfig.shifts && dayConfig.shifts.length > 0) {
        for (const shift of dayConfig.shifts) {
          const dayShiftRecord = await ShiftTemplateDayShift.create({
            dayConfigId: dayConfigRecord.id,
            rotaShiftId: shift.rotaShiftId,
            priority: shift.priority || 1,
            isFlexible: shift.isFlexible || false,
            useDesignationOverride: shift.useDesignationOverride || false,
            notes: shift.notes || null,
            createdById: userId
          }, { transaction });

          // ✅ NEW LOGIC: Always create designation requirements (actual + override merged)
          await this.createMergedDesignationRequirements(
            dayShiftRecord.id,
            shift.rotaShiftId,
            shift.useDesignationOverride,
            shift.designationOverrides || [],
            userId,
            transaction
          );
        }
      }
    }

    console.log(`✅ Created normalized day configurations for template ${templateId}`);
  }

  /**
   * ✅ NEW METHOD: Create merged designation requirements (actual + override)
   * @param {number} dayShiftId - Day shift ID
   * @param {number} rotaShiftId - RotaShift ID
   * @param {boolean} useDesignationOverride - Whether override is enabled
   * @param {Array} designationOverrides - Override designation requirements
   * @param {number} userId - User ID for audit
   * @param {Object} transaction - Database transaction
   */
  async createMergedDesignationRequirements(dayShiftId, rotaShiftId, useDesignationOverride, designationOverrides, userId, transaction) {
    console.log(`🔄 Creating merged designation requirements for dayShift ${dayShiftId}, rotaShift ${rotaShiftId}`);

    // Step 1: Get actual RotaShift designation requirements
    const { RotaShiftDesignationRequirement } = require('../../data/models');
    const actualRequirements = await RotaShiftDesignationRequirement.findAll({
      where: { rotaShiftId },
      transaction
    });

    console.log(`📋 Found ${actualRequirements.length} actual requirements for RotaShift ${rotaShiftId}`);

    // Step 2: Create a map for merging (designationId -> requirement)
    const mergedRequirements = new Map();

    // Step 3: Add actual requirements first (lower priority)
    actualRequirements.forEach(req => {
      mergedRequirements.set(req.designationId, {
        designationId: req.designationId,
        requiredCount: req.requiredCount,
        source: 'actual'
      });
    });

    // Step 4: Override with template overrides if enabled (higher priority)
    if (useDesignationOverride && designationOverrides.length > 0) {
      console.log(`🔄 Applying ${designationOverrides.length} override requirements`);
      designationOverrides.forEach(override => {
        mergedRequirements.set(override.designationId, {
          designationId: override.designationId,
          requiredCount: override.requiredCount,
          source: 'override'
        });
      });
    }

    // Step 5: Create entries in ShiftTemplateDayShiftDesignation table
    const { ShiftTemplateDayShiftDesignation } = require('../../data/models');
    for (const [designationId, requirement] of mergedRequirements) {
      await ShiftTemplateDayShiftDesignation.create({
        dayShiftId,
        designationId: requirement.designationId,
        requiredCount: requirement.requiredCount,
        createdById: userId
      }, { transaction });

      console.log(`✅ Created requirement: Designation ${requirement.designationId} = ${requirement.requiredCount} (${requirement.source})`);
    }

    console.log(`✅ Created ${mergedRequirements.size} merged designation requirements for dayShift ${dayShiftId}`);
  }

  /**
   * Remove existing day configurations for a template
   * @param {number} templateId - Template ID
   * @param {Object} transaction - Database transaction
   */
  async removeExistingDayConfigurations(templateId, transaction) {
    console.log(`🗑️ Removing existing day configurations for template ${templateId}`);

    // Get all day configs for this template
    const dayConfigs = await ShiftTemplateDayConfig.findAll({
      where: { shiftTemplateId: templateId },
      include: [
        {
          model: ShiftTemplateDayShift,
          as: 'dayShifts',
          include: [
            {
              model: ShiftTemplateDayShiftDesignation,
              as: 'designationRequirements'
            }
          ]
        }
      ],
      transaction
    });

    // Remove in proper order (designations -> shifts -> configs)
    for (const dayConfig of dayConfigs) {
      if (dayConfig.dayShifts) {
        for (const dayShift of dayConfig.dayShifts) {
          // Remove designation requirements first
          if (dayShift.designationRequirements) {
            await ShiftTemplateDayShiftDesignation.destroy({
              where: { dayShiftId: dayShift.id },
              transaction
            });
          }
        }

        // Remove day shifts
        await ShiftTemplateDayShift.destroy({
          where: { dayConfigId: dayConfig.id },
          transaction
        });
      }
    }

    // Remove day configs
    await ShiftTemplateDayConfig.destroy({
      where: { shiftTemplateId: templateId },
      transaction
    });

    console.log(`✅ Removed existing day configurations for template ${templateId}`);
  }

  /**
   * Convert normalized day configurations back to JSONB-like structure for API response
   * @param {Object} template - Template with normalized day configs
   * @returns {Object} JSONB-like day configurations
   */
  convertNormalizedToJsonbFormat(template) {
    const dayConfigurations = {};

    if (template.dayConfigs) {
      for (const dayConfig of template.dayConfigs) {
        dayConfigurations[dayConfig.dayOfWeek] = {
          isWorkingDay: dayConfig.isWorkingDay,
          dayType: dayConfig.dayType,
          priority: dayConfig.priority,
          notes: dayConfig.notes,
          shifts: dayConfig.dayShifts ? dayConfig.dayShifts.map(dayShift => ({
            rotaShiftId: dayShift.rotaShiftId,
            priority: dayShift.priority,
            isFlexible: dayShift.isFlexible,
            useDesignationOverride: dayShift.useDesignationOverride,
            notes: dayShift.notes,
            designationOverrides: dayShift.designationRequirements ?
              dayShift.designationRequirements.map(req => ({
                designationId: req.designationId,
                requiredCount: req.requiredCount
              })) : [],
            // Include full RotaShift details for frontend (matching previous response format)
            rotaShiftDetails: dayShift.rotaShift ? {
              id: dayShift.rotaShift.id,
              companyId: dayShift.rotaShift.companyId,
              businessUnitId: dayShift.rotaShift.businessUnitId,
              name: dayShift.rotaShift.name,
              description: dayShift.rotaShift.description,
              departmentId: dayShift.rotaShift.departmentId,
              startTime: dayShift.rotaShift.startTime,
              endTime: dayShift.rotaShift.endTime,
              breakDuration: dayShift.rotaShift.breakDuration,
              priority: dayShift.rotaShift.priority,
              notes: dayShift.rotaShift.notes,
              isActive: dayShift.rotaShift.isActive,
              createdById: dayShift.rotaShift.createdById,
              updatedById: dayShift.rotaShift.updatedById,
              createdAt: dayShift.rotaShift.createdAt,
              updatedAt: dayShift.rotaShift.updatedAt,
              department: dayShift.rotaShift.department,
              designationRequirements: dayShift.rotaShift.designationRequirements || []
            } : null
          })) : []
        };
      }
    }

    return dayConfigurations;
  }
}

module.exports = new ShiftTemplateService();
