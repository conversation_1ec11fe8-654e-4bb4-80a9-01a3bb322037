'use strict';

/**
 * Shift Assignment Service - PRD Implementation
 * 
 * Handles employee assignment business logic according to PRD specifications:
 * - Employee-to-instance assignment workflow
 * - Assignment status tracking and transitions
 * - Conflict detection and validation
 * - Assignment history and audit trail
 */

const {
  ShiftAssignment,
  RotaShiftInstance,
  RotaShiftInstanceDesignationRequirement,
  RotaShift,
  RotaSchedule,
  Employee,
  Department,
  Designation
} = require('../../data/models');
const { NotFoundError, ValidationError, ConflictError } = require('../../common/errors');
const { Op } = require('sequelize');
const sequelize = require('../../data/models').sequelize;
const moment = require('moment');

class ShiftAssignmentService {

  /**
   * Get all shift assignments with advanced filtering
   * @param {Object} filters - Filter options
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Assignments with pagination
   */
  async getAllAssignments(filters, tenantContext) {
    const {
      page = 1,
      limit = 10,
      employeeId,
      scheduleId,
      instanceId,
      status,
      assignmentType,
      startDate,
      endDate,
      departmentId,
      search,
      sortBy = 'assignedAt',
      sortOrder = 'DESC'
    } = filters;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // Apply direct filters
    if (employeeId) whereClause.employeeId = employeeId;
    if (instanceId) whereClause.shiftInstanceId = instanceId;
    if (status) whereClause.status = status;
    if (assignmentType) whereClause.assignmentType = assignmentType;

    const includeClause = [
      {
        model: Employee,
        as: 'employee',
        attributes: ['id', 'firstName', 'lastName', 'contactEmail', 'employeeId'],
        where: {
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        }
      },
      {
        model: Designation,
        as: 'designation',
        attributes: ['id', 'name', 'code'],
        required: false
      },
      {
        model: RotaShiftInstance,
        as: 'shiftInstance',
        attributes: ['id', 'date', 'actualRequiredCount'],
        where: scheduleId ? { scheduleId } : undefined,
        include: [
          {
            model: RotaShift,
            as: 'rotaShift',
            attributes: ['id', 'name', 'startTime', 'endTime'],
            include: [
              {
                model: Department,
                as: 'department',
                attributes: ['id', 'name'],
                where: departmentId ? { id: departmentId } : undefined
              }
            ]
          },
          {
            model: RotaSchedule,
            as: 'schedule',
            attributes: ['id', 'name', 'status']
          }
        ]
      }
    ];

    // Add date filtering
    if (startDate || endDate) {
      const dateFilter = {};
      if (startDate && endDate) {
        dateFilter.date = { [Op.between]: [startDate, endDate] };
      } else if (startDate) {
        dateFilter.date = { [Op.gte]: startDate };
      } else if (endDate) {
        dateFilter.date = { [Op.lte]: endDate };
      }
      
      if (includeClause[1].where) {
        includeClause[1].where = { ...includeClause[1].where, ...dateFilter };
      } else {
        includeClause[1].where = dateFilter;
      }
    }

    // Add search functionality
    if (search) {
      includeClause[0].where = {
        ...includeClause[0].where,
        [Op.or]: [
          { firstName: { [Op.iLike]: `%${search}%` } },
          { lastName: { [Op.iLike]: `%${search}%` } },
          { contactEmail: { [Op.iLike]: `%${search}%` } },
          { employeeId: { [Op.iLike]: `%${search}%` } }
        ]
      };
    }

    const { count, rows: assignments } = await ShiftAssignment.findAndCountAll({
      where: whereClause,
      include: includeClause,
      order: [[sortBy, sortOrder]],
      limit,
      offset,
      distinct: true
    });

    return {
      assignments,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
        hasNext: page < Math.ceil(count / limit),
        hasPrev: page > 1
      }
    };
  }

  /**
   * Get assignment by ID with optional detailed information
   * @param {number} id - Assignment ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} options - Additional options
   * @returns {Object} Assignment details
   */
  async getAssignmentById(id, tenantContext, options = {}) {
    // Ensure ID is a number
    const assignmentId = parseInt(id);
    if (isNaN(assignmentId)) {
      throw new NotFoundError('Invalid assignment ID');
    }

    // First, find the assignment without complex joins
    const assignment = await ShiftAssignment.findByPk(assignmentId);

    if (!assignment) {
      throw new NotFoundError('Shift assignment not found');
    }

    // If no associations needed, return basic assignment
    if (options.includeEmployee === false && options.includeInstance === false) {
      return assignment;
    }

    // Build include clause for detailed query
    const includeClause = [];

    // Include employee information
    if (options.includeEmployee !== false) {
      includeClause.push({
        model: Employee,
        as: 'employee',
        attributes: ['id', 'firstName', 'lastName', 'contactEmail', 'employeeId', 'companyId', 'businessUnitId'],
        required: false
      });
    }

    // Include instance information
    if (options.includeInstance !== false) {
      includeClause.push({
        model: RotaShiftInstance,
        as: 'shiftInstance',
        include: [
          {
            model: RotaShift,
            as: 'rotaShift',
            include: [
              {
                model: Department,
                as: 'department',
                attributes: ['id', 'name']
              },
              {
                model: Designation,
                as: 'designation',
                attributes: ['id', 'name'],
                through: { attributes: ['requiredCount'] }
              }
            ]
          },
          {
            model: RotaSchedule,
            as: 'schedule',
            attributes: ['id', 'name', 'status']
          }
        ]
      });
    }

    // Get assignment with associations
    try {
      const detailedAssignment = await ShiftAssignment.findOne({
        where: { id: assignmentId },
        include: includeClause
      });

      if (detailedAssignment) {
        // Verify tenant access if employee is included
        if (detailedAssignment.employee &&
            (detailedAssignment.employee.companyId !== tenantContext.companyId ||
             detailedAssignment.employee.businessUnitId !== tenantContext.businessUnitId)) {
          throw new NotFoundError('Shift assignment not found');
        }
        return detailedAssignment;
      }
    } catch (error) {
      console.error('Error fetching detailed assignment:', error);
    }

    // Fallback to basic assignment if detailed query fails
    return assignment;
  }

  /**
   * Create new assignment
   * @param {Object} assignmentData - Assignment data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Created assignment
   */
  async createAssignment(assignmentData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      // Validate assignment data
      await this.validateAssignmentData(assignmentData, tenantContext, transaction);

      // Check for conflicts
      await this.checkAssignmentConflicts(assignmentData, tenantContext, transaction);

      const assignment = await ShiftAssignment.create({
        ...assignmentData,
        status: 'assigned',
        assignedBy: tenantContext.userId,
        createdById: tenantContext.userId
      }, { transaction });

      await transaction.commit();

      // Return assignment with associations (outside transaction)
      try {
        return await this.getAssignmentById(assignment.id, tenantContext, {
          includeEmployee: true,
          includeInstance: true
        });
      } catch (fetchError) {
        // If fetching fails, return basic assignment data
        console.error('Error fetching assignment details:', fetchError);
        return assignment;
      }

    } catch (error) {
      if (!transaction.finished) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  /**
   * Update assignment
   * @param {number} id - Assignment ID
   * @param {Object} updateData - Update data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated assignment
   */
  async updateAssignment(id, updateData, tenantContext) {
    const assignment = await this.getAssignmentById(id, tenantContext);

    // Check if assignment can be modified
    await this.validateAssignmentModification(assignment, tenantContext);

    // Validate status transitions
    if (updateData.status) {
      this.validateStatusTransition(assignment.status, updateData.status);
    }

    await assignment.update(updateData);

    return await this.getAssignmentById(id, tenantContext, {
      includeEmployee: true,
      includeInstance: true
    });
  }

  /**
   * Remove assignment
   * @param {number} id - Assignment ID
   * @param {Object} removalData - Removal data
   * @param {Object} tenantContext - Tenant context
   */
  async removeAssignment(id, removalData, tenantContext) {
    const assignment = await this.getAssignmentById(id, tenantContext);

    // Check if assignment can be removed
    await this.validateAssignmentModification(assignment, tenantContext);

    const transaction = await sequelize.transaction();
    try {
      await assignment.update({
        status: 'cancelled',
        cancelledAt: new Date(),
        cancelledBy: removalData.removedBy,
        cancellationReason: removalData.reason,
        updatedById: tenantContext.userId
      }, { transaction });

      // Update designation requirement assigned count in junction table
      await this.updateInstanceDesignationAssignedCount(
        assignment.shiftInstanceId,
        assignment.employee.designationId,
        -1, // Decrement by 1
        transaction
      );

      // Update shift instance total assigned count
      const shiftInstance = await RotaShiftInstance.findByPk(assignment.shiftInstanceId, { transaction });
      if (shiftInstance) {
        await shiftInstance.decrement('totalAssigned', { transaction });
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Confirm assignment (employee confirmation)
   * @param {number} id - Assignment ID
   * @param {Object} confirmData - Confirmation data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated assignment
   */
  async confirmAssignment(id, confirmData, tenantContext) {
    const assignment = await this.getAssignmentById(id, tenantContext);

    if (assignment.status !== 'assigned') {
      throw new ValidationError('Only assigned shifts can be confirmed');
    }

    await assignment.update({
      status: 'confirmed',
      confirmedAt: confirmData.confirmedAt,
      confirmedBy: confirmData.confirmedBy,
      confirmationNotes: confirmData.notes,
      updatedById: tenantContext.userId
    });

    return await this.getAssignmentById(id, tenantContext, {
      includeEmployee: true,
      includeInstance: true
    });
  }

  /**
   * Complete assignment
   * @param {number} id - Assignment ID
   * @param {Object} completionData - Completion data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated assignment
   */
  async completeAssignment(id, completionData, tenantContext) {
    const assignment = await this.getAssignmentById(id, tenantContext);

    if (!['assigned', 'confirmed'].includes(assignment.status)) {
      throw new ValidationError('Only assigned or confirmed shifts can be completed');
    }

    await assignment.update({
      status: 'completed',
      completedAt: completionData.completedAt,
      completedBy: completionData.completedBy,
      completionNotes: completionData.notes,
      actualHours: completionData.actualHours,
      updatedById: tenantContext.userId
    });

    return await this.getAssignmentById(id, tenantContext, {
      includeEmployee: true,
      includeInstance: true
    });
  }

  /**
   * Mark assignment as no-show
   * @param {number} id - Assignment ID
   * @param {Object} noShowData - No-show data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated assignment
   */
  async markNoShow(id, noShowData, tenantContext) {
    const assignment = await this.getAssignmentById(id, tenantContext);

    if (!['assigned', 'confirmed'].includes(assignment.status)) {
      throw new ValidationError('Only assigned or confirmed shifts can be marked as no-show');
    }

    await assignment.update({
      status: 'absent',
      noShowAt: noShowData.markedAt,
      noShowBy: noShowData.markedBy,
      noShowReason: noShowData.reason,
      noShowNotes: noShowData.notes,
      updatedById: tenantContext.userId
    });

    return await this.getAssignmentById(id, tenantContext, {
      includeEmployee: true,
      includeInstance: true
    });
  }

  /**
   * Bulk create assignments
   * @param {Array} assignments - Assignments to create
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Bulk creation results
   */
  async bulkCreateAssignments(assignments, tenantContext) {
    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      created: []
    };

    for (let i = 0; i < assignments.length; i++) {
      try {
        const assignment = await this.createAssignment(assignments[i], tenantContext);
        results.created.push(assignment);
        results.successful++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          index: i,
          assignment: assignments[i],
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Bulk update assignments
   * @param {Array} updates - Updates to apply
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Bulk update results
   */
  async bulkUpdateAssignments(updates, tenantContext) {
    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      updated: []
    };

    for (let i = 0; i < updates.length; i++) {
      try {
        const { id, ...updateData } = updates[i];
        const assignment = await this.updateAssignment(id, updateData, tenantContext);
        results.updated.push(assignment);
        results.successful++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          index: i,
          update: updates[i],
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Bulk confirm assignments
   * @param {Array} assignmentIds - Assignment IDs to confirm
   * @param {Object} confirmData - Confirmation data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Bulk confirmation results
   */
  async bulkConfirmAssignments(assignmentIds, confirmData, tenantContext) {
    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      confirmed: []
    };

    for (let i = 0; i < assignmentIds.length; i++) {
      try {
        const assignment = await this.confirmAssignment(assignmentIds[i], confirmData, tenantContext);
        results.confirmed.push(assignment);
        results.successful++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          assignmentId: assignmentIds[i],
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Validate assignment data
   * @param {Object} assignmentData - Assignment data to validate
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async validateAssignmentData(assignmentData, tenantContext, transaction) {
    const { shiftInstanceId, employeeId } = assignmentData;

    // Required fields validation
    if (!shiftInstanceId || !employeeId) {
      throw new ValidationError('Shift instance ID and employee ID are required');
    }

    // Validate instance exists
    const instance = await RotaShiftInstance.findOne({
      where: { id: shiftInstanceId },
      include: [
        {
          model: RotaSchedule,
          as: 'schedule',
          where: {
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId
          }
        }
      ],
      transaction
    });

    if (!instance) {
      throw new ValidationError('Invalid shift instance ID');
    }

    // Validate employee exists and belongs to tenant
    const employee = await Employee.findOne({
      where: {
        id: employeeId,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        status: 'active'
      },
      transaction
    });

    if (!employee) {
      throw new ValidationError('Invalid employee ID');
    }

    return { instance, employee };
  }

  /**
   * Check assignment conflicts
   * @param {Object} assignmentData - Assignment data
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async checkAssignmentConflicts(assignmentData, tenantContext, transaction) {
    const { shiftInstanceId, employeeId } = assignmentData;

    // Check if employee is already assigned to this instance
    const existingAssignment = await ShiftAssignment.findOne({
      where: {
        shiftInstanceId,
        employeeId,
        status: { [Op.notIn]: ['cancelled'] }
      },
      transaction
    });

    if (existingAssignment) {
      throw new ConflictError('Employee is already assigned to this shift instance');
    }

    // Check for overlapping shifts on the same date
    const instance = await RotaShiftInstance.findOne({
      where: { id: shiftInstanceId },
      include: [
        {
          model: RotaShift,
          as: 'rotaShift',
          attributes: ['startTime', 'endTime']
        }
      ],
      transaction
    });

    const overlappingAssignments = await ShiftAssignment.count({
      where: {
        employeeId,
        status: { [Op.notIn]: ['cancelled', 'absent'] }
      },
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: {
            date: instance.date
          },
          include: [
            {
              model: RotaShift,
              as: 'rotaShift',
              where: {
                [Op.or]: [
                  {
                    startTime: {
                      [Op.between]: [instance.rotaShift.startTime, instance.rotaShift.endTime]
                    }
                  },
                  {
                    endTime: {
                      [Op.between]: [instance.rotaShift.startTime, instance.rotaShift.endTime]
                    }
                  }
                ]
              }
            }
          ]
        }
      ],
      transaction
    });

    if (overlappingAssignments > 0) {
      throw new ConflictError('Employee has overlapping shift assignments on this date');
    }
  }

  /**
   * Validate assignment modification permissions
   * @param {Object} assignment - Assignment object
   * @param {Object} tenantContext - Tenant context
   */
  async validateAssignmentModification(assignment, tenantContext) {
    // 🔒 RESTRICTION: Check schedule status restrictions
    const scheduleStatus = assignment.shiftInstance?.schedule?.status;

    if (scheduleStatus === 'published') {
      throw new ValidationError('Cannot modify assignments: Schedule is already published. Assignment modifications are not allowed for published schedules.');
    }

    if (scheduleStatus === 'cancelled') {
      throw new ValidationError('Cannot modify assignments: Schedule is cancelled. Assignment modifications are not allowed for cancelled schedules.');
    }

    if (scheduleStatus === 'archived') {
      throw new ValidationError('Cannot modify assignments: Schedule is archived. Assignment modifications are not allowed for archived schedules.');
    }

    // Check if assignment is already completed
    if (assignment.status === 'completed') {
      const isManager = tenantContext.userRole === 'manager' || tenantContext.userRole === 'admin';
      if (!isManager) {
        throw new ValidationError('Cannot modify completed assignments without manager permissions');
      }
    }
  }

  /**
   * Assign employees to schedule shifts during schedule creation
   * @param {number} scheduleId - Schedule ID
   * @param {Array} assignments - Array of employee assignments
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Assignment results
   */
  async assignEmployeesToSchedule(scheduleId, assignments, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      const results = await this._assignEmployeesToScheduleWithTransaction(
        scheduleId,
        assignments,
        tenantContext,
        transaction
      );

      await transaction.commit();
      return results;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Internal method to assign employees to schedule with existing transaction
   * @param {number} scheduleId - Schedule ID
   * @param {Array} assignments - Array of employee assignments
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Assignment results
   */
  async _assignEmployeesToScheduleWithTransaction(scheduleId, assignments, tenantContext, transaction) {
    // Validate schedule exists and belongs to tenant
    const schedule = await RotaSchedule.findOne({
      where: {
        id: scheduleId,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      },
      transaction
    });

    if (!schedule) {
      throw new NotFoundError('Schedule not found');
    }

    // 🔒 RESTRICTION: Prevent employee assignments to published or cancelled schedules
    if (schedule.status === 'published') {
      throw new ValidationError('Cannot assign employees: Schedule is already published. Employee assignments cannot be modified for published schedules.');
    }

    if (schedule.status === 'cancelled') {
      throw new ValidationError('Cannot assign employees: Schedule is cancelled. Employee assignments cannot be made to cancelled schedules.');
    }

    const results = {
      successful: [],
      failed: [],
      totalProcessed: assignments.length
    };

    for (const assignment of assignments) {
      try {
        // Find shift instance - support both rotaShiftId and date-only lookup
        let shiftInstance;

        if (assignment.shiftInstanceId) {
          // Direct shift instance ID provided
          shiftInstance = await RotaShiftInstance.findOne({
            where: {
              id: assignment.shiftInstanceId,
              scheduleId
            },
            transaction
          });
        } else if (assignment.rotaShiftId) {
          // Find by rotaShiftId and date
          shiftInstance = await RotaShiftInstance.findOne({
            where: {
              scheduleId,
              rotaShiftId: assignment.rotaShiftId,
              date: assignment.date
            },
            transaction
          });
        } else {
          // Find by date only (for schedule creation scenarios)
          shiftInstance = await RotaShiftInstance.findOne({
            where: {
              scheduleId,
              date: assignment.date
            },
            transaction
          });
        }

        if (!shiftInstance) {
          throw new Error(`Shift instance not found for assignment: ${JSON.stringify({
            shiftInstanceId: assignment.shiftInstanceId,
            rotaShiftId: assignment.rotaShiftId,
            date: assignment.date
          })}`);
        }

        const result = await this._assignEmployeeToShiftInstance({
          shiftInstanceId: shiftInstance.id,
          employeeId: assignment.employeeId,
          designationId: assignment.designationId,
          notes: assignment.notes,
          assignmentType: assignment.assignmentType || 'manual_assigned'
        }, tenantContext, transaction);

        results.successful.push(result);
      } catch (error) {
        results.failed.push({
          assignment,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Remove existing assignments for specific dates during schedule update
   * @param {number} scheduleId - Schedule ID
   * @param {Array} dates - Array of dates to clear assignments for
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Removal results
   */
  async _removeAssignmentsForDates(scheduleId, dates, tenantContext, transaction) {
    try {
      // Find all shift instances for the given dates
      const shiftInstances = await RotaShiftInstance.findAll({
        where: {
          scheduleId,
          date: {
            [Op.in]: dates
          }
        },
        transaction
      });

      if (shiftInstances.length === 0) {
        return { removedCount: 0, message: 'No shift instances found for the specified dates' };
      }

      const shiftInstanceIds = shiftInstances.map(instance => instance.id);

      // Remove all assignments for these shift instances
      const removedCount = await ShiftAssignment.destroy({
        where: {
          shiftInstanceId: {
            [Op.in]: shiftInstanceIds
          }
        },
        transaction
      });

      // Update shift instance counters
      for (const instance of shiftInstances) {
        await instance.update({
          totalAssigned: 0
        }, { transaction });
      }

      console.log(`🗑️ Removed ${removedCount} assignments for dates:`, dates);

      return {
        removedCount,
        affectedDates: dates,
        affectedShiftInstances: shiftInstanceIds.length
      };

    } catch (error) {
      console.error('Error removing assignments for dates:', error);
      throw error;
    }
  }

  /**
   * Bulk assign employees during schedule creation
   * @param {number} scheduleId - Schedule ID
   * @param {Array} assignments - Array of shift instance assignments
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Assignment results
   */
  async bulkScheduleAssign(scheduleId, assignments, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      // Validate schedule exists and belongs to tenant
      const schedule = await RotaSchedule.findOne({
        where: {
          id: scheduleId,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        transaction
      });

      if (!schedule) {
        throw new NotFoundError('Schedule not found');
      }

      const results = {
        successful: [],
        failed: [],
        totalProcessed: assignments.length
      };

      for (const assignment of assignments) {
        try {
          const result = await this._assignEmployeeToShiftInstance({
            shiftInstanceId: assignment.shiftInstanceId,
            employeeId: assignment.employeeId,
            designationId: assignment.designationId,
            notes: assignment.notes,
            assignmentType: assignment.assignmentType || 'manual_assigned'
          }, tenantContext, transaction);

          results.successful.push(result);
        } catch (error) {
          results.failed.push({
            assignment,
            error: error.message
          });
        }
      }

      await transaction.commit();
      return results;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Internal method to assign employee to shift instance
   * @param {Object} assignmentData - Assignment data
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Assignment result
   */
  async _assignEmployeeToShiftInstance(assignmentData, tenantContext, transaction) {
    const { shiftInstanceId, employeeId, designationId, notes, assignmentType } = assignmentData;

    // Validate shift instance exists and get schedule status
    const shiftInstance = await RotaShiftInstance.findByPk(shiftInstanceId, {
      include: [{
        model: RotaSchedule,
        as: 'schedule',
        attributes: ['id', 'status']
      }],
      transaction
    });

    if (!shiftInstance) {
      throw new NotFoundError('Shift instance not found');
    }

    // 🔒 RESTRICTION: Prevent employee assignments to published or cancelled schedules
    if (shiftInstance.schedule?.status === 'published') {
      throw new ValidationError('Cannot assign employee: Schedule is already published. Employee assignments cannot be modified for published schedules.');
    }

    if (shiftInstance.schedule?.status === 'cancelled') {
      throw new ValidationError('Cannot assign employee: Schedule is cancelled. Employee assignments cannot be made to cancelled schedules.');
    }

    // Validate employee exists and belongs to tenant
    const employee = await Employee.findOne({
      where: {
        id: employeeId,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        ...(designationId && { designationId })
      },
      include: [{
        model: Designation,
        as: 'designation',
        attributes: ['id', 'name']
      }],
      transaction
    });

    if (!employee) {
      throw new NotFoundError('Employee not found or designation mismatch');
    }

    // Check if employee is already assigned to this shift instance
    const existingAssignment = await ShiftAssignment.findOne({
      where: {
        shiftInstanceId,
        employeeId
      },
      transaction
    });

    if (existingAssignment) {
      throw new ConflictError('Employee already assigned to this shift instance');
    }

    // Create the assignment
    const assignment = await ShiftAssignment.create({
      shiftInstanceId,
      employeeId,
      assignedBy: tenantContext.userId,
      assignmentType: assignmentType || 'manual_assigned',
      status: 'assigned',
      notes,
      designationId:designationId,
      createdById: tenantContext.userId
    }, { transaction });

    // Update shift instance assigned count
    await shiftInstance.increment('totalAssigned', { transaction });

    // Update designation requirement assigned count in junction table
    await this.updateInstanceDesignationAssignedCount(
      shiftInstanceId,
      designationId,
      1, // Increment by 1
      transaction
    );

    return {
      id: assignment.id,
      shiftInstanceId,
      employeeId,
      employee: {
        id: employee.id,
        name: `${employee.firstName} ${employee.lastName}`,
        designation: employee.designation
      },
      status: 'assigned',
      assignedAt: assignment.createdAt
    };
  }

  /**
   * Validate status transitions
   * @param {string} currentStatus - Current status
   * @param {string} newStatus - New status
   */
  validateStatusTransition(currentStatus, newStatus) {
    const validTransitions = {
      'assigned': ['confirmed', 'cancelled', 'absent'],
      'confirmed': ['completed', 'cancelled', 'absent'],
      'completed': [], // Completed assignments cannot be changed (except by managers)
      'cancelled': [], // Cancelled assignments cannot be changed
      'absent': [] // Absent assignments cannot be changed
    };

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      throw new ValidationError(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }

  /**
   * Update assigned count in junction table when assignment is created/deleted
   * @param {number} shiftInstanceId - Shift instance ID
   * @param {number} designationId - Designation ID
   * @param {number} increment - Count to add (+1 for create, -1 for delete)
   * @param {Object} transaction - Database transaction
   */
  async updateInstanceDesignationAssignedCount(shiftInstanceId, designationId, increment, transaction) {
    try {
      await RotaShiftInstanceDesignationRequirement.increment(
        'assignedCount',
        {
          by: increment,
          where: {
            shiftInstanceId: shiftInstanceId,
            designationId: designationId
          },
          transaction
        }
      );
    } catch (error) {
      // If junction table record doesn't exist, create it
      if (error.name === 'SequelizeDatabaseError' && increment > 0) {
        await RotaShiftInstanceDesignationRequirement.create({
          shiftInstanceId,
          designationId,
          requiredCount: 1,
          assignedCount: increment,
          priority: 0,
          requirementType: 'base',
          sourceType: 'manual',
          createdById: 1 // Default system user
        }, { transaction });
      } else {
        throw error;
      }
    }
  }
}

module.exports = new ShiftAssignmentService();
