'use strict';

/**
 * Rota Shift Template Service - PRD Implementation
 * 
 * <PERSON>les shift template business logic according to PRD specifications:
 * - Templates are self-contained (no ShiftType dependency)
 * - Define timing, staffing, designation requirements
 * - Templates are independent of dates/schedules/employees
 * - Support template library features with search and categorization
 */

const { RotaShift, RotaShiftInstance, Department, Designation, Employee, RotaShiftDesignationRequirement } = require('../../data/models');
const { NotFoundError, ValidationError, ConflictError } = require('../../common/errors');
const { Op } = require('sequelize');
const sequelize = require('../../data/models').sequelize;
const moment = require('moment');

class RotaShiftService {

  /**
   * Get all shift templates with advanced filtering
   * @param {Object} filters - Filter options
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Templates with pagination
   */
  async getAllTemplates(filters, tenantContext) {
    const {
      page = 1,
      limit = 10,
      departmentId,
      designationId,
      category,
      search,
      isActive = true,
      sortBy = 'priority',
      sortOrder = 'DESC'
    } = filters;

    const offset = (page - 1) * limit;
    const whereClause = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      isActive
    };

    // Apply filters
    if (departmentId) whereClause.departmentId = departmentId;
    if (designationId) whereClause.designationId = designationId;
    if (category) whereClause.category = category;
    
    if (search) {
      whereClause[Op.or] = [
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const { count, rows: templates } = await RotaShift.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name'],
          require:false
        },
        {
          model: RotaShiftDesignationRequirement,
          as: 'designationRequirements',
          include: [
            {
              model: Designation,
              as: 'designation',
              attributes: ['id', 'name']
            }
          ],
          required: false
        }
      ],
      order: [[sortBy, sortOrder]],
      limit,
      offset,
      distinct: true
    });

    return {
      templates,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
        hasNext: page < Math.ceil(count / limit),
        hasPrev: page > 1
      }
    };
  }

  /**
   * Get shift template by ID with optional usage analytics
   * @param {number} id - Template ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} options - Additional options
   * @returns {Object} Template details
   */
  async getTemplateById(id, tenantContext, options = {}) {
    const template = await RotaShift.findOne({
      where: {
        id,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      },
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: RotaShiftDesignationRequirement,
          as: 'designationRequirements',
          include: [
            {
              model: Designation,
              as: 'designation',
              attributes: ['id', 'name']
            }
          ]
        }
      ]
    });

    if (!template) {
      throw new NotFoundError('Shift template not found');
    }

    // Add usage analytics if requested
    if (options.includeUsage) {
      template.dataValues.usage = await this.getTemplateUsage(id, {}, tenantContext);
    }

    return template;
  }

  /**
   * Create new shift template
   * @param {Object} templateData - Template data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Created template
   */
  async createTemplate(templateData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      const { designationRequirements, ...templateFields } = templateData;

      // Validate template data
      await this.validateTemplateData(templateData, tenantContext);

      const createData = {
        ...templateFields,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        createdById: tenantContext.userId,
        isActive: true
      };

      // Create template
      const template = await RotaShift.create(createData, { transaction });

      // Create designation requirements
      if (designationRequirements && designationRequirements.length > 0) {
        const requirementData = designationRequirements.map(req => ({
          rotaShiftId: template.id,
          designationId: req.designationId,
          requiredCount: req.requiredCount
        }));

        await RotaShiftDesignationRequirement.bulkCreate(requirementData, { transaction });
      }

      await transaction.commit();
      return await this.getTemplateById(template.id, tenantContext);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Update shift template
   * @param {number} id - Template ID
   * @param {Object} updateData - Update data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated template
   */
  async updateTemplate(id, updateData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      const template = await RotaShift.findOne({
        where: {
          id,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        }
      });

      if (!template) {
        throw new NotFoundError('Shift template not found');
      }

      // Check if template is being used in active schedules
      // const isInUse = await this.checkTemplateInUse(id, tenantContext);
      // if (isInUse && this.hasBreakingChanges(template, updateData)) {
      //   throw new ValidationError('Cannot make breaking changes to template that is in use');
      // }

      const { designationRequirements, ...templateFields } = updateData;

      // Validate update data
      await this.validateTemplateData(updateData, tenantContext, id);

      // Update designation requirements if provided
      if (designationRequirements) {
        // Delete existing requirements
        await RotaShiftDesignationRequirement.destroy({
          where: { rotaShiftId: id },
          transaction
        });

        // Create new requirements
        const requirementData = designationRequirements.map(req => ({
          rotaShiftId: id,
          designationId: req.designationId,
          requiredCount: req.requiredCount
        }));

        await RotaShiftDesignationRequirement.bulkCreate(requirementData, { transaction });
      }

      // Update template
      await template.update(templateFields, { transaction });

      await transaction.commit();
      return await this.getTemplateById(id, tenantContext);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Delete shift template (soft delete)
   * @param {number} id - Template ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} options - Delete options
   */
  async deleteTemplate(id, tenantContext, options = {}) {
    const template = await RotaShift.findOne({
      where: {
        id,
        companyId: tenantContext.companyId,
      }
    });

    if (!template) {
      throw new NotFoundError('Shift template not found');
    }

    // Check if template is being used
    const isInUse = await this.checkTemplateInUse(id, tenantContext);
    if (isInUse && !options.force) {
      throw new ValidationError('Cannot delete template that is in use. Use force=true to override.');
    }

    if (options.force) {
      // Hard delete
      await template.destroy();
    } else {
      // Soft delete
      await template.update({ isActive: false });
    }
  }

  /**
   * Duplicate shift template
   * @param {number} id - Template ID to duplicate
   * @param {Object} modifications - Modifications to apply
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Duplicated template
   */
  async duplicateTemplate(id, modifications, tenantContext) {
    const originalTemplate = await this.getTemplateById(id, tenantContext);
    
    const duplicateData = {
      ...originalTemplate.toJSON(),
      ...modifications,
      id: undefined, // Remove ID to create new record
      name: modifications.name || `${originalTemplate.name} (Copy)`,
      createdById: tenantContext.userId,
      updatedById: tenantContext.userId
    };

    // Remove associations data
    delete duplicateData.department;
    delete duplicateData.designation;
    delete duplicateData.usage;

    return await this.createTemplate(duplicateData, tenantContext);
  }

  /**
   * Get template usage analytics
   * @param {number} id - Template ID
   * @param {Object} options - Analytics options
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Usage analytics
   */
  async getTemplateUsage(id, options = {}, tenantContext) {
    const { startDate, endDate, period = '30d' } = options;
    
    let dateFilter = {};
    if (startDate && endDate) {
      dateFilter = {
        date: {
          [Op.between]: [startDate, endDate]
        }
      };
    } else {
      // Default to last 30 days
      const daysBack = parseInt(period.replace('d', '')) || 30;
      dateFilter = {
        date: {
          [Op.gte]: moment().subtract(daysBack, 'days').format('YYYY-MM-DD')
        }
      };
    }

    const instances = await RotaShiftInstance.findAll({
      where: {
        rotaShiftId: id,
        ...dateFilter
      },
      include: [
        {
          model: require('../../data/models').ShiftAssignment,
          as: 'assignments',
          required: false
        }
      ]
    });

    const totalInstances = instances.length;
    const totalAssignments = instances.reduce((sum, instance) => 
      sum + (instance.assignments?.length || 0), 0
    );
    const averageAssignments = totalInstances > 0 ? totalAssignments / totalInstances : 0;

    return {
      totalInstances,
      totalAssignments,
      averageAssignments: Math.round(averageAssignments * 100) / 100,
      period: options.period || '30d',
      dateRange: {
        start: startDate || moment().subtract(30, 'days').format('YYYY-MM-DD'),
        end: endDate || moment().format('YYYY-MM-DD')
      }
    };
  }

  /**
   * Get template categories
   * @param {Object} tenantContext - Tenant context
   * @returns {Array} Categories with counts
   */
  async getTemplateCategories(tenantContext) {
    const categories = await RotaShift.findAll({
      where: {
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        isActive: true,
        category: { [Op.ne]: null }
      },
      attributes: [
        'category',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['category'],
      order: [['category', 'ASC']]
    });

    return categories.map(cat => ({
      name: cat.category,
      count: parseInt(cat.dataValues.count)
    }));
  }

  /**
   * Search templates with advanced filters
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Search results
   */
  async searchTemplates(query, filters, tenantContext) {
    const searchFilters = {
      ...filters,
      search: query
    };

    return await this.getAllTemplates(searchFilters, tenantContext);
  }

  /**
   * Bulk create templates
   * @param {Array} templates - Templates to create
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Bulk creation results
   */
  async bulkCreateTemplates(templates, tenantContext) {
    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      created: []
    };

    for (let i = 0; i < templates.length; i++) {
      try {
        const template = await this.createTemplate(templates[i], tenantContext);
        results.created.push(template);
        results.successful++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          index: i,
          template: templates[i],
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Bulk update templates
   * @param {Array} updates - Updates to apply
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Bulk update results
   */
  async bulkUpdateTemplates(updates, tenantContext) {
    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      updated: []
    };

    for (let i = 0; i < updates.length; i++) {
      try {
        const { id, ...updateData } = updates[i];
        const template = await this.updateTemplate(id, updateData, tenantContext);
        results.updated.push(template);
        results.successful++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          index: i,
          update: updates[i],
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Validate template data
   * @param {Object} templateData - Template data to validate
   * @param {Object} tenantContext - Tenant context
   * @param {number} excludeId - ID to exclude from validation (for updates)
   */
  async validateTemplateData(templateData, tenantContext, excludeId = null) {
    const { name, description, startTime, endTime, departmentId, designationRequirements } = templateData;

    // Required fields validation
    if (!name || !startTime || !endTime) {
      throw new ValidationError('Name, start time, and end time are required');
    }

    // Designation requirements validation
    if (!designationRequirements || !Array.isArray(designationRequirements) || designationRequirements.length === 0) {
      throw new ValidationError('At least one designation requirement is required');
    }

    // Validate each designation requirement
    for (const req of designationRequirements) {
      if (!req.designationId || !req.requiredCount || req.requiredCount < 1) {
        throw new ValidationError('Each designation requirement must have designationId and requiredCount > 0');
      }

      // Validate designation exists
      const designation = await Designation.findOne({
        where: {
          id: req.designationId,
          companyId: tenantContext.companyId
        }
      });
      if (!designation) {
        throw new ValidationError(`Invalid designation ID: ${req.designationId}`);
      }
    }

    // ✅ ENHANCED TIME VALIDATION - Support night shifts
    const start = moment(startTime, 'HH:mm');
    const end = moment(endTime, 'HH:mm');

    if (!start.isValid() || !end.isValid()) {
      throw new ValidationError('Invalid time format. Use HH:mm format');
    }

    // ✅ SUPPORT NIGHT SHIFTS - Allow cross-midnight shifts
    if (start.isSame(end)) {
      throw new ValidationError('Start time and end time cannot be the same');
    }

    // ✅ VALIDATE SHIFT DURATION
    let duration;
    if (end.isAfter(start)) {
      // Regular shift (same day): 09:00 to 17:00
      duration = end.diff(start, 'hours', true);
    } else {
      // Night shift (cross-midnight): 22:00 to 06:00
      const endNextDay = end.clone().add(1, 'day');
      duration = endNextDay.diff(start, 'hours', true);
    }

    if (duration < 1) {
      throw new ValidationError('Shift duration must be at least 1 hour');
    }
    if (duration > 16) {
      throw new ValidationError('Shift duration cannot exceed 16 hours');
    }

    console.log(`✅ Shift validation passed: ${startTime} to ${endTime} (${duration.toFixed(1)} hours)`);
    console.log(`   Shift type: ${end.isAfter(start) ? 'Regular' : 'Night (cross-midnight)'}`);


    // Check for duplicate names
    const whereClause = {
      name,
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      isActive: true
    };

    if (excludeId) {
      whereClause.id = { [Op.ne]: excludeId };
    }

    const existingTemplate = await RotaShift.findOne({ where: whereClause });
    if (existingTemplate) {
      throw new ValidationError('Template with this name already exists');
    }

    // Validate department exists
    if (departmentId) {
      const department = await Department.findOne({
        where: {
          id: departmentId,
          companyId: tenantContext.companyId,
          status: 'active'
        }
      });
      if (!department) {
        throw new ValidationError('Invalid department ID');
      }
    }

    // Designation validation is now handled above in the designationRequirements loop
  }

  /**
   * Check if template is being used in active schedules
   * @param {number} templateId - Template ID
   * @param {Object} tenantContext - Tenant context
   * @returns {boolean} Whether template is in use
   */
  async checkTemplateInUse(templateId, tenantContext) {
    const instanceCount = await RotaShiftInstance.count({
      where: {
        rotaShiftId: templateId
      },
      include: [
        {
          model: require('../../data/models').RotaSchedule,
          as: 'schedule',
          where: {
            status: { [Op.in]: ['draft', 'published'] }
          }
        }
      ]
    });

    return instanceCount > 0;
  }

  /**
   * Check if update contains breaking changes
   * @param {Object} originalTemplate - Original template
   * @param {Object} updateData - Update data
   * @returns {boolean} Whether changes are breaking
   */
  hasBreakingChanges(originalTemplate, updateData) {
    const breakingFields = ['startTime', 'endTime', 'designationId', 'departmentId'];

    return breakingFields.some(field =>
      updateData[field] !== undefined &&
      updateData[field] !== originalTemplate[field]
    );
  }

  /**
   * ✅ HELPER: Calculate shift duration (supports night shifts)
   * @param {string} startTime - Start time (HH:mm)
   * @param {string} endTime - End time (HH:mm)
   * @returns {Object} Duration info
   */
  calculateShiftDuration(startTime, endTime) {
    const start = moment(startTime, 'HH:mm');
    const end = moment(endTime, 'HH:mm');

    let duration, isNightShift;

    if (end.isAfter(start)) {
      // Regular shift (same day)
      duration = end.diff(start, 'hours', true);
      isNightShift = false;
    } else {
      // Night shift (cross-midnight)
      const endNextDay = end.clone().add(1, 'day');
      duration = endNextDay.diff(start, 'hours', true);
      isNightShift = true;
    }

    return {
      hours: Math.floor(duration),
      minutes: Math.round((duration % 1) * 60),
      totalHours: duration,
      isNightShift,
      displayText: `${Math.floor(duration)}h ${Math.round((duration % 1) * 60)}m${isNightShift ? ' (Night Shift)' : ''}`
    };
  }
}

module.exports = new RotaShiftService();
