'use strict';

const RotaInstanceManager = require('./rotaInstanceManager');
const RotaShiftService = require('./rotaShiftService');
const RotaScheduleService = require('./rotaScheduleService');
const RotaForecastService = require('./rotaForecastService');

/**
 * Frontend-Compatible Rota Service Wrapper
 * 
 * This service maintains the existing frontend API while internally using
 * the new 3-layer architecture (Templates + Instances + Assignments).
 * 
 * Frontend continues to use familiar endpoints and data structures.
 * Backend automatically handles instance management and complexity.
 */
class RotaServiceWrapper {
  constructor() {
    this.instanceManager = new RotaInstanceManager();
    this.shiftService = new RotaShiftService();
    this.scheduleService = new RotaScheduleService();
    this.forecastService = new RotaForecastService();
  }

  /**
   * Frontend API: Create schedule with shifts
   * Backend: Auto-creates instances when published
   */
  async createScheduleWithShifts(scheduleData, tenantContext, transaction = null) {
    try {
      console.log('🔄 Creating schedule with frontend-compatible API...');

      // Create schedule using existing service
      const schedule = await this.scheduleService.createSchedule(scheduleData, tenantContext, transaction);

      // If schedule is published, auto-create instances
      if (scheduleData.status === 'published') {
        await this.instanceManager.createInstancesForSchedule(
          schedule.id,
          tenantContext,
          transaction
        );

        // Apply forecast if enabled
        if (scheduleData.enableForecast !== false) {
          await this.applyForecastToSchedule(schedule.id, tenantContext, transaction);
        }
      }

      return schedule;
    } catch (error) {
      console.error('❌ Error creating schedule with shifts:', error);
      throw error;
    }
  }

  /**
   * Frontend API: Assign employee to shift on specific date
   * Backend: Internally finds/creates instance and creates assignment
   */
  async assignEmployeeToShift(assignmentData, tenantContext, transaction = null) {
    try {
      const { scheduleId, shiftId, employeeId, date } = assignmentData;
      
      console.log(`🔄 Assigning employee ${employeeId} to shift ${shiftId} on ${date} (frontend API)...`);

      // Use instance manager for actual assignment
      const assignment = await this.instanceManager.assignEmployeeToShift(
        scheduleId,
        shiftId,
        employeeId,
        date,
        tenantContext,
        transaction
      );

      // Return frontend-compatible response
      return {
        success: true,
        message: 'Employee assigned successfully',
        data: {
          assignmentId: assignment.id,
          shiftId: shiftId,
          employeeId: employeeId,
          date: date,
          status: assignment.status
        }
      };
    } catch (error) {
      console.error('❌ Error assigning employee to shift:', error);
      throw error;
    }
  }

  /**
   * Frontend API: Remove employee from shift
   * Backend: Internally handles instance and assignment cleanup
   */
  async unassignEmployeeFromShift(assignmentData, tenantContext, transaction = null) {
    try {
      const { scheduleId, shiftId, employeeId, date } = assignmentData;
      
      console.log(`🔄 Unassigning employee ${employeeId} from shift ${shiftId} on ${date} (frontend API)...`);

      // Use instance manager for actual unassignment
      const assignment = await this.instanceManager.unassignEmployeeFromShift(
        scheduleId,
        shiftId,
        employeeId,
        date,
        tenantContext,
        transaction
      );

      // Return frontend-compatible response
      return {
        success: true,
        message: 'Employee unassigned successfully',
        data: {
          assignmentId: assignment.id,
          shiftId: shiftId,
          employeeId: employeeId,
          date: date,
          status: assignment.status
        }
      };
    } catch (error) {
      console.error('❌ Error unassigning employee from shift:', error);
      throw error;
    }
  }

  /**
   * Frontend API: Get calendar data
   * Backend: Complex instance queries, return simple view
   */
  async getCalendarData(startDate, endDate, tenantContext, options = {}) {
    try {
      console.log(`🔄 Getting calendar data (frontend API) from ${startDate} to ${endDate}...`);

      // Use instance manager for complex queries
      const calendarData = await this.instanceManager.getCalendarView(
        startDate,
        endDate,
        tenantContext,
        options
      );

      // Return frontend-compatible format
      return {
        success: true,
        data: {
          shifts: calendarData,
          summary: {
            totalShifts: calendarData.length,
            fullyStaffed: calendarData.filter(s => s.isFullyStaffed).length,
            understaffed: calendarData.filter(s => !s.isFullyStaffed).length,
            overstaffed: calendarData.filter(s => s.isOverStaffed).length
          }
        }
      };
    } catch (error) {
      console.error('❌ Error getting calendar data:', error);
      throw error;
    }
  }

  /**
   * Frontend API: Publish schedule
   * Backend: Auto-creates instances and handles status updates
   */
  async publishSchedule(scheduleId, tenantContext, transaction = null) {
    try {
      console.log(`🔄 Publishing schedule ${scheduleId} (frontend API)...`);

      // Update schedule status
      const schedule = await this.scheduleService.updateScheduleStatus(
        scheduleId,
        'published',
        tenantContext,
        transaction
      );

      // Auto-create instances
      await this.instanceManager.handleScheduleStatusChange(
        scheduleId,
        'published',
        tenantContext,
        transaction
      );

      return {
        success: true,
        message: 'Schedule published successfully',
        data: schedule
      };
    } catch (error) {
      console.error('❌ Error publishing schedule:', error);
      throw error;
    }
  }

  /**
   * Frontend API: Update schedule dates
   * Backend: Handles instance creation/deletion automatically
   */
  async updateScheduleDates(scheduleId, dateData, tenantContext, transaction = null) {
    try {
      const { startDate, endDate } = dateData;
      
      console.log(`🔄 Updating schedule ${scheduleId} dates (frontend API)...`);

      // Get current schedule data
      const currentSchedule = await this.scheduleService.getScheduleById(scheduleId, tenantContext);
      const oldStartDate = currentSchedule.startDate;
      const oldEndDate = currentSchedule.endDate;

      // Update schedule
      const updatedSchedule = await this.scheduleService.updateSchedule(
        scheduleId,
        { startDate, endDate },
        tenantContext,
        transaction
      );

      // Handle instance updates
      await this.instanceManager.handleScheduleDateChange(
        scheduleId,
        oldStartDate,
        oldEndDate,
        startDate,
        endDate,
        tenantContext,
        transaction
      );

      return {
        success: true,
        message: 'Schedule dates updated successfully',
        data: updatedSchedule
      };
    } catch (error) {
      console.error('❌ Error updating schedule dates:', error);
      throw error;
    }
  }

  /**
   * Frontend API: Get shift details
   * Backend: Aggregates data from templates and instances
   */
  async getShiftDetails(shiftId, date, tenantContext) {
    try {
      console.log(`🔄 Getting shift ${shiftId} details for ${date} (frontend API)...`);

      // Get template data
      const template = await this.shiftService.getShiftById(shiftId, tenantContext);

      // Get instance data if date provided
      let instanceData = null;
      if (date) {
        const calendarData = await this.instanceManager.getCalendarView(
          date,
          date,
          tenantContext
        );
        instanceData = calendarData.find(shift => shift.id === shiftId);
      }

      // Combine template and instance data
      const shiftDetails = {
        // Template data (always available)
        id: template.id,
        name: template.name,
        description: template.description,
        startTime: template.startTime,
        endTime: template.endTime,
        breakDuration: template.breakDuration,
        baseRequiredCount: template.baseRequiredCount,
        department: template.department,
        designation: template.designation,
        
        // Instance data (if date provided)
        ...(instanceData && {
          date: instanceData.date,
          actualRequiredCount: instanceData.actualRequiredCount,
          assignedCount: instanceData.assignedCount,
          assignments: instanceData.assignments,
          isFullyStaffed: instanceData.isFullyStaffed,
          scheduleStatus: instanceData.scheduleStatus
        })
      };

      return {
        success: true,
        data: shiftDetails
      };
    } catch (error) {
      console.error('❌ Error getting shift details:', error);
      throw error;
    }
  }

  /**
   * Internal method: Handle attendance updates
   * Called by attendance service when check-in/check-out occurs
   */
  async handleAttendanceUpdate(attendanceData, tenantContext, transaction = null) {
    try {
      console.log('🔄 Handling attendance update for rota instances...');

      await this.instanceManager.updateFromAttendance(
        attendanceData,
        tenantContext,
        transaction
      );

      console.log('✅ Attendance update handled for rota instances');
    } catch (error) {
      console.error('❌ Error handling attendance update:', error);
      // Don't throw error to avoid breaking attendance flow
    }
  }

  /**
   * Apply forecast to schedule instances
   * Frontend API: Generate and apply forecast predictions
   */
  async applyForecastToSchedule(scheduleId, tenantContext, transaction = null) {
    try {
      console.log(`🔄 Applying forecast to schedule ${scheduleId} (frontend API)...`);

      // Get schedule date range
      const schedule = await this.scheduleService.getScheduleById(scheduleId, tenantContext);

      // Generate forecast for the schedule period
      const forecasts = await this.forecastService.generateForecast(
        schedule.startDate,
        schedule.endDate,
        tenantContext,
        {
          includeHistoricalAnalysis: true,
          considerSeasonality: true,
          considerSpecialEvents: true
        }
      );

      // Apply forecast to instances
      const updatedInstances = await this.forecastService.applyForecastToInstances(
        scheduleId,
        forecasts,
        tenantContext,
        transaction
      );

      return {
        success: true,
        message: 'Forecast applied successfully',
        data: {
          scheduleId,
          totalForecasts: forecasts.length,
          updatedInstances: updatedInstances.length,
          forecasts: forecasts.filter(f => f.forecastRequiredCount !== f.baseRequiredCount)
        }
      };
    } catch (error) {
      console.error('❌ Error applying forecast to schedule:', error);
      throw error;
    }
  }

  /**
   * Get forecast summary for schedule
   * Frontend API: View forecast predictions and confidence
   */
  async getForecastSummary(scheduleId, tenantContext) {
    try {
      console.log(`🔄 Getting forecast summary for schedule ${scheduleId} (frontend API)...`);

      const schedule = await this.scheduleService.getScheduleById(scheduleId, tenantContext);

      const summary = await this.forecastService.getForecastSummary(
        schedule.startDate,
        schedule.endDate,
        tenantContext
      );

      return {
        success: true,
        data: {
          scheduleId,
          scheduleName: schedule.name,
          dateRange: {
            startDate: schedule.startDate,
            endDate: schedule.endDate
          },
          summary
        }
      };
    } catch (error) {
      console.error('❌ Error getting forecast summary:', error);
      throw error;
    }
  }

  /**
   * Get instance manager for advanced operations
   * Used by other services that need direct access
   */
  getInstanceManager() {
    return this.instanceManager;
  }

  /**
   * Get forecast service for advanced operations
   * Used by other services that need direct access
   */
  getForecastService() {
    return this.forecastService;
  }
}

module.exports = RotaServiceWrapper;
