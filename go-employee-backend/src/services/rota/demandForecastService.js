'use strict';

/**
 * Demand Forecast Service - PRD Implementation
 * 
 * Handles intelligent demand forecasting for optimal scheduling:
 * - Historical data analysis
 * - Predictive algorithms
 * - Seasonal pattern recognition
 * - Integration with auto-scheduling
 */

const {
  DemandForecast,
  DemandForecastInstance,
  DemandForecastDesignationRequirement,
  ShiftAssignment,
  RotaShiftInstance,
  RotaShift,
  RotaShiftDesignationRequirement,
  Department,
  Designation,
  ShiftTemplate,
  ShiftTemplateDayConfig,
  ShiftTemplateDayShift,
  Holiday,
  sequelize
} = require('../../data/models');
const { NotFoundError, ValidationError } = require('../../common/errors');
const { Op } = require('sequelize');
const moment = require('moment');

class DemandForecastService {

  /**
   * Generate demand forecasts for department and date range
   * @param {Object} forecastRequest - Forecast generation request
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Generated forecasts
   */
  async generateDemandForecasts(forecastRequest, tenantContext) {
    const {
      departmentId,
      startDate,
      endDate,
      forecastType = 'historical_average',
      historicalPeriodDays = 30,
      includeSeasonality = true,
      includeEvents = false
    } = forecastRequest;

    const transaction = await sequelize.transaction();

    try {
      // 1. Analyze historical data
      const historicalData = await this.analyzeHistoricalDemand(
        departmentId,
        startDate,
        endDate,
        historicalPeriodDays,
        tenantContext,
        transaction
      );

      // 2. Generate forecasts based on type
      let forecasts = [];
      
      switch (forecastType) {
        case 'historical_average':
          forecasts = await this.generateHistoricalAverageForecasts(
            historicalData,
            departmentId,
            startDate,
            endDate,
            tenantContext
          );
          break;
          
        case 'trend_analysis':
          forecasts = await this.generateTrendBasedForecasts(
            historicalData,
            departmentId,
            startDate,
            endDate,
            tenantContext
          );
          break;
          
        case 'seasonal_pattern':
          forecasts = await this.generateSeasonalForecasts(
            historicalData,
            departmentId,
            startDate,
            endDate,
            includeSeasonality,
            tenantContext
          );
          break;
          
        default:
          forecasts = await this.generateHistoricalAverageForecasts(
            historicalData,
            departmentId,
            startDate,
            endDate,
            tenantContext
          );
      }

      // 3. Apply external factors if enabled
      if (includeEvents) {
        forecasts = await this.applyEventAdjustments(forecasts, tenantContext);
      }

      // 4. Save forecasts to database
      const savedForecasts = await this.saveForecastsToDatabase(
        forecasts,
        forecastType,
        historicalPeriodDays,
        tenantContext,
        transaction
      );

      await transaction.commit();

      return {
        forecasts: savedForecasts,
        metadata: {
          departmentId,
          dateRange: { startDate, endDate },
          forecastType,
          historicalPeriodDays,
          totalForecasts: savedForecasts.length,
          averageConfidence: this.calculateAverageConfidence(savedForecasts)
        }
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get active forecasts for scheduling
   * @param {Object} filters - Filter criteria
   * @param {Object} tenantContext - Tenant context
   * @returns {Array} Active forecasts
   */
  async getActiveForecasts(filters, tenantContext) {
    const {
      departmentId,
      startDate,
      endDate,
      minConfidence = 0,
      forecastTypes = []
    } = filters;

    const whereClause = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      status: 'active',
      confidence: { [Op.gte]: minConfidence }
    };

    if (departmentId) {
      whereClause.departmentId = departmentId;
    }

    if (startDate && endDate) {
      whereClause.date = {
        [Op.between]: [startDate, endDate]
      };
    }

    if (forecastTypes.length > 0) {
      whereClause.forecastType = { [Op.in]: forecastTypes };
    }

    const forecasts = await DemandForecast.findAll({
      where: whereClause,
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        }
      ],
      order: [
        ['date', 'ASC']
      ]
    });

    return forecasts;
  }

  /**
   * Update forecast accuracy after actual data is available
   * @param {number} forecastId - Forecast ID
   * @param {number} actualRequirement - Actual requirement
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated forecast
   */
  async updateForecastAccuracy(forecastId, actualRequirement, tenantContext) {
    const forecast = await DemandForecast.findOne({
      where: {
        id: forecastId,
        companyId: tenantContext.companyId
      }
    });

    if (!forecast) {
      throw new NotFoundError('Forecast not found');
    }

    // TODO: Add actualRequirement and accuracyScore fields to model
    // For now, just update the notes with accuracy information
    const accuracyPercentage = Math.abs(1 - (Math.abs(forecast.requiredCount - actualRequirement) / forecast.requiredCount)) * 100;
    const accuracyNote = `Actual: ${actualRequirement}, Predicted: ${forecast.requiredCount}, Accuracy: ${accuracyPercentage.toFixed(2)}%`;

    await forecast.update({
      notes: forecast.notes ? `${forecast.notes}\n${accuracyNote}` : accuracyNote,
      updatedById: tenantContext.userId
    });

    return forecast;
  }

  /**
   * Get forecast accuracy statistics
   * @param {Object} filters - Filter criteria
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Accuracy statistics
   */
  async getForecastAccuracyStats(filters, tenantContext) {
    const {
      departmentId,
      startDate,
      endDate,
      forecastType
    } = filters;

    const whereClause = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      status: 'active'
    };

    if (departmentId) whereClause.departmentId = departmentId;
    if (forecastType) whereClause.forecastType = forecastType;

    if (startDate && endDate) {
      whereClause.date = {
        [Op.between]: [startDate, endDate]
      };
    }

    // For now, return basic stats without accuracy calculation
    // TODO: Add actualRequirement and accuracyScore fields to model for proper accuracy tracking
    const stats = await DemandForecast.findAll({
      where: whereClause,
      attributes: [
        'forecastType',
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalForecasts'],
        [sequelize.fn('AVG', sequelize.col('confidence')), 'averageConfidence'],
        [sequelize.fn('MIN', sequelize.col('confidence')), 'minConfidence'],
        [sequelize.fn('MAX', sequelize.col('confidence')), 'maxConfidence']
      ],
      group: ['forecastType'],
      raw: true
    });

    // Transform the results to include placeholder accuracy data
    const transformedStats = stats.map(stat => ({
      ...stat,
      averageAccuracy: null, // Will be calculated when accuracy fields are added
      minAccuracy: null,
      maxAccuracy: null
    }));

    return transformedStats;
  }

  // Helper Methods

  /**
   * Analyze historical demand patterns
   * @param {number} departmentId - Department ID
   * @param {string} startDate - Analysis start date
   * @param {string} endDate - Analysis end date
   * @param {number} historicalPeriodDays - Historical period in days
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Historical analysis
   */
  async analyzeHistoricalDemand(departmentId, startDate, endDate, historicalPeriodDays, tenantContext, transaction) {
    const analysisStartDate = moment(startDate).subtract(historicalPeriodDays, 'days').format('YYYY-MM-DD');
    const analysisEndDate = moment(startDate).subtract(1, 'day').format('YYYY-MM-DD');

    // Get historical shift assignments
    const historicalAssignments = await ShiftAssignment.findAll({
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: {
            date: {
              [Op.between]: [analysisStartDate, analysisEndDate]
            }
          },
          include: [
            {
              model: RotaShift,
              as: 'rotaShift',
            }
          ]
        }
      ],
      transaction
    });

    // Group by time slots and calculate patterns
    const timeSlotPatterns = {};
    
    historicalAssignments.forEach(assignment => {
      const shift = assignment.shiftInstance.rotaShift;
      const date = assignment.shiftInstance.date;
      const dayOfWeek = moment(date).day();
      
      const timeSlotKey = `${shift.startTime}-${shift.endTime}`;
      
      if (!timeSlotPatterns[timeSlotKey]) {
        timeSlotPatterns[timeSlotKey] = {
          startTime: shift.startTime,
          endTime: shift.endTime,
          totalAssignments: 0,
          dayPatterns: {},
          weeklyAverage: 0,
          trend: 'stable'
        };
      }
      
      timeSlotPatterns[timeSlotKey].totalAssignments++;
      
      if (!timeSlotPatterns[timeSlotKey].dayPatterns[dayOfWeek]) {
        timeSlotPatterns[timeSlotKey].dayPatterns[dayOfWeek] = 0;
      }
      timeSlotPatterns[timeSlotKey].dayPatterns[dayOfWeek]++;
    });

    // Calculate averages and trends
    Object.keys(timeSlotPatterns).forEach(timeSlot => {
      const pattern = timeSlotPatterns[timeSlot];
      const totalDays = historicalPeriodDays;
      pattern.weeklyAverage = Math.round((pattern.totalAssignments / totalDays) * 7);
      
      // Simple trend analysis (could be enhanced with more sophisticated algorithms)
      pattern.trend = this.calculateTrend(pattern.dayPatterns);
    });

    return {
      timeSlotPatterns,
      analysisStartDate,
      analysisEndDate,
      totalDaysAnalyzed: historicalPeriodDays,
      departmentId
    };
  }

  /**
   * Generate historical average based forecasts
   * @param {Object} historicalData - Historical analysis data
   * @param {number} departmentId - Department ID
   * @param {string} startDate - Forecast start date
   * @param {string} endDate - Forecast end date
   * @param {Object} tenantContext - Tenant context
   * @returns {Array} Generated forecasts
   */
  async generateHistoricalAverageForecasts(historicalData, departmentId, startDate, endDate, tenantContext) {
    const forecasts = [];
    const { timeSlotPatterns } = historicalData;

    const start = moment(startDate);
    const end = moment(endDate);

    for (let date = start.clone(); date.isSameOrBefore(end); date.add(1, 'day')) {
      const dayOfWeek = date.day();
      const currentDate = date.format('YYYY-MM-DD');

      Object.keys(timeSlotPatterns).forEach(timeSlotKey => {
        const pattern = timeSlotPatterns[timeSlotKey];
        const dayPattern = pattern.dayPatterns[dayOfWeek] || 0;
        
        if (dayPattern > 0) {
          const averageRequirement = Math.round(dayPattern / (historicalData.totalDaysAnalyzed / 7));
          const confidence = this.calculateConfidence(pattern, dayOfWeek);

          forecasts.push({
            departmentId,
            date: currentDate,
            startTime: pattern.startTime,
            endTime: pattern.endTime,
            predictedRequirement: Math.max(1, averageRequirement),
            confidence,
            forecastType: 'historical_average',
            baselineRequirement: averageRequirement,
            influencingFactors: {
              historicalAverage: averageRequirement,
              dayOfWeek: dayOfWeek,
              sampleSize: dayPattern
            }
          });
        }
      });
    }

    return forecasts;
  }

  /**
   * Generate trend-based forecasts
   * @param {Object} historicalData - Historical analysis data
   * @param {number} departmentId - Department ID
   * @param {string} startDate - Forecast start date
   * @param {string} endDate - Forecast end date
   * @param {Object} tenantContext - Tenant context
   * @returns {Array} Generated forecasts
   */
  async generateTrendBasedForecasts(historicalData, departmentId, startDate, endDate, tenantContext) {
    // Enhanced trend analysis implementation
    const baseForecasts = await this.generateHistoricalAverageForecasts(
      historicalData, departmentId, startDate, endDate, tenantContext
    );

    // Apply trend adjustments
    return baseForecasts.map(forecast => {
      const trendMultiplier = this.getTrendMultiplier(forecast.influencingFactors);
      
      return {
        ...forecast,
        predictedRequirement: Math.max(1, Math.round(forecast.predictedRequirement * trendMultiplier)),
        forecastType: 'trend_analysis',
        confidence: Math.max(50, forecast.confidence - 10), // Slightly lower confidence for trend-based
        influencingFactors: {
          ...forecast.influencingFactors,
          trendMultiplier
        }
      };
    });
  }

  /**
   * Generate seasonal pattern forecasts
   * @param {Object} historicalData - Historical analysis data
   * @param {number} departmentId - Department ID
   * @param {string} startDate - Forecast start date
   * @param {string} endDate - Forecast end date
   * @param {boolean} includeSeasonality - Include seasonal adjustments
   * @param {Object} tenantContext - Tenant context
   * @returns {Array} Generated forecasts
   */
  async generateSeasonalForecasts(historicalData, departmentId, startDate, endDate, includeSeasonality, tenantContext) {
    const baseForecasts = await this.generateHistoricalAverageForecasts(
      historicalData, departmentId, startDate, endDate, tenantContext
    );

    if (!includeSeasonality) {
      return baseForecasts;
    }

    // Apply seasonal adjustments
    return baseForecasts.map(forecast => {
      const seasonalMultiplier = this.getSeasonalMultiplier(forecast.date);

      return {
        ...forecast,
        predictedRequirement: Math.max(1, Math.round(forecast.predictedRequirement * seasonalMultiplier)),
        forecastType: 'seasonal_pattern',
        confidence: forecast.confidence,
        influencingFactors: {
          ...forecast.influencingFactors,
          seasonalMultiplier,
          season: this.getSeason(forecast.date)
        }
      };
    });
  }

  /**
   * Save forecasts to database
   * @param {Array} forecasts - Generated forecasts
   * @param {string} forecastType - Forecast type
   * @param {number} historicalPeriodDays - Historical period
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Array} Saved forecasts
   */
  async saveForecastsToDatabase(forecasts, forecastType, historicalPeriodDays, tenantContext, transaction) {
    const savedForecasts = [];

    for (const forecast of forecasts) {
      // Check if forecast already exists
      const existingForecast = await DemandForecast.findOne({
        where: {
          companyId: tenantContext.companyId,
          departmentId: forecast.departmentId,
          date: forecast.date,
          startTime: forecast.startTime,
          endTime: forecast.endTime
        },
        transaction
      });

      if (existingForecast) {
        // Update existing forecast
        await existingForecast.update({
          predictedRequirement: forecast.predictedRequirement,
          confidence: forecast.confidence,
          forecastType: forecast.forecastType,
          influencingFactors: forecast.influencingFactors,
          baselineRequirement: forecast.baselineRequirement,
          historicalDataPeriod: historicalPeriodDays,
          status: 'active',
          generatedBy: 'system',
          generatedAt: new Date(),
          updatedById: tenantContext.userId
        }, { transaction });

        savedForecasts.push(existingForecast);
      } else {
        // Create new forecast
        const newForecast = await DemandForecast.create({
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId,
          ...forecast,
          historicalDataPeriod: historicalPeriodDays,
          status: 'active',
          generatedBy: 'system',
          generatedAt: new Date(),
          createdById: tenantContext.userId
        }, { transaction });

        savedForecasts.push(newForecast);
      }
    }

    return savedForecasts;
  }

  // Utility Methods

  calculateTrend(dayPatterns) {
    // Simple trend calculation - could be enhanced
    const values = Object.values(dayPatterns);
    if (values.length < 2) return 'stable';
    
    const avg = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / values.length;
    
    return variance > avg * 0.5 ? 'variable' : 'stable';
  }

  calculateConfidence(pattern, dayOfWeek) {
    const dayCount = pattern.dayPatterns[dayOfWeek] || 0;
    const totalCount = pattern.totalAssignments;
    
    if (totalCount === 0) return 0;
    
    const sampleRatio = dayCount / totalCount;
    const baseConfidence = Math.min(90, sampleRatio * 100 + 50);
    
    return Math.round(baseConfidence);
  }

  getTrendMultiplier(factors) {
    // Simple trend multiplier - could be enhanced with ML
    return 1.0; // Placeholder
  }

  getSeasonalMultiplier(date) {
    const month = moment(date).month();
    
    // Simple seasonal adjustments
    const seasonalFactors = {
      0: 0.9,  // January - lower demand
      1: 0.95, // February
      2: 1.0,  // March
      3: 1.05, // April
      4: 1.1,  // May - higher demand
      5: 1.15, // June
      6: 1.2,  // July - peak
      7: 1.15, // August
      8: 1.1,  // September
      9: 1.05, // October
      10: 1.0, // November
      11: 0.95 // December
    };
    
    return seasonalFactors[month] || 1.0;
  }

  getSeason(date) {
    const month = moment(date).month();
    
    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'autumn';
    return 'winter';
  }

  calculateAverageConfidence(forecasts) {
    if (forecasts.length === 0) return 0;
    
    const totalConfidence = forecasts.reduce((sum, forecast) => sum + forecast.confidence, 0);
    return Math.round(totalConfidence / forecasts.length);
  }

  applyEventAdjustments(forecasts, tenantContext) {
    // Placeholder for event-based adjustments (holidays, special events)
    return forecasts;
  }

  // ==================== BASIC CRUD OPERATIONS ====================

  /**
   * Create a new demand forecast - ENHANCED for 3-table architecture
   * @param {Object} forecastData - Forecast data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Created forecast with instances
   */
  async createForecast(forecastData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      // ✅ SUPPORT BOTH OLD AND NEW FORMATS
      const isLegacyFormat = forecastData.date && forecastData.designationId && forecastData.requiredCount;
      const isNewFormat = forecastData.startDate && forecastData.endDate && forecastData.name;

      if (isLegacyFormat) {
        // ✅ LEGACY SINGLE-DATE FORMAT - Backward compatibility
        const forecast = await DemandForecast.create({
          ...forecastData,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId || forecastData.businessUnitId,
          createdById: tenantContext.userId,
          updatedById: tenantContext.userId
        }, { transaction });

        await transaction.commit();
        return await this.getForecastById(forecast.id, tenantContext);

      } else if (isNewFormat) {
        // ✅ NEW DATE RANGE FORMAT - 3-table architecture
        const {
          name,
          description,
          departmentId,
          templateId,
          startDate,
          endDate,
          forecasts = [], // Array of daily forecasts (legacy support)
          customRequirements = [], // ✅ NEW: Custom requirements (following schedule service pattern)
          forecastType = 'manual',
          // ✅ NEW: Weekend and holiday exclusion options
          excludeWeekends = true,
          excludeHolidays = true
        } = forecastData;

        // Create main forecast record
        const forecast = await DemandForecast.create({
          name,
          description,
          departmentId,
          templateId,
          startDate,
          endDate,
          status: 'draft',
          forecastType,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId,
          createdById: tenantContext.userId,
          updatedById: tenantContext.userId
        }, { transaction });

        // ✅ GENERATE DAILY INSTANCES
        const instances = await this.generateForecastInstances(
          forecast.id,
          startDate,
          endDate,
          forecasts,
          tenantContext,
          transaction,
          templateId, // Pass templateId for auto-generation
          { excludeWeekends, excludeHolidays } // Pass exclusion options
        );

        // ✅ APPLY CUSTOM REQUIREMENTS (following schedule service pattern)
        if (customRequirements && customRequirements.length > 0) {
          console.log(`🔧 Applying ${customRequirements.length} custom requirements`);
          await this.applyCustomRequirements(forecast.id, customRequirements, tenantContext, transaction);
        }

        // Update performance counters
        await forecast.update({
          totalInstances: instances.length,
          totalRequiredCount: instances.reduce((sum, inst) => sum + inst.totalRequiredCount, 0)
        }, { transaction });

        await transaction.commit();
        return await this.getForecastById(forecast.id, tenantContext);

      } else {
        throw new ValidationError('Invalid forecast data format. Provide either legacy format (date, designationId, requiredCount) or new format (name, startDate, endDate)');
      }

    } catch (error) {
      await transaction.rollback();
      throw new ValidationError(`Failed to create forecast: ${error.message}`);
    }
  }

  /**
   * Extract template shifts with their designation requirements
   * @param {number} templateId - Template ID
   * @param {Object} transaction - Database transaction
   * @returns {Array} Array of template shifts
   */
  async extractTemplateShifts(templateId, transaction) {
    console.log(`🎯 Extracting shifts from template ID: ${templateId}`);

    const template = await ShiftTemplate.findByPk(templateId, {
      include: [
        {
          model: ShiftTemplateDayConfig,
          as: 'dayConfigs',
          where: { isActive: true },
          required: false,
          include: [
            {
              model: ShiftTemplateDayShift,
              as: 'dayShifts',
              where: { isActive: true },
              required: false,
              include: [
                {
                  model: RotaShift,
                  as: 'rotaShift',
                  where: { isActive: true },
                  required: false,
                  include: [
                    {
                      model: RotaShiftDesignationRequirement,
                      as: 'designationRequirements',
                      attributes: ['id', 'rotaShiftId', 'designationId', 'requiredCount'],
                      include: [
                        {
                          model: Designation,
                          as: 'designation',
                          attributes: ['id', 'name']
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      transaction
    });

    if (!template) {
      throw new ValidationError(`Template ${templateId} not found`);
    }

    if (!template.dayConfigs || template.dayConfigs.length === 0) {
      console.log(`⚠️ Template ${templateId} has no day configurations`);
      return [];
    }

    console.log(`🔍 Template has ${template.dayConfigs.length} day configurations`);

    // Extract all unique shifts from all day configurations
    const allShifts = new Map();

    for (let dayIndex = 0; dayIndex < template.dayConfigs.length; dayIndex++) {
      const dayConfig = template.dayConfigs[dayIndex];
      console.log(`📅 Day config ${dayIndex + 1}: ${dayConfig.dayOfWeek}, dayShifts: ${dayConfig.dayShifts?.length || 0}`);

      if (dayConfig.dayShifts && dayConfig.dayShifts.length > 0) {
        for (let shiftIndex = 0; shiftIndex < dayConfig.dayShifts.length; shiftIndex++) {
          const dayShift = dayConfig.dayShifts[shiftIndex];
          console.log(`  🔄 Day shift ${shiftIndex + 1}: rotaShiftId: ${dayShift.rotaShiftId}`);

          if (dayShift.rotaShift && !allShifts.has(dayShift.rotaShift.id)) {
            // Get the RotaShift with designation requirements (same as schedule service approach)
            const rotaShift = await RotaShift.findByPk(dayShift.rotaShift.id, {
              include: [{
                model: RotaShiftDesignationRequirement,
                as: 'designationRequirements'
              }],
              transaction
            });

            if (rotaShift) {
              // Build designation requirements (same as schedule service)
              const designationReqs = await this.buildDesignationRequirements(rotaShift, transaction);

              console.log(`    ✅ Adding RotaShift ${rotaShift.id} with ${designationReqs.length} designation requirements`);

              const shiftData = {
                rotaShiftId: rotaShift.id,
                designations: designationReqs
              };

              allShifts.set(rotaShift.id, shiftData);
            }
          }
        }
      }
    }

    const templateShifts = Array.from(allShifts.values());
    console.log(`📋 Found ${templateShifts.length} unique shifts from template`);
    return templateShifts;
  }

  /**
   * Build designation requirements from RotaShift (following schedule service pattern)
   * @param {Object} rotaShift - RotaShift object
   * @param {Object} transaction - Database transaction
   * @returns {Array} Processed designation requirements
   */
  async buildDesignationRequirements(rotaShift, transaction) {
    console.log(`🔍 Building designation requirements for RotaShift ${rotaShift.id}`);

    // Use RotaShift default requirements (same as schedule service line 964-967)
    const shiftReqs = await RotaShiftDesignationRequirement.findAll({
      where: { rotaShiftId: rotaShift.id },
      transaction
    });

    console.log(`📊 Found ${shiftReqs.length} designation requirements from database`);

    const requirements = shiftReqs.map(req => {
      console.log(`  📋 Processing requirement:`, {
        id: req.id,
        designationId: req.designationId,
        requiredCount: req.requiredCount
      });

      return {
        designationId: req.designationId,
        requiredCount: req.requiredCount || 1,
        priority: req.priority || 5,
        confidence: 100,
        forecastMethod: 'template_based'
      };
    });

    console.log(`✅ Built ${requirements.length} designation requirements:`, requirements);
    return requirements;
  }

  /**
   * Process designation requirements from RotaShift (legacy method - keeping for compatibility)
   * @param {Array} designationRequirements - Raw designation requirements
   * @returns {Array} Processed designation requirements
   */
  processDesignationRequirements(designationRequirements) {
    console.log(`🔍 Processing designation requirements:`, {
      input: designationRequirements,
      isArray: Array.isArray(designationRequirements),
      length: designationRequirements?.length || 0
    });

    if (!designationRequirements || designationRequirements.length === 0) {
      console.log(`⚠️ No designation requirements found - returning empty array`);
      return [];
    }

    console.log(`📋 Raw designation requirements:`, JSON.stringify(designationRequirements, null, 2));

    const processed = designationRequirements
      .filter(req => {
        const hasDesignationId = req && req.designationId;
        console.log(`🔍 Filtering requirement:`, {
          req,
          hasDesignationId,
          designationId: req?.designationId
        });
        return hasDesignationId;
      })
      .map(req => {
        const processed = {
          designationId: req.designationId,
          requiredCount: req.requiredCount || 1,
          priority: req.priority || 5,
          confidence: 100,
          forecastMethod: 'template_based'
        };
        console.log(`✅ Processed requirement:`, processed);
        return processed;
      });

    console.log(`📊 Final processed ${processed.length} designation requirements:`, processed);
    return processed;
  }

  /**
   * Check if a date should be excluded based on options
   * @param {string} dateStr - Date string
   * @param {string} dayOfWeek - Day of week
   * @param {Object} options - Exclusion options
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Exclusion result
   */
  async checkDateExclusion(dateStr, dayOfWeek, options, tenantContext, transaction) {
    const { excludeWeekends, excludeHolidays } = options;

    // Check weekend exclusion
    if (excludeWeekends && (dayOfWeek === 'saturday' || dayOfWeek === 'sunday')) {
      return {
        shouldExclude: true,
        reason: `${dayOfWeek} - Weekend`
      };
    }

    // Check holiday exclusion
    if (excludeHolidays) {
      const isHoliday = await this.checkIfHoliday(dateStr, tenantContext, transaction);
      if (isHoliday.isHoliday) {
        return {
          shouldExclude: true,
          reason: `${dayOfWeek} - Holiday: ${isHoliday.holidayName}`
        };
      }
    }

    return {
      shouldExclude: false,
      reason: null
    };
  }

  /**
   * Get shifts for a specific date
   * @param {string} dateStr - Date string
   * @param {Array} forecasts - Manual forecasts
   * @param {Array} templateShifts - Template shifts
   * @returns {Array} Shifts for the date
   */
  getShiftsForDate(dateStr, forecasts, templateShifts) {
    // Find forecast data for this date
    const dayForecast = forecasts.find(f => f.date === dateStr) || {};
    let shifts = dayForecast.shifts || [];

    // If no manual forecast data and we have template shifts, use them
    if (templateShifts.length > 0 && (!dayForecast.shifts || dayForecast.shifts.length === 0)) {
      shifts = templateShifts.map(templateShift => ({
        ...templateShift,
        confidence: 100,
        forecastMethod: 'template_based'
      }));
    }

    return shifts;
  }

  /**
   * Create a forecast instance
   * @param {number} forecastId - Forecast ID
   * @param {string} dateStr - Date string
   * @param {Object} shift - Shift data
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Created instance
   */
  async createForecastInstance(forecastId, dateStr, shift, tenantContext, transaction) {
    // Validate that rotaShiftId is present
    if (!shift.rotaShiftId) {
      throw new ValidationError('RotaShift ID is required for all forecast instances');
    }

    return await DemandForecastInstance.create({
      forecastId,
      date: dateStr,
      rotaShiftId: shift.rotaShiftId,
      totalRequiredCount: shift.designations?.reduce((sum, d) => sum + (d.requiredCount || 0), 0) || 0,
      confidence: shift.confidence || 100,
      forecastMethod: shift.forecastMethod || 'template_based',
      createdById: tenantContext.userId,
      updatedById: tenantContext.userId
    }, { transaction });
  }

  /**
   * Create/Update designation requirements for an instance (with override support)
   * @param {number} instanceId - Instance ID
   * @param {Array} designations - Base designation requirements
   * @param {Array} overrideDesignations - Override designation requirements (optional)
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async createDesignationRequirements(instanceId, designations, tenantContext, transaction, overrideDesignations = []) {
    console.log(`🎯 createDesignationRequirements called with:`, {
      instanceId,
      designations,
      designationsLength: designations?.length || 0,
      isArray: Array.isArray(designations)
    });

    if (!designations || designations.length === 0) {
      console.log(`⚠️ No designations to create for instance ${instanceId} - designations is empty or null`);
      return;
    }

    // Combine base and override designations (following schedule service pattern)
    const allDesignations = [...designations];

    // Create override map for easy lookup
    const overrideMap = {};
    overrideDesignations.forEach(override => {
      overrideMap[override.designationId] = override;
    });

    console.log(`📊 Creating/updating designation requirements for instance ${instanceId}:`, {
      baseDesignations: designations.length,
      overrideDesignations: overrideDesignations.length,
      totalToProcess: allDesignations.length
    });

    for (let i = 0; i < allDesignations.length; i++) {
      const designation = allDesignations[i];
      console.log(`🔄 Processing designation ${i + 1}/${allDesignations.length}:`, designation);

      // Validate designationId
      if (!designation.designationId) {
        console.error(`❌ Invalid designation data at index ${i}:`, designation);
        throw new ValidationError(`Designation ID is required for all designation requirements. Invalid data at index ${i}: ${JSON.stringify(designation)}`);
      }

      // Check if there's an override for this designation
      const override = overrideMap[designation.designationId];
      const finalRequiredCount = override ? override.requiredCount : designation.requiredCount || 1;
      const requirementType = override ? 'override' : 'base';

      console.log(`📋 Designation ${designation.designationId}:`, {
        baseRequiredCount: designation.requiredCount || 1,
        overrideRequiredCount: override?.requiredCount,
        finalRequiredCount,
        requirementType
      });

      // Check if requirement already exists
      const existingRequirement = await DemandForecastDesignationRequirement.findOne({
        where: {
          instanceId,
          designationId: designation.designationId
        },
        transaction
      });

      const requirementData = {
        instanceId,
        designationId: designation.designationId,
        requiredCount: finalRequiredCount,
        priority: designation.priority || 5,
        confidence: designation.confidence || 100,
        forecastMethod: designation.forecastMethod || 'template_based',
        updatedById: tenantContext.userId
      };

      try {
        if (existingRequirement) {
          // UPDATE existing requirement
          await existingRequirement.update(requirementData, { transaction });
          console.log(`✅ Updated existing designation requirement ${designation.designationId} (${requirementType})`);
        } else {
          // CREATE new requirement
          requirementData.createdById = tenantContext.userId;
          const created = await DemandForecastDesignationRequirement.create(requirementData, { transaction });
          console.log(`✅ Created new designation requirement ${designation.designationId} with ID: ${created.id} (${requirementType})`);
        }
      } catch (error) {
        console.error(`❌ Failed to process designation requirement ${designation.designationId}:`, error.message);
        throw error;
      }
    }

    // Process override-only designations (designations that exist in override but not in base)
    for (const override of overrideDesignations) {
      const existsInBase = allDesignations.some(d => d.designationId === override.designationId);
      if (!existsInBase) {
        console.log(`🔄 Processing override-only designation:`, override);

        const requirementData = {
          instanceId,
          designationId: override.designationId,
          requiredCount: override.requiredCount,
          priority: override.priority || 5,
          confidence: override.confidence || 100,
          forecastMethod: override.forecastMethod || 'manual',
          createdById: tenantContext.userId,
          updatedById: tenantContext.userId
        };

        try {
          const created = await DemandForecastDesignationRequirement.create(requirementData, { transaction });
          console.log(`✅ Created override-only designation requirement ${override.designationId} with ID: ${created.id}`);
        } catch (error) {
          console.error(`❌ Failed to create override-only designation requirement ${override.designationId}:`, error.message);
          throw error;
        }
      }
    }

    const totalProcessed = allDesignations.length + overrideDesignations.filter(o => !allDesignations.some(d => d.designationId === o.designationId)).length;
    console.log(`🎉 Successfully processed ${totalProcessed} designation requirements for instance ${instanceId}`);
  }

  /**
   * Apply custom requirements to existing forecast instances (following schedule service pattern)
   * @param {number} forecastId - Forecast ID
   * @param {Array} customRequirements - Custom requirements array
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async applyCustomRequirements(forecastId, customRequirements, tenantContext, transaction) {
    console.log(`🔧 Applying custom requirements to forecast ${forecastId}`);

    for (const customReq of customRequirements) {
      const { date, rotaShiftId, designationRequirements } = customReq;

      console.log(`📅 Processing custom requirement for date: ${date}, rotaShiftId: ${rotaShiftId}`);

      // Find the specific forecast instance for this date+rotaShiftId
      const instance = await DemandForecastInstance.findOne({
        where: {
          forecastId,
          date,
          rotaShiftId
        },
        transaction
      });

      if (!instance) {
        console.log(`⚠️ No instance found for date: ${date}, rotaShiftId: ${rotaShiftId} - skipping`);
        continue;
      }

      console.log(`✅ Found instance ${instance.id} for date: ${date}, rotaShiftId: ${rotaShiftId}`);

      // Update designation requirements efficiently (update existing, create new, remove unused)
      let totalRequiredCount = 0;

      // Get existing designation requirements for this instance
      const existingRequirements = await DemandForecastDesignationRequirement.findAll({
        where: { instanceId: instance.id },
        transaction
      });

      console.log(`🔍 Found ${existingRequirements.length} existing designation requirements for instance ${instance.id}`);

      // Create maps for efficient processing
      const existingMap = {};
      existingRequirements.forEach(req => {
        existingMap[req.designationId] = req;
      });

      const newDesignationIds = designationRequirements.map(req => req.designationId);

      // Process each new designation requirement
      for (const req of designationRequirements) {
        const requirementData = {
          requiredCount: req.requiredCount,
          priority: req.priority || 5,
          confidence: req.confidence || 100,
          forecastMethod: req.forecastMethod || 'manual',
          updatedById: tenantContext.userId
        };

        if (existingMap[req.designationId]) {
          // UPDATE existing requirement
          await existingMap[req.designationId].update(requirementData, { transaction });
          console.log(`🔄 Updated designation requirement: designationId ${req.designationId}, requiredCount ${req.requiredCount}`);
        } else {
          // CREATE new requirement
          await DemandForecastDesignationRequirement.create({
            instanceId: instance.id,
            designationId: req.designationId,
            ...requirementData,
            createdById: tenantContext.userId
          }, { transaction });
          console.log(`✅ Created new designation requirement: designationId ${req.designationId}, requiredCount ${req.requiredCount}`);
        }

        totalRequiredCount += req.requiredCount;
      }

      // Remove designation requirements that are no longer needed
      const requirementsToRemove = existingRequirements.filter(req =>
        !newDesignationIds.includes(req.designationId)
      );

      // if (requirementsToRemove.length > 0) {
      //   const idsToRemove = requirementsToRemove.map(req => req.id);
      //   await DemandForecastDesignationRequirement.destroy({
      //     where: { id: idsToRemove },
      //     transaction
      //   });
      //   console.log(`🗑️ Removed ${requirementsToRemove.length} unused designation requirements`);
      // }

      // Update instance total required count
      await instance.update({
        totalRequiredCount,
        updatedById: tenantContext.userId
      }, { transaction });

      console.log(`📊 Updated instance ${instance.id} totalRequiredCount to ${totalRequiredCount}`);
    }

    console.log(`🎉 Successfully applied ${customRequirements.length} custom requirements`);
  }

  /**
   * Update designation requirements for a forecast instance (following schedule service pattern)
   * @param {number} instanceId - Instance ID
   * @param {Array} designationRequirements - New designation requirements
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated instance
   */
  async updateInstanceDesignationRequirements(instanceId, designationRequirements, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      // Find the forecast instance
      const instance = await DemandForecastInstance.findOne({
        where: {
          id: instanceId,
          // Add tenant context validation if needed
        },
        transaction
      });

      if (!instance) {
        throw new NotFoundError('Forecast instance not found');
      }

      console.log(`🔧 Updating designation requirements for forecast instance ${instanceId}`);

      // Remove existing requirements
      await DemandForecastDesignationRequirement.destroy({
        where: { instanceId },
        transaction
      });

      // Create new requirements
      if (designationRequirements && designationRequirements.length > 0) {
        for (const req of designationRequirements) {
          const requirementData = {
            instanceId,
            designationId: req.designationId,
            requiredCount: req.requiredCount || 1,
            priority: req.priority || 5,
            confidence: req.confidence || 100,
            forecastMethod: req.forecastMethod || 'manual',
            createdById: tenantContext.userId,
            updatedById: tenantContext.userId
          };

          await DemandForecastDesignationRequirement.create(requirementData, { transaction });
          console.log(`✅ Created designation requirement for designation ${req.designationId}`);
        }
      }

      // Update instance totals
      const totalRequired = designationRequirements.reduce((sum, req) => sum + (req.requiredCount || 1), 0);
      await instance.update({
        totalRequiredCount: totalRequired
      }, { transaction });

      await transaction.commit();

      // Return updated instance with requirements
      return await DemandForecastInstance.findByPk(instanceId, {
        include: [
          {
            model: DemandForecastDesignationRequirement,
            as: 'designationRequirements',
            include: [
              {
                model: Designation,
                as: 'designation',
                attributes: ['id', 'name']
              }
            ]
          }
        ]
      });

    } catch (error) {
      await transaction.rollback();
      console.error(`❌ Error updating designation requirements for instance ${instanceId}:`, error.message);
      throw error;
    }
  }

  /**
   * Generate daily forecast instances for a date range
   * @param {number} forecastId - Parent forecast ID
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {Array} forecasts - Array of daily forecast data
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @param {number} templateId - Optional template ID for auto-generation
   * @param {Object} options - Additional options
   * @param {boolean} options.excludeWeekends - Whether to exclude weekends (default: true)
   * @param {boolean} options.excludeHolidays - Whether to exclude holidays (default: true)
   * @returns {Array} Created instances
   */
  async generateForecastInstances(forecastId, startDate, endDate, forecasts, tenantContext, transaction, templateId = null, options = {}) {
    const instances = [];
    const start = moment(startDate);
    const end = moment(endDate);

    // Extract options with defaults
    const {
      excludeWeekends = true,
      excludeHolidays = true
    } = options;

    console.log(`🎯 Generating forecast instances with options:`, {
      excludeWeekends,
      excludeHolidays,
      templateId
    });

    // Get template shifts if templateId is provided and forecasts array is empty
    let templateShifts = [];
    if (templateId && (!forecasts || forecasts.length === 0)) {
      templateShifts = await this.extractTemplateShifts(templateId, transaction);
    }
    // Create instances for each date in range
    const skippedDates = [];
    for (let date = start.clone(); date.isSameOrBefore(end); date.add(1, 'day')) {
      const dateStr = date.format('YYYY-MM-DD');
      const dayOfWeek = date.format('dddd').toLowerCase();

      console.log(`📅 Processing forecast date: ${dateStr} (${dayOfWeek})`);

      // Check if date should be excluded
      const exclusionCheck = await this.checkDateExclusion(dateStr, dayOfWeek, options, tenantContext, transaction);
      if (exclusionCheck.shouldExclude) {
        console.log(`⏭️ Skipping ${dateStr} - ${exclusionCheck.reason}`);
        skippedDates.push(`${dateStr} (${exclusionCheck.reason})`);
        continue;
      }

      // Get shifts for this date
      const shifts = this.getShiftsForDate(dateStr, forecasts, templateShifts);
      if (shifts.length === 0) {
        console.log(`⚠️ Skipping ${dateStr} - No shifts available`);
        skippedDates.push(`${dateStr} - No shifts available`);
        continue;
      }

      // Create instances for each shift
      console.log(`🔄 Creating instances for ${shifts.length} shifts on ${dateStr}`);
      for (let shiftIndex = 0; shiftIndex < shifts.length; shiftIndex++) {
        const shift = shifts[shiftIndex];
        console.log(`📋 Processing shift ${shiftIndex + 1}/${shifts.length} for ${dateStr}:`, {
          rotaShiftId: shift.rotaShiftId,
          designationsCount: shift.designations?.length || 0,
          designations: shift.designations
        });

        const instance = await this.createForecastInstance(forecastId, dateStr, shift, tenantContext, transaction);
        console.log(`✅ Created instance ${instance.id} for shift ${shift.rotaShiftId} on ${dateStr}`);

        // Check if there are designation overrides for this shift/date
        const dayForecast = forecasts.find(f => f.date === dateStr);
        const shiftOverride = dayForecast?.shifts?.find(s => s.rotaShiftId === shift.rotaShiftId);
        const overrideDesignations = shiftOverride?.designationOverrides || [];

        await this.createDesignationRequirements(instance.id, shift.designations, tenantContext, transaction, overrideDesignations);
        instances.push(instance);
      }
    }

    // Log summary of skipped dates
    if (skippedDates.length > 0) {
      console.log(`📊 Skipped ${skippedDates.length} dates:`, skippedDates.join(', '));
    }

    console.log(`✅ Generated ${instances.length} forecast instances`);
    return instances;
  }

  /**
   * Check if a date is a holiday
   * @param {string} dateStr - Date string (YYYY-MM-DD)
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Holiday check result
   */
  async checkIfHoliday(dateStr, tenantContext, transaction) {
    try {
      console.log(`🔍 Checking if ${dateStr} is a holiday`);

      // Check for holidays in the company/business unit
      const holiday = await Holiday.findOne({
        where: {
          date: dateStr,
          companyId: tenantContext.companyId,
          [Op.or]: [
            { businessUnitId: tenantContext.businessUnitId },
            { businessUnitId: null }, // Company-wide holidays
            { isCompanyWide: true }
          ]
        },
        transaction
      });

      if (holiday) {
        console.log(`🏖️ Found holiday: ${holiday.name} on ${dateStr}`);
        return {
          isHoliday: true,
          holidayName: holiday.name,
          holidayType: holiday.optionalHoliday ? 'optional' : 'mandatory',
          isRecurring: holiday.isRecurring,
          holiday: holiday
        };
      }

      console.log(`✅ ${dateStr} is not a holiday`);
      return {
        isHoliday: false,
        holidayName: null,
        holidayType: null,
        isRecurring: false,
        holiday: null
      };

    } catch (error) {
      console.error(`❌ Error checking holiday for ${dateStr}:`, error.message);
      // Don't throw error - assume not a holiday if check fails
      return {
        isHoliday: false,
        holidayName: null,
        holidayType: null,
        isRecurring: false,
        holiday: null,
        error: error.message
      };
    }
  }

  /**
   * Regenerate forecast instances when date range changes
   * @param {number} forecastId - Forecast ID
   * @param {string} startDate - New start date
   * @param {string} endDate - New end date
   * @param {Array} forecasts - Updated forecast data
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async regenerateForecastInstances(forecastId, startDate, endDate, forecasts, tenantContext, transaction) {
    // Delete existing instances
    await DemandForecastInstance.destroy({
      where: { forecastId },
      transaction
    });

    // Get the forecast to check if it has a templateId and options
    const forecast = await DemandForecast.findByPk(forecastId, { transaction });
    const templateId = forecast ? forecast.templateId : null;

    // Use default options for regeneration (can be enhanced later to store options in forecast)
    const options = {
      excludeWeekends: true,
      excludeHolidays: true
    };

    // Generate new instances
    return await this.generateForecastInstances(
      forecastId,
      startDate,
      endDate,
      forecasts,
      tenantContext,
      transaction,
      templateId,
      options
    );
  }

  /**
   * Update specific forecast instances
   * @param {number} forecastId - Forecast ID
   * @param {Array} forecasts - Array of forecast updates
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async updateForecastInstances(forecastId, forecasts, tenantContext, transaction) {
    for (const dayForecast of forecasts) {
      const { date, shifts = [] } = dayForecast;

      for (const shift of shifts) {
        // Find existing instance by rotaShiftId
        const instance = await DemandForecastInstance.findOne({
          where: {
            forecastId,
            date,
            rotaShiftId: shift.rotaShiftId
          }
        });

        if (instance) {
          // Update existing instance
          await instance.update({
            confidence: shift.confidence,
            totalRequiredCount: shift.designations?.reduce((sum, d) => sum + (d.requiredCount || 0), 0) || 0,
            updatedById: tenantContext.userId
          }, { transaction });

          // Update designation requirements with override support
          if (shift.designations) {
            // Delete existing requirements
            await DemandForecastDesignationRequirement.destroy({
              where: { instanceId: instance.id },
              transaction
            });

            // Get override designations if provided
            const overrideDesignations = shift.designationOverrides || [];

            // Create new requirements with override support
            await this.createDesignationRequirements(
              instance.id,
              shift.designations,
              tenantContext,
              transaction,
              overrideDesignations
            );
          }
        }
      }
    }
  }

  /**
   * Get all forecasts with filtering and pagination
   * @param {Object} filters - Query filters
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Forecasts with pagination
   */
  async getForecasts(filters, tenantContext) {
    try {
      const {
        page = 1,
        limit = 10,
        departmentId,
        designationId,
        status = 'active',
        forecastType,
        startDate,
        endDate,
        minConfidence = 0
      } = filters;

      const offset = (page - 1) * limit;

      const whereClause = {
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        status,
        confidence: { [Op.gte]: minConfidence }
      };

      if (departmentId) {
        whereClause.departmentId = departmentId;
      }

      if (designationId) {
        whereClause.designationId = designationId;
      }

      if (forecastType) {
        whereClause.forecastType = forecastType;
      }

      if (startDate && endDate) {
        whereClause.date = {
          [Op.between]: [startDate, endDate]
        };
      } else if (startDate) {
        whereClause.date = { [Op.gte]: startDate };
      } else if (endDate) {
        whereClause.date = { [Op.lte]: endDate };
      }

      const { count, rows: forecasts } = await DemandForecast.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name']
          },
          {
            model: Designation,
            as: 'designation',
            attributes: ['id', 'name', 'code']
          }
        ],
        order: [['date', 'ASC'], ['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      const totalPages = Math.ceil(count / limit);

      return {
        forecasts,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      throw new ValidationError(`Failed to get forecasts: ${error.message}`);
    }
  }

  /**
   * Get forecast by ID
   * @param {number} forecastId - Forecast ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Forecast details
   */
  async getForecastById(forecastId, tenantContext) {
    try {
      const forecast = await DemandForecast.findOne({
        where: {
          id: forecastId,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        include: [
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name', 'code']
          },
          {
            model: Designation,
            as: 'designation',
            attributes: ['id', 'name', 'code']
          },
          {
            model: DemandForecastInstance,
            as: 'instances',
            include: [
              {
                model: RotaShift,
                as: 'rotaShift',
                attributes: ['id', 'name', 'startTime', 'endTime'],
                required: false
              },
              {
                model: DemandForecastDesignationRequirement,
                as: 'designationRequirements',
                include: [
                  {
                    model: Designation,
                    as: 'designation',
                    attributes: ['id', 'name', 'code']
                  }
                ]
              }
            ]
          }
        ]
      });

      if (!forecast) {
        throw new NotFoundError('Forecast not found');
      }

      return forecast;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new ValidationError(`Failed to get forecast: ${error.message}`);
    }
  }

  /**
   * Update forecast by ID - ENHANCED for 3-table architecture
   * @param {number} forecastId - Forecast ID
   * @param {Object} updateData - Update data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated forecast
   */
  async updateForecast(forecastId, updateData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      const forecast = await DemandForecast.findOne({
        where: {
          id: forecastId,
          companyId: tenantContext.companyId,
        }
      });

      if (!forecast) {
        throw new NotFoundError('Forecast not found');
      }

      // ✅ DETERMINE UPDATE TYPE
      const isLegacyUpdate = updateData.date || updateData.designationId || updateData.requiredCount;
      const isNewUpdate = updateData.name || updateData.startDate || updateData.endDate || updateData.forecasts;

      if (isLegacyUpdate) {
        // ✅ LEGACY UPDATE - Simple field updates
        await forecast.update({
          ...updateData,
          updatedById: tenantContext.userId
        }, { transaction });

      } else if (isNewUpdate) {
        // ✅ NEW FORMAT UPDATE - Handle instances and requirements
        const {
          name,
          description,
          departmentId,
          templateId,
          startDate,
          endDate,
          status,
          forecasts = [], // Legacy support
          customRequirements = [], // ✅ NEW: Custom requirements support
          ...otherFields
        } = updateData;

        // Update main forecast record
        const mainUpdateData = {
          ...otherFields,
          updatedById: tenantContext.userId
        };

        if (name !== undefined) mainUpdateData.name = name;
        if (description !== undefined) mainUpdateData.description = description;
        if (departmentId !== undefined) mainUpdateData.departmentId = departmentId;
        if (templateId !== undefined) mainUpdateData.templateId = templateId;
        if (status !== undefined) mainUpdateData.status = status;

        await forecast.update(mainUpdateData, { transaction });

        // ✅ HANDLE DATE RANGE CHANGES
        if (startDate || endDate) {
          const newStartDate = startDate || forecast.startDate;
          const newEndDate = endDate || forecast.endDate;

          // Update date range
          await forecast.update({
            startDate: newStartDate,
            endDate: newEndDate
          }, { transaction });

          // Only regenerate if date range actually changed
          const dateRangeChanged = (
            (startDate && startDate !== forecast.startDate) ||
            (endDate && endDate !== forecast.endDate)
          );

          if (dateRangeChanged) {
            console.log(`📅 Date range changed, regenerating instances`);
            await this.regenerateForecastInstances(
              forecastId,
              newStartDate,
              newEndDate,
              forecasts,
              tenantContext,
              transaction
            );
          } else {
            console.log(`📅 Date range unchanged, skipping regeneration`);
          }
        }

        // ✅ UPDATE SPECIFIC FORECASTS/INSTANCES (Legacy support)
        if (forecasts && forecasts.length > 0) {
          await this.updateForecastInstances(
            forecastId,
            forecasts,
            tenantContext,
            transaction
          );
        }

        // ✅ APPLY CUSTOM REQUIREMENTS (following schedule service pattern)
        if (customRequirements && customRequirements.length > 0) {
          console.log(`🔧 Applying ${customRequirements.length} custom requirements in update`);

          // Check if instances exist, if not create them first
          const existingInstances = await DemandForecastInstance.count({
            where: { forecastId },
            transaction
          });

          if (existingInstances === 0) {
            console.log(`📋 No instances found, creating from template first`);
            await this.generateForecastInstances(
              forecastId,
              forecast.startDate,
              forecast.endDate,
              [],
              tenantContext,
              transaction,
              forecast.templateId,
              { excludeWeekends: true, excludeHolidays: true }
            );
          }

          await this.applyCustomRequirements(forecastId, customRequirements, tenantContext, transaction);
        }

        // Update performance counters
        const instances = await DemandForecastInstance.findAll({
          where: { forecastId },
          include: [{
            model: DemandForecastDesignationRequirement,
            as: 'designationRequirements'
          }]
        });

        const totalRequiredCount = instances.reduce((sum, instance) => {
          return sum + (instance.designationRequirements?.reduce((instSum, req) =>
            instSum + req.requiredCount, 0) || 0);
        }, 0);

        await forecast.update({
          totalInstances: instances.length,
          totalRequiredCount
        }, { transaction });

      } else {
        // ✅ SIMPLE FIELD UPDATES
        await forecast.update({
          ...updateData,
          updatedById: tenantContext.userId
        }, { transaction });
      }

      await transaction.commit();
      return await this.getForecastById(forecastId, tenantContext);

    } catch (error) {
      await transaction.rollback();
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new ValidationError(`Failed to update forecast: ${error.message}`);
    }
  }

  /**
   * Delete forecast by ID with proper cascade and validation
   * @param {number} forecastId - Forecast ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} options - Delete options
   * @param {boolean} options.force - Force delete (hard delete)
   * @param {boolean} options.cascade - Delete related data
   * @returns {Object} Delete result with statistics
   */
  async deleteForecast(forecastId, tenantContext, options = {}) {
    const transaction = await sequelize.transaction();

    try {
      const { force = false, cascade = true } = options;

      console.log(`🗑️ Deleting forecast ${forecastId} with options:`, { force, cascade });

      // Find forecast with related data for validation
      const forecast = await DemandForecast.findOne({
        where: {
          id: forecastId,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        include: [
          {
            model: DemandForecastInstance,
            as: 'instances',
            include: [
              {
                model: DemandForecastDesignationRequirement,
                as: 'designationRequirements'
              }
            ]
          }
        ],
        transaction
      });

      if (!forecast) {
        throw new NotFoundError('Forecast not found');
      }

      // Validation checks
      await this.validateForecastDeletion(forecast, tenantContext, transaction);

      // Collect statistics before deletion
      const stats = {
        forecastId: forecast.id,
        forecastName: forecast.name,
        instancesCount: forecast.instances?.length || 0,
        designationRequirementsCount: 0,
        deletedAt: new Date(),
        deletedBy: tenantContext.userId,
        deleteType: force ? 'hard' : 'soft'
      };

      // Count designation requirements
      if (forecast.instances) {
        stats.designationRequirementsCount = forecast.instances.reduce((total, instance) => {
          return total + (instance.designationRequirements?.length || 0);
        }, 0);
      }

      console.log(`📊 Forecast deletion statistics:`, stats);

      if (cascade) {
        // Delete related data in proper order
        await this.cascadeDeleteForecastData(forecastId, transaction);
      }

      // Delete main forecast
      if (force) {
        await forecast.destroy({ force: true, transaction });
        console.log(`💥 Hard deleted forecast ${forecastId}`);
      } else {
        await forecast.destroy({ transaction });
        console.log(`🗑️ Soft deleted forecast ${forecastId}`);
      }

      await transaction.commit();

      return {
        success: true,
        message: `Forecast deleted successfully`,
        statistics: stats
      };

    } catch (error) {
      await transaction.rollback();
      console.error(`❌ Failed to delete forecast ${forecastId}:`, error.message);

      if (error instanceof NotFoundError || error instanceof ValidationError) {
        throw error;
      }
      throw new ValidationError(`Failed to delete forecast: ${error.message}`);
    }
  }

  /**
   * Validate if forecast can be deleted
   * @param {Object} forecast - Forecast object
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async validateForecastDeletion(forecast, tenantContext, transaction) {
    console.log(`🔍 Validating forecast deletion for ${forecast.id}`);

    // Check if forecast is being used in active schedules or other dependencies
    // This is where you can add business logic validations

    // Example validations:
    if (forecast.status === 'active') {
      console.log(`⚠️ Warning: Deleting active forecast ${forecast.id}`);
      // You might want to throw an error here based on business rules
      // throw new ValidationError('Cannot delete active forecast. Please deactivate first.');
    }

    // Check for dependencies (example: if forecast is referenced elsewhere)
    // const dependencies = await this.checkForecastDependencies(forecast.id, transaction);
    // if (dependencies.length > 0) {
    //   throw new ValidationError(`Cannot delete forecast. It has ${dependencies.length} dependencies.`);
    // }

    console.log(`✅ Forecast ${forecast.id} validation passed`);
  }

  /**
   * Cascade delete forecast related data
   * @param {number} forecastId - Forecast ID
   * @param {Object} transaction - Database transaction
   */
  async cascadeDeleteForecastData(forecastId, transaction) {
    console.log(`🔄 Cascade deleting data for forecast ${forecastId}`);

    // Get all instances for this forecast
    const instances = await DemandForecastInstance.findAll({
      where: { forecastId },
      transaction
    });

    const instanceIds = instances.map(instance => instance.id);
    console.log(`📋 Found ${instances.length} instances to delete`);

    if (instanceIds.length > 0) {
      // Delete designation requirements first (child records)
      const deletedRequirements = await DemandForecastDesignationRequirement.destroy({
        where: { instanceId: instanceIds },
        transaction
      });
      console.log(`🗑️ Deleted ${deletedRequirements} designation requirements`);

      // Delete instances (parent records)
      const deletedInstances = await DemandForecastInstance.destroy({
        where: { id: instanceIds },
        transaction
      });
      console.log(`🗑️ Deleted ${deletedInstances} forecast instances`);
    }

    console.log(`✅ Cascade deletion completed for forecast ${forecastId}`);
  }

  /**
   * Bulk delete forecasts with validation
   * @param {Array} forecastIds - Array of forecast IDs
   * @param {Object} tenantContext - Tenant context
   * @param {Object} options - Delete options
   * @returns {Object} Bulk delete result
   */
  async bulkDeleteForecasts(forecastIds, tenantContext, options = {}) {
    const transaction = await sequelize.transaction();

    try {
      console.log(`🗑️ Bulk deleting ${forecastIds.length} forecasts`);

      const results = {
        successful: [],
        failed: [],
        statistics: {
          totalRequested: forecastIds.length,
          totalDeleted: 0,
          totalFailed: 0,
          totalInstancesDeleted: 0,
          totalRequirementsDeleted: 0
        }
      };

      for (const forecastId of forecastIds) {
        try {
          const result = await this.deleteForecast(forecastId, tenantContext, options);
          results.successful.push({
            forecastId,
            result
          });
          results.statistics.totalDeleted++;
          results.statistics.totalInstancesDeleted += result.statistics.instancesCount;
          results.statistics.totalRequirementsDeleted += result.statistics.designationRequirementsCount;
        } catch (error) {
          results.failed.push({
            forecastId,
            error: error.message
          });
          results.statistics.totalFailed++;
        }
      }

      await transaction.commit();

      console.log(`📊 Bulk delete completed:`, results.statistics);
      return results;

    } catch (error) {
      await transaction.rollback();
      console.error(`❌ Bulk delete failed:`, error.message);
      throw new ValidationError(`Bulk delete failed: ${error.message}`);
    }
  }

  /**
   * Generate Excel template for demand forecast upload
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Excel template data
   */
  async generateExcelTemplate(tenantContext) {
    try {
      // Get departments and designations for dropdown data
      const departments = await Department.findAll({
        where: {
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        attributes: ['id', 'name']
      });

      const designations = await Designation.findAll({
        where: {
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        attributes: ['id', 'name', 'code']
      });

      // Create sample data with proper format
      const sampleData = [
        {
          'Date (YYYY-MM-DD)': '2025-08-01',
          'Department ID': departments[0]?.id || 1,
          'Department Name': departments[0]?.name || 'Sample Department',
          'Designation ID': designations[0]?.id || 1,
          'Designation Name': designations[0]?.name || 'Sample Designation',
          'Required Count': 5,
          'Priority (1-10)': 5,
          'Confidence (0-100)': 85,
          'Forecast Type': 'historical_average',
          'Valid From (YYYY-MM-DD)': '2025-08-01',
          'Valid To (YYYY-MM-DD)': '2025-08-31',
          'Notes': 'Sample forecast entry'
        },
        {
          'Date (YYYY-MM-DD)': '2025-08-02',
          'Department ID': departments[0]?.id || 1,
          'Department Name': departments[0]?.name || 'Sample Department',
          'Designation ID': designations[0]?.id || 1,
          'Designation Name': designations[0]?.name || 'Sample Designation',
          'Required Count': 3,
          'Priority (1-10)': 7,
          'Confidence (0-100)': 90,
          'Forecast Type': 'trend_analysis',
          'Valid From (YYYY-MM-DD)': '2025-08-01',
          'Valid To (YYYY-MM-DD)': '2025-08-31',
          'Notes': 'Weekend forecast'
        }
      ];

      return {
        templateData: sampleData,
        metadata: {
          departments: departments.map(d => ({ id: d.id, name: d.name })),
          designations: designations.map(d => ({ id: d.id, name: d.name, code: d.code })),
          forecastTypes: [
            'historical_average',
            'trend_analysis',
            'seasonal_pattern',
            'machine_learning',
            'manual_override'
          ],
          instructions: [
            '1. Fill all required columns (marked with *)',
            '2. Date format must be YYYY-MM-DD',
            '3. Department ID and Designation ID must exist in system',
            '4. Required Count must be positive integer',
            '5. Priority range: 1-10 (higher = more important)',
            '6. Confidence range: 0-100 (percentage)',
            '7. Valid From/To dates define forecast validity period'
          ]
        }
      };

    } catch (error) {
      throw new ValidationError(`Failed to generate Excel template: ${error.message}`);
    }
  }

  /**
   * Process uploaded Excel data and insert into database
   * @param {Array} excelData - Parsed Excel data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Processing results
   */
  async processExcelUpload(excelData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      const results = {
        total: excelData.length,
        successful: 0,
        failed: 0,
        errors: [],
        created: []
      };

      // Validate and process each row
      for (let i = 0; i < excelData.length; i++) {
        const row = excelData[i];
        const rowNumber = i + 2; // Excel row number (accounting for header)

        try {
          // Validate required fields
          const validationResult = this.validateExcelRow(row, rowNumber);
          if (!validationResult.isValid) {
            results.errors.push(...validationResult.errors);
            results.failed++;
            continue;
          }

          // Check if department and designation exist
          const department = await Department.findOne({
            where: {
              id: row['Department ID'],
              companyId: tenantContext.companyId,
              businessUnitId: tenantContext.businessUnitId
            }
          });

          if (!department) {
            results.errors.push(`Row ${rowNumber}: Department ID ${row['Department ID']} not found`);
            results.failed++;
            continue;
          }

          const designation = await Designation.findOne({
            where: {
              id: row['Designation ID'],
              companyId: tenantContext.companyId,
              businessUnitId: tenantContext.businessUnitId
            }
          });

          if (!designation) {
            results.errors.push(`Row ${rowNumber}: Designation ID ${row['Designation ID']} not found`);
            results.failed++;
            continue;
          }

          // Check for duplicate forecast
          const existingForecast = await DemandForecast.findOne({
            where: {
              companyId: tenantContext.companyId,
              businessUnitId: tenantContext.businessUnitId,
              departmentId: row['Department ID'],
              designationId: row['Designation ID'],
              date: row['Date (YYYY-MM-DD)']
            },
            transaction
          });

          if (existingForecast) {
            results.errors.push(`Row ${rowNumber}: Forecast already exists for this date, department, and designation`);
            results.failed++;
            continue;
          }

          // Create forecast record
          const forecastData = {
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId,
            departmentId: row['Department ID'],
            designationId: row['Designation ID'],
            date: row['Date (YYYY-MM-DD)'],
            requiredCount: parseInt(row['Required Count']),
            priority: parseInt(row['Priority (1-10)']) || 5,
            confidence: parseFloat(row['Confidence (0-100)']) || 100,
            forecastType: row['Forecast Type'] || 'manual_override',
            validFrom: row['Valid From (YYYY-MM-DD)'] || row['Date (YYYY-MM-DD)'],
            validTo: row['Valid To (YYYY-MM-DD)'] || null,
            notes: row['Notes'] || null,
            status: 'active',
            metadata: {
              source: 'excel_upload',
              uploadedAt: new Date(),
              rowNumber: rowNumber
            },
            createdById: tenantContext.userId,
            updatedById: tenantContext.userId
          };

          const createdForecast = await DemandForecast.create(forecastData, { transaction });

          results.created.push({
            id: createdForecast.id,
            date: createdForecast.date,
            department: department.name,
            designation: designation.name,
            requiredCount: createdForecast.requiredCount
          });

          results.successful++;

        } catch (rowError) {
          results.errors.push(`Row ${rowNumber}: ${rowError.message}`);
          results.failed++;
        }
      }

      await transaction.commit();

      return results;

    } catch (error) {
      await transaction.rollback();
      throw new ValidationError(`Excel processing failed: ${error.message}`);
    }
  }

  /**
   * Validate Excel row data
   * @param {Object} row - Excel row data
   * @param {number} rowNumber - Row number for error reporting
   * @returns {Object} Validation result
   */
  validateExcelRow(row, rowNumber) {
    const errors = [];

    // Required fields validation
    const requiredFields = [
      'Date (YYYY-MM-DD)',
      'Department ID',
      'Designation ID',
      'Required Count'
    ];

    requiredFields.forEach(field => {
      if (!row[field] || row[field] === '') {
        errors.push(`Row ${rowNumber}: ${field} is required`);
      }
    });

    // Date format validation
    if (row['Date (YYYY-MM-DD)']) {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(row['Date (YYYY-MM-DD)'])) {
        errors.push(`Row ${rowNumber}: Date must be in YYYY-MM-DD format`);
      } else {
        const date = new Date(row['Date (YYYY-MM-DD)']);
        if (isNaN(date.getTime())) {
          errors.push(`Row ${rowNumber}: Invalid date`);
        }
      }
    }

    // Numeric validations
    if (row['Department ID'] && !Number.isInteger(Number(row['Department ID']))) {
      errors.push(`Row ${rowNumber}: Department ID must be a valid integer`);
    }

    if (row['Designation ID'] && !Number.isInteger(Number(row['Designation ID']))) {
      errors.push(`Row ${rowNumber}: Designation ID must be a valid integer`);
    }

    if (row['Required Count']) {
      const count = parseInt(row['Required Count']);
      if (isNaN(count) || count < 0) {
        errors.push(`Row ${rowNumber}: Required Count must be a positive integer`);
      }
    }

    if (row['Priority (1-10)']) {
      const priority = parseInt(row['Priority (1-10)']);
      if (isNaN(priority) || priority < 1 || priority > 10) {
        errors.push(`Row ${rowNumber}: Priority must be between 1 and 10`);
      }
    }

    if (row['Confidence (0-100)']) {
      const confidence = parseFloat(row['Confidence (0-100)']);
      if (isNaN(confidence) || confidence < 0 || confidence > 100) {
        errors.push(`Row ${rowNumber}: Confidence must be between 0 and 100`);
      }
    }

    // Forecast type validation
    if (row['Forecast Type']) {
      const validTypes = ['historical_average', 'trend_analysis', 'seasonal_pattern', 'machine_learning', 'manual_override'];
      if (!validTypes.includes(row['Forecast Type'])) {
        errors.push(`Row ${rowNumber}: Invalid forecast type. Must be one of: ${validTypes.join(', ')}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // ==================== ANALYTICS APIs FOR GRAPH VISUALIZATION ====================

  /**
   * Get department-wise forecast analytics for visualization
   * @param {Object} filters - Filter parameters
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Department-wise analytics data
   */
  async getDepartmentWiseAnalytics(filters, tenantContext) {
    const { dateFrom, dateTo, departmentIds } = filters;

    // Build where conditions
    const whereConditions = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      status: 'active'
    };

    // ✅ FIX: Date filtering - only apply if filters are provided
    if (dateFrom || dateTo) {
      whereConditions[Op.or] = [];

      if (dateFrom && dateTo) {
        whereConditions[Op.or].push({
          [Op.and]: [
            { startDate: { [Op.lte]: dateTo } },
            { endDate: { [Op.gte]: dateFrom } }
          ]
        });
      } else if (dateFrom) {
        whereConditions[Op.or].push({ endDate: { [Op.gte]: dateFrom } });
      } else if (dateTo) {
        whereConditions[Op.or].push({ startDate: { [Op.lte]: dateTo } });
      }
    }

    // ✅ FIX: Department filtering - only apply if departmentIds are provided
    if (departmentIds && departmentIds.length > 0) {
      whereConditions.departmentId = { [Op.in]: departmentIds };
    }

    // Fetch forecasts with related data
    const forecasts = await DemandForecast.findAll({
      where: whereConditions,
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        },
        {
          model: DemandForecastInstance,
          as: 'instances',
          include: [
            {
              model: DemandForecastDesignationRequirement,
              as: 'designationRequirements',
              include: [
                {
                  model: Designation,
                  as: 'designation',
                  attributes: ['id', 'name']
                }
              ]
            },
            {
              model: RotaShift,
              as: 'rotaShift',
              attributes: ['id', 'name', 'startTime', 'endTime']
            }
          ]
        }
      ],
      order: [['startDate', 'ASC']]
    });

    // Group and aggregate data by department
    const departmentMap = new Map();
    let totalRequiredEmployees = 0;
    let dateRange = { earliest: null, latest: null };

    forecasts.forEach(forecast => {
      const deptId = forecast.departmentId;
      const deptName = forecast.department?.name || `Department ${deptId}`;

      if (!departmentMap.has(deptId)) {
        departmentMap.set(deptId, {
          departmentId: deptId,
          departmentName: deptName,
          forecasts: [],
          totalRequiredCount: 0,
          totalInstances: 0,
          dateRanges: []
        });
      }

      const deptData = departmentMap.get(deptId);

      // Calculate forecast total count
      let forecastTotalCount = 0;
      if (forecast.instances) {
        forecast.instances.forEach(instance => {
          if (instance.designationRequirements) {
            instance.designationRequirements.forEach(req => {
              forecastTotalCount += req.requiredCount;
            });
          }
        });
      }

      // Add forecast info with total count
      deptData.forecasts.push({
        id: forecast.id,
        name: forecast.name,
        startDate: forecast.startDate,
        endDate: forecast.endDate,
        status: forecast.status,
        confidence: forecast.confidence,
        forecastType: forecast.forecastType,
        totalCount: forecastTotalCount // ✅ Added total count per forecast
      });

      // Add date range
      deptData.dateRanges.push({
        startDate: forecast.startDate,
        endDate: forecast.endDate
      });

      // Calculate totals from instances
      if (forecast.instances) {
        forecast.instances.forEach(instance => {
          deptData.totalInstances++;

          if (instance.designationRequirements) {
            instance.designationRequirements.forEach(req => {
              deptData.totalRequiredCount += req.requiredCount;
              totalRequiredEmployees += req.requiredCount;
            });
          }
        });
      }

      // Update global date range
      if (!dateRange.earliest || forecast.startDate < dateRange.earliest) {
        dateRange.earliest = forecast.startDate;
      }
      if (!dateRange.latest || forecast.endDate > dateRange.latest) {
        dateRange.latest = forecast.endDate;
      }
    });

    // Convert to array and sort by total required count
    const departmentAnalytics = Array.from(departmentMap.values())
      .sort((a, b) => b.totalRequiredCount - a.totalRequiredCount);

    return {
      summary: {
        totalDepartments: departmentAnalytics.length,
        totalForecasts: forecasts.length,
        totalRequiredEmployees,
        dateRange
      },
      departments: departmentAnalytics
    };
  }

  /**
   * Get shift-wise forecast analytics for visualization
   * @param {Object} filters - Filter parameters
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Shift-wise analytics data
   */
  async getShiftWiseAnalytics(filters, tenantContext) {
    const { dateFrom, dateTo, shiftIds, forecastIds } = filters;

    // Build where conditions for forecasts
    const forecastWhere = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      status: 'active'
    };

    // ✅ FIX: Only apply forecast filter if provided
    if (forecastIds && forecastIds.length > 0) {
      forecastWhere.id = { [Op.in]: forecastIds };
    }

    // ✅ FIX: Build where conditions for instances - only apply if filters provided
    const instanceWhere = {};

    // Date filtering - only apply if dates are provided
    if (dateFrom && dateTo) {
      instanceWhere.date = { [Op.between]: [dateFrom, dateTo] };
    } else if (dateFrom) {
      instanceWhere.date = { [Op.gte]: dateFrom };
    } else if (dateTo) {
      instanceWhere.date = { [Op.lte]: dateTo };
    }

    // Shift filtering - only apply if shiftIds are provided
    if (shiftIds && shiftIds.length > 0) {
      instanceWhere.rotaShiftId = { [Op.in]: shiftIds };
    }

    // ✅ FIX: Use same structure as department-wise API (which works)
    const forecasts = await DemandForecast.findAll({
      where: forecastWhere,
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        },
        {
          model: DemandForecastInstance,
          as: 'instances',
          ...(Object.keys(instanceWhere).length > 0 && { where: instanceWhere }),
          include: [
            {
              model: RotaShift,
              as: 'rotaShift',
              attributes: ['id', 'name', 'startTime', 'endTime', 'breakDuration']
            },
            {
              model: DemandForecastDesignationRequirement,
              as: 'designationRequirements',
              include: [
                {
                  model: Designation,
                  as: 'designation',
                  attributes: ['id', 'name']
                }
              ]
            }
          ]
        }
      ],
      order: [['startDate', 'ASC']]
    });


    // ✅ Process forecasts directly (same as department-wise API)

    // ✅ Group data by forecast -> date -> shift (same pattern as department API)
    const forecastMap = new Map();
    let totalRequiredEmployees = 0;

    forecasts.forEach(forecast => {
      if (forecast.instances && forecast.instances.length > 0) {
        forecast.instances.forEach(instance => {
          const forecastId = forecast.id;
          const date = instance.date;
          const shiftId = instance.rotaShiftId;
          const shiftName = instance.rotaShift?.name || `Shift ${shiftId}`;

          // Initialize forecast structure
          if (!forecastMap.has(forecastId)) {
            forecastMap.set(forecastId, {
              forecastDetails: {
                id: forecast.id,
                name: forecast.name,
                startDate: forecast.startDate,
                endDate: forecast.endDate,
                status: forecast.status,
                confidence: forecast.confidence,
                forecastType: forecast.forecastType,
                department: forecast.department
              },
              dateShiftData: new Map()
            });
          }

          const forecastData = forecastMap.get(forecastId);

          // Initialize date structure
          if (!forecastData.dateShiftData.has(date)) {
            forecastData.dateShiftData.set(date, new Map());
          }

          const dateData = forecastData.dateShiftData.get(date);

          // Initialize shift structure
          if (!dateData.has(shiftId)) {
            dateData.set(shiftId, {
              shiftId,
              shiftName,
              shiftDetails: {
                startTime: instance.rotaShift?.startTime,
                endTime: instance.rotaShift?.endTime,
                breakDuration: instance.rotaShift?.breakDuration
              },
              requiredEmployeeCount: 0,
              designations: []
            });
          }

          const shiftData = dateData.get(shiftId);

          // Add designation requirements
          if (instance.designationRequirements) {
            instance.designationRequirements.forEach(req => {
              shiftData.requiredEmployeeCount += req.requiredCount;
              totalRequiredEmployees += req.requiredCount;

              shiftData.designations.push({
                designationId: req.designationId,
                designationName: req.designation?.name || `Designation ${req.designationId}`,
                requiredCount: req.requiredCount,
                confidence: req.confidence
              });
            });
          }
        });
      }
    });

    // Convert nested Maps to arrays for JSON serialization
    const forecastAnalytics = Array.from(forecastMap.entries()).map(([forecastId, forecastData]) => {
      const dateShiftArray = Array.from(forecastData.dateShiftData.entries()).map(([date, dateData]) => {
        const shiftsArray = Array.from(dateData.values());
        return {
          date,
          shifts: shiftsArray
        };
      });

      return {
        forecastDetails: forecastData.forecastDetails,
        dateShiftData: dateShiftArray
      };
    });

    // ✅ Calculate summary from forecasts (same as department API)
    const totalInstances = forecasts.reduce((sum, forecast) => sum + (forecast.instances?.length || 0), 0);
    const allDates = [];
    forecasts.forEach(forecast => {
      if (forecast.instances) {
        forecast.instances.forEach(instance => {
          allDates.push(instance.date);
        });
      }
    });

    return {
      summary: {
        totalForecasts: forecastAnalytics.length,
        totalInstances,
        totalRequiredEmployees,
        dateRange: {
          earliest: allDates.length > 0 ? Math.min(...allDates.map(d => new Date(d))) : null,
          latest: allDates.length > 0 ? Math.max(...allDates.map(d => new Date(d))) : null
        }
      },
      forecasts: forecastAnalytics
    };
  }

  /**
   * Get designation and date-wise forecast analytics for visualization
   * @param {Object} filters - Filter parameters
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Designation and date-wise analytics data
   */
  async getDesignationDateWiseAnalytics(filters, tenantContext) {
    const { dateFrom, dateTo, designationIds, forecastIds } = filters;

    // Build where conditions for forecasts
    const forecastWhere = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      status: 'active'
    };

    // ✅ FIX: Only apply forecast filter if provided
    if (forecastIds && forecastIds.length > 0) {
      forecastWhere.id = { [Op.in]: forecastIds };
    }

    // ✅ FIX: Build where conditions for instances - only apply if filters provided
    const instanceWhere = {};

    // Date filtering - only apply if dates are provided
    if (dateFrom && dateTo) {
      instanceWhere.date = { [Op.between]: [dateFrom, dateTo] };
    } else if (dateFrom) {
      instanceWhere.date = { [Op.gte]: dateFrom };
    } else if (dateTo) {
      instanceWhere.date = { [Op.lte]: dateTo };
    }

    // ✅ FIX: Build where conditions for designation requirements - only apply if provided
    const designationWhere = {};
    if (designationIds && designationIds.length > 0) {
      designationWhere.designationId = { [Op.in]: designationIds };
    }

    // ✅ FIX: Use same structure as department-wise API (which works)
    const forecasts = await DemandForecast.findAll({
      where: forecastWhere,
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        },
        {
          model: DemandForecastInstance,
          as: 'instances',
          ...(Object.keys(instanceWhere).length > 0 && { where: instanceWhere }),
          include: [
            {
              model: RotaShift,
              as: 'rotaShift',
              attributes: ['id', 'name', 'startTime', 'endTime']
            },
            {
              model: DemandForecastDesignationRequirement,
              as: 'designationRequirements',
              ...(Object.keys(designationWhere).length > 0 && { where: designationWhere }),
              include: [
                {
                  model: Designation,
                  as: 'designation',
                  attributes: ['id', 'name']
                }
              ]
            }
          ]
        }
      ],
      order: [['startDate', 'ASC']]
    });


    // ✅ Process forecasts directly (same as department and shift APIs)

    // ✅ Group data by forecast -> date -> designation (same pattern as other APIs)
    const forecastMap = new Map();
    let totalRequiredEmployees = 0;

    forecasts.forEach(forecast => {
      if (forecast.instances && forecast.instances.length > 0) {
        forecast.instances.forEach(instance => {
          if (instance.designationRequirements && instance.designationRequirements.length > 0) {
            instance.designationRequirements.forEach(req => {
              const forecastId = forecast.id;
              const date = instance.date;
              const designationId = req.designationId;
              const designationName = req.designation?.name || `Designation ${designationId}`;

              // Initialize forecast structure
              if (!forecastMap.has(forecastId)) {
                forecastMap.set(forecastId, {
                  forecastDetails: {
                    id: forecast.id,
                    name: forecast.name,
                    startDate: forecast.startDate,
                    endDate: forecast.endDate,
                    status: forecast.status,
                    confidence: forecast.confidence,
                    forecastType: forecast.forecastType,
                    department: forecast.department
                  },
                  dateDesignationData: new Map()
                });
              }

              const forecastData = forecastMap.get(forecastId);

              // Initialize date structure
              if (!forecastData.dateDesignationData.has(date)) {
                forecastData.dateDesignationData.set(date, new Map());
              }

              const dateData = forecastData.dateDesignationData.get(date);

              // Initialize designation structure
              if (!dateData.has(designationId)) {
                dateData.set(designationId, {
                  designationId,
                  designationName,
                  requiredCount: 0,
                  instances: [],
                  totalConfidence: 0,
                  averageConfidence: 0,
                  forecastMethods: new Set()
                });
              }

              const designationData = dateData.get(designationId);

              // Add requirement data
              designationData.requiredCount += req.requiredCount;
              totalRequiredEmployees += req.requiredCount;

              designationData.instances.push({
                instanceId: instance.id,
                requiredCount: req.requiredCount,
                confidence: req.confidence,
                forecastMethod: req.forecastMethod,
                isOverridden: req.isOverridden,
                overrideReason: req.overrideReason,
                shift: {
                  id: instance.rotaShift?.id,
                  name: instance.rotaShift?.name,
                  startTime: instance.rotaShift?.startTime,
                  endTime: instance.rotaShift?.endTime
                }
              });

              // Update confidence and methods
              if (req.confidence) {
                designationData.totalConfidence += parseFloat(req.confidence);
              }
              if (req.forecastMethod) {
                designationData.forecastMethods.add(req.forecastMethod);
              }
            });
          }
        });
      }
    });

    // Calculate averages and convert Sets to Arrays
    forecastMap.forEach(forecastData => {
      forecastData.dateDesignationData.forEach(dateData => {
        dateData.forEach(designationData => {
          if (designationData.instances.length > 0) {
            designationData.averageConfidence = designationData.totalConfidence / designationData.instances.length;
          }
          designationData.forecastMethods = Array.from(designationData.forecastMethods);
          delete designationData.totalConfidence; // Remove intermediate calculation
        });
      });
    });

    // Convert nested Maps to arrays for JSON serialization
    const forecastAnalytics = Array.from(forecastMap.entries()).map(([, forecastData]) => {
      const dateDesignationArray = Array.from(forecastData.dateDesignationData.entries()).map(([date, dateData]) => {
        const designationsArray = Array.from(dateData.values());
        return {
          date,
          designations: designationsArray
        };
      });

      return {
        forecastDetails: forecastData.forecastDetails,
        dateDesignationData: dateDesignationArray
      };
    });

    // ✅ Calculate summary from forecasts (same as other APIs)
    const uniqueDesignations = new Set();
    const allDates = [];
    let totalRequirements = 0;

    forecasts.forEach(forecast => {
      if (forecast.instances) {
        forecast.instances.forEach(instance => {
          allDates.push(instance.date);
          if (instance.designationRequirements) {
            instance.designationRequirements.forEach(req => {
              uniqueDesignations.add(req.designationId);
              totalRequirements++;
            });
          }
        });
      }
    });

    return {
      summary: {
        totalForecasts: forecastAnalytics.length,
        totalDesignations: uniqueDesignations.size,
        totalRequirements,
        totalRequiredEmployees,
        dateRange: {
          earliest: allDates.length > 0 ? Math.min(...allDates.map(d => new Date(d))) : null,
          latest: allDates.length > 0 ? Math.max(...allDates.map(d => new Date(d))) : null
        }
      },
      forecasts: forecastAnalytics
    };
  }
}

module.exports = new DemandForecastService();
