'use strict';

const { RotaShift, RotaShiftInstance, ShiftAssignment, RotaSchedule, Employee, User, Department, Designation } = require('../../data/models');
const { ValidationError, NotFoundError } = require('../../common/errors');
const { Op } = require('sequelize');
const moment = require('moment');

/**
 * Frontend-Agnostic Instance Management Service
 * 
 * This service handles all the complexity of the 3-layer architecture internally:
 * - RotaShift (Templates)
 * - RotaShiftInstance (Date-specific instances)
 * - ShiftAssignment (Employee tracking)
 * 
 * Frontend only deals with simple concepts: Schedules, Shifts (templates), Employees, Dates
 * Backend automatically manages instances, status synchronization, and updates
 */
class RotaInstanceManager {

  /**
   * Auto-create instances for all shifts × all dates when schedule is published
   * Called internally when schedule status changes to 'published'
   * Frontend never calls this directly
   */
  async createInstancesForSchedule(scheduleId, tenantContext, transaction = null) {
    try {
      console.log(`🔄 Creating instances for schedule ${scheduleId}...`);

      const schedule = await RotaSchedule.findByPk(scheduleId, {
        include: [
          {
            model: RotaShift,
            as: 'shifts',
            where: { isActive: true }
          }
        ],
        transaction
      });

      if (!schedule) {
        throw new NotFoundError(`Schedule ${scheduleId} not found`);
      }

      const instances = [];
      const startDate = moment(schedule.startDate);
      const endDate = moment(schedule.endDate);

      // Create instances for each shift × each date
      for (const shift of schedule.shifts) {
        let currentDate = startDate.clone();
        
        while (currentDate.isSameOrBefore(endDate)) {
          // Check if instance already exists
          const existingInstance = await RotaShiftInstance.findOne({
            where: {
              rotaShiftId: shift.id,
              date: currentDate.format('YYYY-MM-DD')
            },
            transaction
          });

          if (!existingInstance) {
            const instance = await RotaShiftInstance.create({
              rotaShiftId: shift.id,
              scheduleId: scheduleId,
              date: currentDate.format('YYYY-MM-DD'),
              actualRequiredCount: shift.baseRequiredCount, // Default to template count
              createdById: tenantContext.userId
            }, { transaction });

            instances.push(instance);
            console.log(`✅ Created instance for ${shift.name} on ${currentDate.format('YYYY-MM-DD')}`);
          }

          currentDate.add(1, 'day');
        }
      }

      console.log(`✅ Created ${instances.length} instances for schedule ${scheduleId}`);
      return instances;
    } catch (error) {
      console.error(`❌ Error creating instances for schedule ${scheduleId}:`, error);
      throw error;
    }
  }

  /**
   * Assign employee to shift on specific date
   * Frontend calls this with simple parameters: shiftId (template), employeeId, date
   * Backend internally finds instance and creates assignment
   */
  async assignEmployeeToShift(scheduleId, shiftId, employeeId, date, tenantContext, transaction = null) {
    try {
      console.log(`🔄 Assigning employee ${employeeId} to shift ${shiftId} on ${date}...`);

      // 1. Get schedule and validate status-based restrictions
      const schedule = await RotaSchedule.findByPk(scheduleId, { transaction });
      if (!schedule) {
        throw new NotFoundError(`Schedule ${scheduleId} not found`);
      }

      await this.validateScheduleStatusForAssignment(schedule, date);

      // 2. Find or create the shift instance
      let instance = await RotaShiftInstance.findOne({
        where: {
          rotaShiftId: shiftId,
          scheduleId: scheduleId,
          date: date
        },
        include: [
          {
            model: RotaShift,
            as: 'rotaShift'
          }
        ],
        transaction
      });

      if (!instance) {
        // Auto-create instance if it doesn't exist
        const shift = await RotaShift.findByPk(shiftId, { transaction });
        if (!shift) {
          throw new NotFoundError(`Shift template ${shiftId} not found`);
        }

        instance = await RotaShiftInstance.create({
          rotaShiftId: shiftId,
          scheduleId: scheduleId,
          date: date,
          actualRequiredCount: shift.baseRequiredCount,
          createdById: tenantContext.userId
        }, { transaction });

        console.log(`✅ Auto-created instance for shift ${shiftId} on ${date}`);
      }

      // 3. Validate date-based restrictions
      await this.validateDateBasedRestrictions(date, tenantContext);

      // 4. Validate employee eligibility
      await this.validateEmployeeEligibility(employeeId, instance, tenantContext, transaction);

      // 5. Check if employee is already assigned
      const existingAssignment = await ShiftAssignment.findOne({
        where: {
          shiftInstanceId: instance.id,
          employeeId: employeeId
        },
        transaction
      });

      if (existingAssignment) {
        throw new ValidationError(`Employee ${employeeId} is already assigned to this shift`);
      }

      // 6. Check capacity
      const currentAssignments = await ShiftAssignment.count({
        where: {
          shiftInstanceId: instance.id,
          status: { [Op.notIn]: ['cancelled'] }
        },
        transaction
      });

      if (currentAssignments >= instance.actualRequiredCount) {
        throw new ValidationError(`Shift is already fully staffed (${currentAssignments}/${instance.actualRequiredCount})`);
      }

      // 7. Perform comprehensive business rules validation
      const BusinessRulesValidationService = require('./businessRulesValidationService');
      const businessRulesService = new BusinessRulesValidationService();

      const proposedAssignments = await ShiftAssignment.findAll({
        where: { shiftInstanceId: instance.id, status: { [Op.notIn]: ['cancelled'] } },
        transaction
      });

      // Add the new assignment to the validation
      proposedAssignments.push({
        shiftInstanceId: instance.id,
        employeeId: employeeId,
        status: 'assigned'
      });

      const validationResult = await businessRulesService.performComprehensiveValidation({
        shiftInstance: instance,
        proposedAssignments: proposedAssignments
      }, tenantContext);

      // Log validation results
      if (!validationResult.valid) {
        console.warn(`⚠️ Business rule violations detected for assignment:`, validationResult.summary);

        // Block assignment if there are critical violations
        if (validationResult.violations.critical.length > 0) {
          const criticalMessages = validationResult.violations.critical.map(v => v.message).join('; ');
          throw new ValidationError(`Assignment blocked due to critical business rule violations: ${criticalMessages}`);
        }

        // Log warnings for non-critical violations
        if (validationResult.violations.warning.length > 0) {
          const warningMessages = validationResult.violations.warning.map(v => v.message).join('; ');
          console.warn(`⚠️ Assignment proceeding with warnings: ${warningMessages}`);
        }
      }

      // 8. Create assignment
      const assignment = await ShiftAssignment.create({
        shiftInstanceId: instance.id,
        employeeId: employeeId,
        assignedBy: tenantContext.userId,
        status: 'assigned',
        createdById: tenantContext.userId,
        validationWarnings: validationResult.violations.warning.length > 0 ?
          JSON.stringify(validationResult.violations.warning) : null
      }, { transaction });

      console.log(`✅ Assigned employee ${employeeId} to shift instance ${instance.id}`);
      return assignment;
    } catch (error) {
      console.error(`❌ Error assigning employee to shift:`, error);
      throw error;
    }
  }

  /**
   * Remove employee assignment from shift
   * Frontend calls this with simple parameters
   * Backend handles instance and assignment cleanup
   */
  async unassignEmployeeFromShift(scheduleId, shiftId, employeeId, date, tenantContext, transaction = null) {
    try {
      console.log(`🔄 Unassigning employee ${employeeId} from shift ${shiftId} on ${date}...`);

      // 1. Get schedule and validate status-based restrictions for unassignment
      const schedule = await RotaSchedule.findByPk(scheduleId, { transaction });
      if (!schedule) {
        throw new NotFoundError(`Schedule ${scheduleId} not found`);
      }

      await this.validateScheduleStatusForUnassignment(schedule, date);

      // 2. Find the shift instance
      const instance = await RotaShiftInstance.findOne({
        where: {
          rotaShiftId: shiftId,
          scheduleId: scheduleId,
          date: date
        },
        transaction
      });

      if (!instance) {
        throw new NotFoundError(`Shift instance not found for shift ${shiftId} on ${date}`);
      }

      // 3. Validate date-based restrictions for unassignment
      await this.validateDateBasedRestrictions(date, tenantContext);

      // 4. Find and validate assignment
      const assignment = await ShiftAssignment.findOne({
        where: {
          shiftInstanceId: instance.id,
          employeeId: employeeId
        },
        transaction
      });

      if (!assignment) {
        throw new NotFoundError(`Employee ${employeeId} is not assigned to this shift`);
      }

      // 5. Check if assignment can be cancelled based on status
      if (['completed', 'checked_in'].includes(assignment.status)) {
        throw new ValidationError(`Cannot unassign employee who has ${assignment.status} status`);
      }

      // 6. Update assignment status to cancelled instead of deleting
      await assignment.update({
        status: 'cancelled',
        updatedById: tenantContext.userId
      }, { transaction });

      console.log(`✅ Unassigned employee ${employeeId} from shift instance ${instance.id}`);
      return assignment;
    } catch (error) {
      console.error(`❌ Error unassigning employee from shift:`, error);
      throw error;
    }
  }

  /**
   * Get calendar view for frontend
   * Complex instance queries, return simple view
   * Frontend gets clean, familiar data structure
   */
  async getCalendarView(startDate, endDate, tenantContext, options = {}) {
    try {
      console.log(`🔄 Getting calendar view from ${startDate} to ${endDate}...`);

      const instances = await RotaShiftInstance.findAll({
        where: {
          date: {
            [Op.between]: [startDate, endDate]
          }
        },
        include: [
          {
            model: RotaShift,
            as: 'rotaShift',
            where: {
              companyId: tenantContext.companyId,
              businessUnitId: tenantContext.businessUnitId,
              isActive: true
            },
            include: [
              {
                model: Department,
                as: 'department'
              }
            ]
          },
          {
            model: RotaSchedule,
            as: 'schedule'
          },
          {
            model: ShiftAssignment,
            as: 'assignments',
            where: {
              status: { [Op.notIn]: ['cancelled'] }
            },
            required: false,
            include: [
              {
                model: Employee,
                as: 'employee',
                include: [
                  {
                    model: User,
                    as: 'user',
                    attributes: ['id', 'email']
                  }
                ]
              }
            ]
          }
        ],
        order: [
          ['date', 'ASC'],
          ['rotaShift', 'startTime', 'ASC']
        ]
      });

      // Transform to frontend-friendly format
      const calendarData = instances.map(instance => ({
        // Frontend familiar fields
        id: instance.rotaShift.id,           // Template ID (frontend familiar)
        instanceId: instance.id,             // Internal instance ID
        name: instance.rotaShift.name,
        date: instance.date,
        startTime: instance.rotaShift.startTime,
        endTime: instance.rotaShift.endTime,
        breakDuration: instance.rotaShift.breakDuration,
        
        // Requirements and staffing
        baseRequiredCount: instance.rotaShift.baseRequiredCount,    // From template
        actualRequiredCount: instance.actualRequiredCount,         // Final count used
        assignedCount: instance.assignments.length,
        
        // Status information
        scheduleStatus: instance.schedule.status,
        isFullyStaffed: instance.assignments.length >= instance.actualRequiredCount,
        isOverStaffed: instance.assignments.length > instance.actualRequiredCount,
        
        // Department and designation info
        department: instance.rotaShift.department,
        designation: instance.rotaShift.designation,
        
        // Employee assignments
        assignments: instance.assignments.map(assignment => ({
          assignmentId: assignment.id,
          employeeId: assignment.employeeId,
          employeeName: `${assignment.employee.firstName} ${assignment.employee.lastName}`,
          employeeEmail: assignment.employee.contactEmail,
          status: assignment.status,
          assignedAt: assignment.assignedAt
        })),
        
        // Instance-specific data
        notes: instance.notes,
        overrideReason: instance.actualRequiredCount !== instance.rotaShift.baseRequiredCount 
          ? 'forecast_prediction' : null
      }));

      console.log(`✅ Retrieved ${calendarData.length} shift instances for calendar view`);
      return calendarData;
    } catch (error) {
      console.error(`❌ Error getting calendar view:`, error);
      throw error;
    }
  }

  /**
   * Update instances based on attendance automatically
   * Background process, frontend unaware
   */
  async updateFromAttendance(attendanceData, tenantContext, transaction = null) {
    try {
      console.log(`🔄 Updating instances from attendance data...`);

      const { employeeId, date, checkInTime, checkOutTime, status } = attendanceData;

      // Find all shift instances for this employee on this date
      const assignments = await ShiftAssignment.findAll({
        where: {
          employeeId: employeeId
        },
        include: [
          {
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: {
              date: date
            }
          }
        ],
        transaction
      });

      for (const assignment of assignments) {
        let newStatus = assignment.status;

        // Update status based on attendance
        if (checkInTime && assignment.status === 'assigned') {
          newStatus = 'checked_in';
        }
        
        if (checkOutTime && assignment.status === 'checked_in') {
          newStatus = 'completed';
        }
        
        if (status === 'absent') {
          newStatus = 'absent';
        }

        // Update assignment with actual times
        await assignment.update({
          status: newStatus,
          actualStartTime: checkInTime,
          actualEndTime: checkOutTime,
          updatedById: tenantContext.userId
        }, { transaction });

        console.log(`✅ Updated assignment ${assignment.id} status to ${newStatus}`);
      }

      return assignments;
    } catch (error) {
      console.error(`❌ Error updating instances from attendance:`, error);
      throw error;
    }
  }

  /**
   * Handle schedule status changes
   * Automatically update all related instances
   */
  async handleScheduleStatusChange(scheduleId, newStatus, tenantContext, transaction = null) {
    try {
      console.log(`🔄 Handling schedule ${scheduleId} status change to ${newStatus}...`);

      if (newStatus === 'published') {
        // Auto-create instances when schedule is published
        await this.createInstancesForSchedule(scheduleId, tenantContext, transaction);
      }

      // Additional status-based logic can be added here
      // e.g., archiving instances when schedule is archived

      console.log(`✅ Handled schedule ${scheduleId} status change to ${newStatus}`);
    } catch (error) {
      console.error(`❌ Error handling schedule status change:`, error);
      throw error;
    }
  }

  /**
   * Clean up instances when schedule dates change
   * Add/remove instances as needed
   */
  async handleScheduleDateChange(scheduleId, oldStartDate, oldEndDate, newStartDate, newEndDate, tenantContext, transaction = null) {
    try {
      console.log(`🔄 Handling schedule ${scheduleId} date change...`);

      // Remove instances outside new date range
      await RotaShiftInstance.destroy({
        where: {
          scheduleId: scheduleId,
          [Op.or]: [
            { date: { [Op.lt]: newStartDate } },
            { date: { [Op.gt]: newEndDate } }
          ]
        },
        transaction
      });

      // Create instances for new dates if schedule is published
      const schedule = await RotaSchedule.findByPk(scheduleId, { transaction });
      if (schedule && schedule.status === 'published') {
        await this.createInstancesForSchedule(scheduleId, tenantContext, transaction);
      }

      console.log(`✅ Handled schedule ${scheduleId} date change`);
    } catch (error) {
      console.error(`❌ Error handling schedule date change:`, error);
      throw error;
    }
  }

  /**
   * Validate schedule status for assignment operations
   * @param {Object} schedule - Schedule object
   * @param {string} date - Assignment date
   */
  async validateScheduleStatusForAssignment(schedule, date) {
    const moment = require('moment');
    const assignmentDate = moment(date);
    const today = moment();

    // Check schedule status restrictions
    switch (schedule.status) {
      case 'draft':
        // Allow assignments to draft schedules
        break;

      case 'published':
        // Published schedules have restrictions
        if (assignmentDate.isSameOrBefore(today, 'day')) {
          throw new ValidationError('Cannot assign employees to published shifts on current or past dates');
        }

        // Check if assignment requires manager approval (future enhancement)
        console.log(`⚠️ Assignment to published schedule requires manager approval`);
        break;

      case 'archived':
        throw new ValidationError('Cannot assign employees to archived schedules');

      case 'cancelled':
        throw new ValidationError('Cannot assign employees to cancelled schedules');

      default:
        throw new ValidationError(`Invalid schedule status: ${schedule.status}`);
    }
  }

  /**
   * Validate date-based restrictions for assignments
   * @param {string} date - Assignment date
   * @param {Object} tenantContext - Tenant context
   */
  async validateDateBasedRestrictions(date, tenantContext) {
    const moment = require('moment');
    const assignmentDate = moment(date);
    const today = moment();

    // 1. No assignments to past dates (except same day with grace period)
    if (assignmentDate.isBefore(today, 'day')) {
      throw new ValidationError('Cannot assign employees to past dates');
    }

    // 2. Check minimum advance notice (from company settings)
    const CompanyModuleSetting = require('../../data/models').CompanyModuleSetting;
    const rotaSettings = await CompanyModuleSetting.findOne({
      where: {
        companyId: tenantContext.companyId,
        moduleName: 'rota'
      }
    });

    if (rotaSettings?.settings?.operational?.minAdvanceSchedulingDays) {
      const minAdvanceDays = rotaSettings.settings.operational.minAdvanceSchedulingDays;
      const minDate = today.clone().add(minAdvanceDays, 'days');

      if (assignmentDate.isBefore(minDate, 'day')) {
        throw new ValidationError(`Assignments require at least ${minAdvanceDays} days advance notice`);
      }
    }

    // 3. Check maximum advance scheduling limit
    if (rotaSettings?.settings?.operational?.maxAdvanceSchedulingDays) {
      const maxAdvanceDays = rotaSettings.settings.operational.maxAdvanceSchedulingDays;
      const maxDate = today.clone().add(maxAdvanceDays, 'days');

      if (assignmentDate.isAfter(maxDate, 'day')) {
        throw new ValidationError(`Cannot schedule more than ${maxAdvanceDays} days in advance`);
      }
    }
  }

  /**
   * Validate employee eligibility for shift assignment
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Shift instance
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async validateEmployeeEligibility(employeeId, instance, tenantContext, transaction) {
    const Employee = require('../../data/models').Employee;
    const EmployeeAvailability = require('../../data/models').EmployeeAvailability;

    // 1. Check if employee exists and is active
    const employee = await Employee.findOne({
      where: {
        id: employeeId,
        companyId: tenantContext.companyId,
        status: 'active'
      },
      transaction
    });

    if (!employee) {
      throw new NotFoundError(`Active employee ${employeeId} not found`);
    }

    // 2. Check designation match
    if (employee.designationId !== instance.rotaShift.designationId) {
      throw new ValidationError(
        `Employee designation (${employee.designationId}) does not match shift requirement (${instance.rotaShift.designationId})`
      );
    }

    // 3. Check department match (if specified)
    if (instance.rotaShift.departmentId && employee.departmentId !== instance.rotaShift.departmentId) {
      throw new ValidationError(
        `Employee department (${employee.departmentId}) does not match shift requirement (${instance.rotaShift.departmentId})`
      );
    }

    // 4. Check employee availability (if availability service exists)
    try {
      const EmployeeAvailabilityService = require('./employeeAvailabilityService');
      const availabilityService = new EmployeeAvailabilityService();

      const shiftDetails = {
        date: instance.date,
        startTime: instance.rotaShift.startTime,
        endTime: instance.rotaShift.endTime,
        departmentId: instance.rotaShift.departmentId
      };

      const availabilityCheck = await availabilityService.checkEmployeeAvailabilityForShift(
        employeeId,
        shiftDetails,
        tenantContext
      );

      if (!availabilityCheck.available) {
        const violations = availabilityCheck.violations?.high || availabilityCheck.violations || [];
        const violationMessages = violations.map(v => v.message || v.type).join(', ');
        throw new ValidationError(`Employee not available: ${violationMessages}`);
      }
    } catch (error) {
      // If availability service doesn't exist or fails, log warning but don't block
      if (error.message.includes('not available')) {
        throw error; // Re-throw availability violations
      }
      console.warn(`⚠️ Could not check employee availability: ${error.message}`);
    }

    // 5. Check for shift conflicts (same date/time overlaps)
    await this.checkShiftConflicts(employeeId, instance, transaction);
  }

  /**
   * Check for shift conflicts for employee
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Shift instance to check
   * @param {Object} transaction - Database transaction
   */
  async checkShiftConflicts(employeeId, instance, transaction) {
    const moment = require('moment');

    // Get all assignments for employee on the same date
    const existingAssignments = await ShiftAssignment.findAll({
      where: {
        employeeId: employeeId,
        status: { [Op.notIn]: ['cancelled', 'absent'] }
      },
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: {
            date: instance.date
          },
          include: [
            {
              model: RotaShift,
              as: 'rotaShift'
            }
          ]
        }
      ],
      transaction
    });

    // Check for time overlaps
    const newShiftStart = moment(`${instance.date} ${instance.rotaShift.startTime}`);
    const newShiftEnd = moment(`${instance.date} ${instance.rotaShift.endTime}`);

    for (const assignment of existingAssignments) {
      const existingShift = assignment.shiftInstance.rotaShift;
      const existingStart = moment(`${instance.date} ${existingShift.startTime}`);
      const existingEnd = moment(`${instance.date} ${existingShift.endTime}`);

      // Check for overlap
      if (newShiftStart.isBefore(existingEnd) && newShiftEnd.isAfter(existingStart)) {
        throw new ValidationError(
          `Employee has conflicting shift: ${existingShift.name} (${existingShift.startTime}-${existingShift.endTime})`
        );
      }
    }
  }

  /**
   * Validate schedule status for unassignment operations
   * @param {Object} schedule - Schedule object
   * @param {string} date - Assignment date
   */
  async validateScheduleStatusForUnassignment(schedule, date) {
    const moment = require('moment');
    const assignmentDate = moment(date);
    const today = moment();

    // Check schedule status restrictions for unassignment
    switch (schedule.status) {
      case 'draft':
        // Allow unassignments from draft schedules
        break;

      case 'published':
        // Published schedules have stricter restrictions for unassignment
        if (assignmentDate.isSameOrBefore(today, 'day')) {
          throw new ValidationError('Cannot unassign employees from published shifts on current or past dates');
        }

        // Check if unassignment requires manager approval (future enhancement)
        console.log(`⚠️ Unassignment from published schedule requires manager approval`);
        break;

      case 'archived':
        throw new ValidationError('Cannot modify assignments in archived schedules');

      case 'cancelled':
        throw new ValidationError('Cannot modify assignments in cancelled schedules');

      default:
        throw new ValidationError(`Invalid schedule status: ${schedule.status}`);
    }
  }
}

module.exports = RotaInstanceManager;
