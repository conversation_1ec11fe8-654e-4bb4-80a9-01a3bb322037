'use strict';

const { Op } = require('sequelize');
const { RotaShift, RotaShiftInstance, ShiftAssignment, Employee, RotaSchedule } = require('../../data/models');
const moment = require('moment');

/**
 * Business Rules Validation Service
 * 
 * Implements comprehensive business rule validations for rota management:
 * - Staffing validations
 * - Timing validations  
 * - Approval workflows for published shift changes
 */
class BusinessRulesValidationService {
  constructor() {
    this.validationRules = {
      staffing: [
        'minimum_staffing_requirements',
        'maximum_staffing_limits',
        'skill_requirements',
        'seniority_requirements'
      ],
      timing: [
        'shift_overlap_prevention',
        'minimum_gap_between_shifts',
        'maximum_consecutive_hours',
        'weekly_hour_limits'
      ],
      approval: [
        'published_schedule_changes',
        'manager_approval_required',
        'employee_consent_required'
      ]
    };
  }

  /**
   * Validate staffing requirements for shift
   * @param {Object} shiftInstance - Shift instance
   * @param {Array} proposedAssignments - Proposed employee assignments
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async validateStaffingRequirements(shiftInstance, proposedAssignments, tenantContext) {
    try {
      const violations = [];
      
      // 1. Check minimum staffing
      const minStaffingResult = await this.checkMinimumStaffing(shiftInstance, proposedAssignments);
      if (!minStaffingResult.valid) {
        violations.push(...minStaffingResult.violations);
      }

      // 2. Check maximum staffing limits
      const maxStaffingResult = await this.checkMaximumStaffing(shiftInstance, proposedAssignments);
      if (!maxStaffingResult.valid) {
        violations.push(...maxStaffingResult.violations);
      }

      // 3. Check skill requirements
      const skillsResult = await this.checkSkillRequirements(shiftInstance, proposedAssignments, tenantContext);
      if (!skillsResult.valid) {
        violations.push(...skillsResult.violations);
      }

      // 4. Check seniority requirements
      const seniorityResult = await this.checkSeniorityRequirements(shiftInstance, proposedAssignments, tenantContext);
      if (!seniorityResult.valid) {
        violations.push(...seniorityResult.violations);
      }

      return {
        valid: violations.length === 0,
        violations,
        summary: {
          totalViolations: violations.length,
          criticalViolations: violations.filter(v => v.severity === 'critical').length,
          warningViolations: violations.filter(v => v.severity === 'warning').length
        }
      };

    } catch (error) {
      console.error('Error validating staffing requirements:', error);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Check minimum staffing requirements
   * @param {Object} shiftInstance - Shift instance
   * @param {Array} proposedAssignments - Proposed assignments
   * @returns {Object} Validation result
   */
  async checkMinimumStaffing(shiftInstance, proposedAssignments) {
    const violations = [];
    const requiredCount = shiftInstance.actualRequiredCount || shiftInstance.rotaShift?.baseRequiredCount || 1;
    const assignedCount = proposedAssignments.filter(a => a.status !== 'cancelled').length;

    if (assignedCount < requiredCount) {
      violations.push({
        type: 'MINIMUM_STAFFING_VIOLATION',
        severity: 'critical',
        message: `Insufficient staffing: ${assignedCount}/${requiredCount} required`,
        details: {
          required: requiredCount,
          assigned: assignedCount,
          shortage: requiredCount - assignedCount
        }
      });
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * Check maximum staffing limits
   * @param {Object} shiftInstance - Shift instance
   * @param {Array} proposedAssignments - Proposed assignments
   * @returns {Object} Validation result
   */
  async checkMaximumStaffing(shiftInstance, proposedAssignments) {
    const violations = [];
    const maxCount = shiftInstance.maxStaffing || (shiftInstance.actualRequiredCount * 1.5); // 150% of required
    const assignedCount = proposedAssignments.filter(a => a.status !== 'cancelled').length;

    if (assignedCount > maxCount) {
      violations.push({
        type: 'MAXIMUM_STAFFING_VIOLATION',
        severity: 'warning',
        message: `Overstaffing detected: ${assignedCount}/${Math.floor(maxCount)} maximum`,
        details: {
          maximum: Math.floor(maxCount),
          assigned: assignedCount,
          excess: assignedCount - Math.floor(maxCount)
        }
      });
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * Check skill requirements
   * @param {Object} shiftInstance - Shift instance
   * @param {Array} proposedAssignments - Proposed assignments
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async checkSkillRequirements(shiftInstance, proposedAssignments, tenantContext) {
    const violations = [];
    
    try {
      // Get shift skill requirements
      const shiftSkills = shiftInstance.rotaShift?.requiredSkills || [];
      
      if (shiftSkills.length === 0) {
        return { valid: true, violations: [] };
      }

      // Check each assignment for required skills
      for (const assignment of proposedAssignments) {
        if (assignment.status === 'cancelled') continue;

        const employee = await Employee.findByPk(assignment.employeeId, {
          include: [
            {
              model: require('../../data/models').EmployeeSkill,
              as: 'skills',
              include: [
                {
                  model: require('../../data/models').Skill,
                  as: 'skill'
                }
              ]
            }
          ]
        });

        if (!employee) continue;

        const employeeSkills = employee.skills?.map(es => es.skill.name) || [];
        const missingSkills = shiftSkills.filter(skill => !employeeSkills.includes(skill));

        if (missingSkills.length > 0) {
          violations.push({
            type: 'SKILL_REQUIREMENT_VIOLATION',
            severity: 'warning',
            message: `Employee ${employee.firstName} ${employee.lastName} missing required skills: ${missingSkills.join(', ')}`,
            details: {
              employeeId: employee.id,
              employeeName: `${employee.firstName} ${employee.lastName}`,
              requiredSkills: shiftSkills,
              employeeSkills,
              missingSkills
            }
          });
        }
      }

    } catch (error) {
      console.error('Error checking skill requirements:', error);
      violations.push({
        type: 'SKILL_CHECK_ERROR',
        severity: 'warning',
        message: `Unable to verify skill requirements: ${error.message}`
      });
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * Check seniority requirements
   * @param {Object} shiftInstance - Shift instance
   * @param {Array} proposedAssignments - Proposed assignments
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async checkSeniorityRequirements(shiftInstance, proposedAssignments, tenantContext) {
    const violations = [];
    
    try {
      const minSeniorityMonths = shiftInstance.rotaShift?.minSeniorityMonths || 0;
      
      if (minSeniorityMonths === 0) {
        return { valid: true, violations: [] };
      }

      for (const assignment of proposedAssignments) {
        if (assignment.status === 'cancelled') continue;

        const employee = await Employee.findByPk(assignment.employeeId);
        if (!employee) continue;

        const seniorityMonths = moment().diff(moment(employee.joiningDate), 'months');
        
        if (seniorityMonths < minSeniorityMonths) {
          violations.push({
            type: 'SENIORITY_REQUIREMENT_VIOLATION',
            severity: 'warning',
            message: `Employee ${employee.firstName} ${employee.lastName} has insufficient seniority: ${seniorityMonths}/${minSeniorityMonths} months`,
            details: {
              employeeId: employee.id,
              employeeName: `${employee.firstName} ${employee.lastName}`,
              requiredMonths: minSeniorityMonths,
              actualMonths: seniorityMonths,
              joiningDate: employee.joiningDate
            }
          });
        }
      }

    } catch (error) {
      console.error('Error checking seniority requirements:', error);
      violations.push({
        type: 'SENIORITY_CHECK_ERROR',
        severity: 'warning',
        message: `Unable to verify seniority requirements: ${error.message}`
      });
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * Validate timing constraints
   * @param {Object} shiftInstance - Shift instance
   * @param {Array} proposedAssignments - Proposed assignments
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async validateTimingConstraints(shiftInstance, proposedAssignments, tenantContext) {
    try {
      const violations = [];

      for (const assignment of proposedAssignments) {
        if (assignment.status === 'cancelled') continue;

        // 1. Check shift overlaps
        const overlapResult = await this.checkShiftOverlaps(assignment.employeeId, shiftInstance, tenantContext);
        if (!overlapResult.valid) {
          violations.push(...overlapResult.violations);
        }

        // 2. Check minimum gap between shifts
        const gapResult = await this.checkMinimumGapBetweenShifts(assignment.employeeId, shiftInstance, tenantContext);
        if (!gapResult.valid) {
          violations.push(...gapResult.violations);
        }

        // 3. Check maximum consecutive hours
        const consecutiveResult = await this.checkMaximumConsecutiveHours(assignment.employeeId, shiftInstance, tenantContext);
        if (!consecutiveResult.valid) {
          violations.push(...consecutiveResult.violations);
        }

        // 4. Check weekly hour limits
        const weeklyResult = await this.checkWeeklyHourLimits(assignment.employeeId, shiftInstance, tenantContext);
        if (!weeklyResult.valid) {
          violations.push(...weeklyResult.violations);
        }
      }

      return {
        valid: violations.length === 0,
        violations,
        summary: {
          totalViolations: violations.length,
          criticalViolations: violations.filter(v => v.severity === 'critical').length,
          warningViolations: violations.filter(v => v.severity === 'warning').length
        }
      };

    } catch (error) {
      console.error('Error validating timing constraints:', error);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Check for shift overlaps
   * @param {number} employeeId - Employee ID
   * @param {Object} shiftInstance - Shift instance
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async checkShiftOverlaps(employeeId, shiftInstance, tenantContext) {
    const violations = [];

    try {
      const ConflictDetectionService = require('./conflictDetectionService');
      const conflictService = new ConflictDetectionService();

      const conflicts = await conflictService.checkShiftConflicts(employeeId, shiftInstance, null);

      if (conflicts.length > 0) {
        violations.push({
          type: 'SHIFT_OVERLAP_VIOLATION',
          severity: 'critical',
          message: `Shift overlap detected for employee ${employeeId}`,
          details: {
            employeeId,
            conflicts: conflicts.map(c => ({
              conflictingShiftId: c.conflictingShiftId,
              conflictingDate: c.conflictingDate,
              conflictingTime: c.conflictingTime
            }))
          }
        });
      }

    } catch (error) {
      console.error('Error checking shift overlaps:', error);
      violations.push({
        type: 'OVERLAP_CHECK_ERROR',
        severity: 'warning',
        message: `Unable to verify shift overlaps: ${error.message}`
      });
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * Check minimum gap between shifts
   * @param {number} employeeId - Employee ID
   * @param {Object} shiftInstance - Shift instance
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async checkMinimumGapBetweenShifts(employeeId, shiftInstance, tenantContext) {
    const violations = [];

    try {
      const ConflictDetectionService = require('./conflictDetectionService');
      const conflictService = new ConflictDetectionService();

      const shiftDetails = {
        date: shiftInstance.date,
        startTime: shiftInstance.rotaShift.startTime,
        endTime: shiftInstance.rotaShift.endTime
      };

      const restPeriodResult = await conflictService.checkRestPeriodViolation(
        employeeId,
        shiftDetails,
        tenantContext
      );

      if (!restPeriodResult.valid && restPeriodResult.violation) {
        violations.push({
          type: 'MINIMUM_GAP_VIOLATION',
          severity: 'critical',
          message: restPeriodResult.message,
          details: restPeriodResult.details
        });
      }

    } catch (error) {
      console.error('Error checking minimum gap between shifts:', error);
      violations.push({
        type: 'GAP_CHECK_ERROR',
        severity: 'warning',
        message: `Unable to verify minimum gap: ${error.message}`
      });
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * Check maximum consecutive hours
   * @param {number} employeeId - Employee ID
   * @param {Object} shiftInstance - Shift instance
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async checkMaximumConsecutiveHours(employeeId, shiftInstance, tenantContext) {
    const violations = [];

    try {
      const ConflictDetectionService = require('./conflictDetectionService');
      const conflictService = new ConflictDetectionService();

      const consecutiveDaysResult = await conflictService.checkConsecutiveDaysLimit(
        employeeId,
        shiftInstance.date,
        tenantContext
      );

      if (!consecutiveDaysResult.valid && consecutiveDaysResult.violation) {
        violations.push({
          type: 'CONSECUTIVE_HOURS_VIOLATION',
          severity: 'warning',
          message: consecutiveDaysResult.message,
          details: consecutiveDaysResult.details
        });
      }

    } catch (error) {
      console.error('Error checking maximum consecutive hours:', error);
      violations.push({
        type: 'CONSECUTIVE_CHECK_ERROR',
        severity: 'warning',
        message: `Unable to verify consecutive hours: ${error.message}`
      });
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * Check weekly hour limits
   * @param {number} employeeId - Employee ID
   * @param {Object} shiftInstance - Shift instance
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async checkWeeklyHourLimits(employeeId, shiftInstance, tenantContext) {
    const violations = [];

    try {
      const ConflictDetectionService = require('./conflictDetectionService');
      const conflictService = new ConflictDetectionService();

      const shiftDetails = {
        date: shiftInstance.date,
        startTime: shiftInstance.rotaShift.startTime,
        endTime: shiftInstance.rotaShift.endTime
      };

      const weeklyHoursResult = await conflictService.checkWeeklyHoursLimit(
        employeeId,
        shiftDetails,
        tenantContext
      );

      if (!weeklyHoursResult.valid && weeklyHoursResult.violation) {
        violations.push({
          type: 'WEEKLY_HOURS_VIOLATION',
          severity: 'warning',
          message: weeklyHoursResult.message,
          details: weeklyHoursResult.details
        });
      }

    } catch (error) {
      console.error('Error checking weekly hour limits:', error);
      violations.push({
        type: 'WEEKLY_HOURS_CHECK_ERROR',
        severity: 'warning',
        message: `Unable to verify weekly hours: ${error.message}`
      });
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * Validate approval workflows for published schedule changes
   * @param {Object} changeRequest - Change request details
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async validateApprovalWorkflows(changeRequest, tenantContext) {
    try {
      const violations = [];

      // 1. Check if change requires manager approval
      const managerApprovalResult = await this.checkManagerApprovalRequired(changeRequest, tenantContext);
      if (!managerApprovalResult.valid) {
        violations.push(...managerApprovalResult.violations);
      }

      // 2. Check if employee consent is required
      const employeeConsentResult = await this.checkEmployeeConsentRequired(changeRequest, tenantContext);
      if (!employeeConsentResult.valid) {
        violations.push(...employeeConsentResult.violations);
      }

      // 3. Check approval deadlines
      const deadlineResult = await this.checkApprovalDeadlines(changeRequest, tenantContext);
      if (!deadlineResult.valid) {
        violations.push(...deadlineResult.violations);
      }

      return {
        valid: violations.length === 0,
        violations,
        approvalRequired: violations.some(v => v.type.includes('APPROVAL_REQUIRED')),
        summary: {
          totalViolations: violations.length,
          approvalViolations: violations.filter(v => v.type.includes('APPROVAL')).length,
          consentViolations: violations.filter(v => v.type.includes('CONSENT')).length
        }
      };

    } catch (error) {
      console.error('Error validating approval workflows:', error);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Check if manager approval is required
   * @param {Object} changeRequest - Change request
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async checkManagerApprovalRequired(changeRequest, tenantContext) {
    const violations = [];

    try {
      const { changeType, scheduleStatus, timeUntilShift } = changeRequest;

      // Manager approval required for:
      // 1. Changes to published schedules
      // 2. Changes within 24 hours of shift
      // 3. Overtime assignments
      // 4. Emergency staffing changes

      if (scheduleStatus === 'published') {
        violations.push({
          type: 'MANAGER_APPROVAL_REQUIRED',
          severity: 'critical',
          message: 'Manager approval required for changes to published schedules',
          details: {
            reason: 'published_schedule_change',
            changeType
          }
        });
      }

      if (timeUntilShift && timeUntilShift < 24) {
        violations.push({
          type: 'MANAGER_APPROVAL_REQUIRED',
          severity: 'critical',
          message: 'Manager approval required for changes within 24 hours of shift',
          details: {
            reason: 'short_notice_change',
            hoursUntilShift: timeUntilShift
          }
        });
      }

      if (changeType === 'overtime_assignment') {
        violations.push({
          type: 'MANAGER_APPROVAL_REQUIRED',
          severity: 'critical',
          message: 'Manager approval required for overtime assignments',
          details: {
            reason: 'overtime_assignment'
          }
        });
      }

    } catch (error) {
      console.error('Error checking manager approval requirements:', error);
      violations.push({
        type: 'APPROVAL_CHECK_ERROR',
        severity: 'warning',
        message: `Unable to verify approval requirements: ${error.message}`
      });
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * Check if employee consent is required
   * @param {Object} changeRequest - Change request
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async checkEmployeeConsentRequired(changeRequest, tenantContext) {
    const violations = [];

    try {
      const { changeType, affectedEmployees, isVoluntary } = changeRequest;

      // Employee consent required for:
      // 1. Non-voluntary overtime
      // 2. Shift swaps
      // 3. Schedule changes affecting personal time
      // 4. Weekend/holiday work (if not pre-consented)

      if (changeType === 'overtime_assignment' && !isVoluntary) {
        violations.push({
          type: 'EMPLOYEE_CONSENT_REQUIRED',
          severity: 'critical',
          message: 'Employee consent required for non-voluntary overtime',
          details: {
            reason: 'non_voluntary_overtime',
            affectedEmployees
          }
        });
      }

      if (changeType === 'shift_swap') {
        violations.push({
          type: 'EMPLOYEE_CONSENT_REQUIRED',
          severity: 'critical',
          message: 'Employee consent required for shift swaps',
          details: {
            reason: 'shift_swap',
            affectedEmployees
          }
        });
      }

    } catch (error) {
      console.error('Error checking employee consent requirements:', error);
      violations.push({
        type: 'CONSENT_CHECK_ERROR',
        severity: 'warning',
        message: `Unable to verify consent requirements: ${error.message}`
      });
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * Check approval deadlines
   * @param {Object} changeRequest - Change request
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Validation result
   */
  async checkApprovalDeadlines(changeRequest, tenantContext) {
    const violations = [];

    try {
      const { requestDate, shiftDate, approvalDeadline } = changeRequest;
      const now = moment();
      const shiftMoment = moment(shiftDate);
      const deadlineMoment = moment(approvalDeadline);

      // Check if approval deadline has passed
      if (deadlineMoment.isBefore(now)) {
        violations.push({
          type: 'APPROVAL_DEADLINE_PASSED',
          severity: 'critical',
          message: 'Approval deadline has passed',
          details: {
            deadline: approvalDeadline,
            currentTime: now.toISOString(),
            hoursOverdue: now.diff(deadlineMoment, 'hours', true)
          }
        });
      }

      // Check if there's sufficient time for approval process
      const hoursUntilShift = shiftMoment.diff(now, 'hours', true);
      if (hoursUntilShift < 12) {
        violations.push({
          type: 'INSUFFICIENT_APPROVAL_TIME',
          severity: 'warning',
          message: 'Insufficient time for proper approval process',
          details: {
            hoursUntilShift,
            minimumRequired: 12
          }
        });
      }

    } catch (error) {
      console.error('Error checking approval deadlines:', error);
      violations.push({
        type: 'DEADLINE_CHECK_ERROR',
        severity: 'warning',
        message: `Unable to verify approval deadlines: ${error.message}`
      });
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * Comprehensive business rules validation
   * @param {Object} validationRequest - Validation request
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Comprehensive validation result
   */
  async performComprehensiveValidation(validationRequest, tenantContext) {
    try {
      const { shiftInstance, proposedAssignments, changeRequest } = validationRequest;

      const results = await Promise.all([
        this.validateStaffingRequirements(shiftInstance, proposedAssignments, tenantContext),
        this.validateTimingConstraints(shiftInstance, proposedAssignments, tenantContext),
        changeRequest ? this.validateApprovalWorkflows(changeRequest, tenantContext) : { valid: true, violations: [] }
      ]);

      const [staffingResult, timingResult, approvalResult] = results;

      const allViolations = [
        ...staffingResult.violations,
        ...timingResult.violations,
        ...approvalResult.violations
      ];

      const criticalViolations = allViolations.filter(v => v.severity === 'critical');
      const warningViolations = allViolations.filter(v => v.severity === 'warning');

      return {
        valid: allViolations.length === 0,
        canProceedWithWarnings: criticalViolations.length === 0,
        violations: {
          critical: criticalViolations,
          warning: warningViolations,
          all: allViolations
        },
        validationResults: {
          staffing: staffingResult,
          timing: timingResult,
          approval: approvalResult
        },
        summary: {
          totalViolations: allViolations.length,
          criticalViolations: criticalViolations.length,
          warningViolations: warningViolations.length,
          approvalRequired: approvalResult.approvalRequired || false
        }
      };

    } catch (error) {
      console.error('Error in comprehensive business rules validation:', error);
      return {
        valid: false,
        error: error.message
      };
    }
  }
}

module.exports = BusinessRulesValidationService;
