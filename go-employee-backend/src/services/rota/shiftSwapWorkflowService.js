'use strict';

/**
 * Shift Swap Workflow Service - PRD Implementation
 * 
 * Integrates shift swap requests with the existing workflow engine:
 * - Automatic workflow creation for swap requests
 * - Approval routing based on company hierarchy
 * - Status synchronization between swap and workflow
 * - Notification integration
 */

const {
  ShiftSwapRequest,
  ShiftAssignment,
  WorkflowInstance,
  WorkflowDefinition,
  WorkflowStep,
  WorkflowAssignment,
  WorkflowAction,
  Employee,
  Department,
  Role ,
   RotaShiftInstance,
  sequelize,Designation,RotaShift,
} = require('../../data/models');
const { NotFoundError, ValidationError } = require('../../common/errors');
const ApprovalWorkflowEngine = require('../workflow/approvalWorkflowEngine');
const { Op } = require('sequelize');

class ShiftSwapWorkflowService {
  constructor() {
    this.workflowEngine = new ApprovalWorkflowEngine();
    this.entityType = 'shift_swap';
  }

  /**
   * Create workflow for shift swap request
   * @param {Object} swapRequest - Shift swap request
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Workflow instance
   */
  async createWorkflowForSwapRequest(swapRequest, tenantContext) {
    try {
      console.log(`🚀 Starting workflow for shift swap request ID: ${swapRequest.id}`);
      console.log(`🔍 Swap request data:`, {
        id: swapRequest.id,
        requesterId: swapRequest.requesterId,
        swapDate: swapRequest.swapDate,
        reasonForSwap: swapRequest.reasonForSwap
      });

      // Prepare entity data for workflow engine (using correct field names)
      const entityData = {
        employeeId: swapRequest.requesterId,
        requesterId: swapRequest.requesterId,
        swapDate: swapRequest.swapDate,
        reasonForSwap: swapRequest.reasonForSwap,
        reasonDescription: swapRequest.reasonDescription,
        urgency: swapRequest.urgency || 'medium',
        currentShiftAssignmentId: swapRequest.currentShiftAssignmentId,
        desiredShiftId: swapRequest.desiredShiftId,
        targetEmployeeId: swapRequest.targetEmployeeId,
        swapWithAnyAvailableEmployee: swapRequest.swapWithAnyAvailableEmployee,
        swapWithAnyAvailableShift: swapRequest.swapWithAnyAvailableShift,
        businessUnitId: tenantContext.businessUnitId || swapRequest.businessUnitId,
        requesterName: swapRequest.requester?.user ?
          `${swapRequest.requester.user.firstName} ${swapRequest.requester.user.lastName}` : 'Unknown',
        requesterEmail: swapRequest.requester?.user?.email || swapRequest.requester?.contactEmail || ''
      };

      console.log(`🔍 Entity data for workflow:`, entityData);

      // Start workflow using the same workflow engine as leave system
      const workflowInstance = await this.workflowEngine.startWorkflow(
        this.entityType,
        swapRequest.id,
        entityData,
        tenantContext
      );

      console.log(`✅ Workflow started successfully for shift swap request ID: ${swapRequest.id}`);
      return workflowInstance;

    } catch (error) {
      console.error(`❌ Failed to start workflow for shift swap request:`, error);
      throw error;
    }
  }

  /**
   * Process shift swap approval through workflow (same pattern as leave workflow)
   * @param {number} swapRequestId - Swap request ID
   * @param {Object} approvalData - Approval data
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Updated workflow instance
   */
  async processSwapApproval(swapRequestId, approvalData, tenantContext) {
    try {
      console.log(`🔄 Processing shift swap approval for swap ID: ${swapRequestId}`);

      // Find the workflow instance for this swap request
      const workflowInstance = await this.findWorkflowInstanceBySwapRequest(swapRequestId, tenantContext);

      if (!workflowInstance) {
        throw new NotFoundError('Workflow instance not found for this shift swap request');
      }

      // Process the approval action through workflow engine - FLEXIBLE APPROVAL SYSTEM
      const action = {
        type: 'approve', // Use 'type' for flexible approval system
        comments: approvalData.comments || 'Approved',
        data: approvalData
      };

      const result = await this.workflowEngine.processApprovalAction(
        workflowInstance.id,
        action,
        tenantContext
      );

      console.log(`📋 Shift swap workflow action result:`, {
        success: result.success,
        workflowStatus: result.workflowStatus,
        isFinalDecision: result.isFinalDecision
      });

      // Update swap request status based on workflow result
      await this.updateSwapRequestFromWorkflow(swapRequestId, result, tenantContext);

      return result;

    } catch (error) {
      console.error(`❌ Failed to process shift swap approval:`, error);
      throw error;
    }
  }

  /**
   * Process shift swap rejection through workflow (same pattern as leave workflow)
   * @param {number} swapRequestId - Swap request ID
   * @param {Object} rejectionData - Rejection data
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Updated workflow instance
   */
  async processSwapRejection(swapRequestId, rejectionData, tenantContext) {
    try {
      console.log(`🔄 Processing shift swap rejection for swap ID: ${swapRequestId}`);

      // Find the workflow instance for this swap request
      const workflowInstance = await this.findWorkflowInstanceBySwapRequest(swapRequestId, tenantContext);

      if (!workflowInstance) {
        throw new NotFoundError('Workflow instance not found for this shift swap request');
      }

      // Process the rejection action through workflow engine - FLEXIBLE APPROVAL SYSTEM
      const action = {
        type: 'reject', // Use 'type' for flexible approval system
        comments: rejectionData.rejectionReason || 'Rejected',
        data: rejectionData
      };

      const result = await this.workflowEngine.processApprovalAction(
        workflowInstance.id,
        action,
        tenantContext
      );

      console.log(`📋 Shift swap workflow rejection result:`, {
        success: result.success,
        workflowStatus: result.workflowStatus,
        isFinalDecision: result.isFinalDecision
      });

      // Update swap request status based on workflow result
      await this.updateSwapRequestFromWorkflow(swapRequestId, result, tenantContext);

      return result;

    } catch (error) {
      console.error(`❌ Failed to process shift swap rejection:`, error);
      throw error;
    }
  }

  /**
   * Get swap request with workflow details
   * @param {number} swapRequestId - Swap request ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Swap request with workflow
   */
  async getSwapRequestWithWorkflow(swapRequestId, tenantContext) {
    const swapRequest = await ShiftSwapRequest.findOne({
      where: { id: swapRequestId },
      include: [
        {
          model: Employee,
          as: 'requester',
          attributes: ['id', 'firstName', 'lastName', 'email', 'employeeId']
        },
        {
          model: Employee,
          as: 'target',
          attributes: ['id', 'firstName', 'lastName', 'email', 'employeeId'],
          required: false
        },
        {
          model: WorkflowInstance,
          as: 'workflowInstance',
          include: [
            {
              model: WorkflowAssignment,
              as: 'assignments',
              include: [
                {
                  model: Employee,
                  as: 'assignee',
                  attributes: ['id', 'firstName', 'lastName', 'email']
                }
              ]
            },
            {
              model: WorkflowAction,
              as: 'actions',
              include: [
                {
                  model: Employee,
                  as: 'actor',
                  attributes: ['id', 'firstName', 'lastName', 'email']
                }
              ],
              order: [['createdAt', 'DESC']]
            }
          ]
        }
      ]
    });

    if (!swapRequest) {
      throw new NotFoundError('Shift swap request not found');
    }

    return swapRequest;
  }

  /**
   * Find workflow instance by swap request (same pattern as leave workflow)
   * @param {number} swapRequestId - Swap request ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Workflow instance
   */
  async findWorkflowInstanceBySwapRequest(swapRequestId, tenantContext) {
    const { WorkflowInstance } = require('../../data/models');

    return await WorkflowInstance.findOne({
      where: {
        entityType: this.entityType,
        entityId: swapRequestId,
        companyId: tenantContext.companyId
      }
    });
  }

  /**
   * Update swap request from workflow result (same pattern as leave workflow)
   * @param {number} swapRequestId - Swap request ID
   * @param {Object} workflowResult - Workflow result
   * @param {Object} tenantContext - Tenant context
   */
  async updateSwapRequestFromWorkflow(swapRequestId, workflowResult, tenantContext) {
    
    const transaction = await sequelize.transaction();

    try {
      // Determine final status based on workflow result
      let finalStatus = 'pending';

      if (workflowResult.isFinalDecision) {
        if (workflowResult.workflowStatus === 'approved') {
          finalStatus = 'approved';
        } else if (workflowResult.workflowStatus === 'rejected') {
          finalStatus = 'rejected';
        }
      }

      console.log(`🔄 Updating swap request ${swapRequestId} with workflow status: ${finalStatus}`);

      // If final status is approved, execute the swap
      if (finalStatus === 'approved') {
        console.log(`✅ Final approval received for swap request ${swapRequestId}, executing swap...`);

        // Get swap request details for execution
        const swapRequest = await ShiftSwapRequest.findOne({
          where: {
            id: swapRequestId,
            companyId: tenantContext.companyId
          },
          transaction
        });

        if (!swapRequest) {
          throw new Error(`Swap request ${swapRequestId} not found`);
        }

        // CRITICAL: Check availability at approval time (not creation time)
        console.log(`🔍 Checking availability before executing approved request ID: ${swapRequestId}`);

        const availabilityCheck = await this.checkSwapAvailabilityAtApproval(swapRequest, tenantContext, transaction);

        if (!availabilityCheck.canExecute) {
          // Availability failed - reject the request with detailed reason
          console.log(`❌ Swap request ${swapRequestId} cannot be executed: ${availabilityCheck.reason}`);

          await ShiftSwapRequest.update({
            status: 'rejected',
            responseDate: new Date(),
            respondedBy: tenantContext.userId,
            managerNotes: `Rejected during approval: ${availabilityCheck.reason}`,
            rejectionReason: 'availability_failed'
          }, {
            where: {
              id: swapRequestId,
              companyId: tenantContext.companyId
            },
            transaction
          });

          console.log(`✅ Swap request ${swapRequestId} rejected due to availability constraints`);
          return; // Exit early - don't execute
        }

        console.log(`✅ Availability check passed - executing swap for request ID: ${swapRequestId}`);

        // Execute the swap based on request configuration
        const shiftSwapService = require('./shiftSwapService');
        const executionResult = await this.executeSwapBasedOnRequest(swapRequest, tenantContext, transaction);

        // Update swap request to completed status with execution details
        await ShiftSwapRequest.update({
          status: 'completed', // Final status after execution
          responseDate: new Date(),
          respondedBy: tenantContext.userId,
          managerNotes: workflowResult.comments || workflowResult.data?.comments,
          executedAt: new Date(),
          executedBy: tenantContext.userId,
          executionNotes: `Auto-executed upon approval: ${executionResult.message}`
        }, {
          where: {
            id: swapRequestId,
            companyId: tenantContext.companyId
          },
          transaction
        });

        console.log(`✅ Swap request ${swapRequestId} approved and executed successfully`);

      } else {
        // For rejected or pending status, just update normally
        await ShiftSwapRequest.update({
          status: finalStatus,
          responseDate: new Date(),
          respondedBy: tenantContext.userId,
          managerNotes: workflowResult.comments || workflowResult.data?.comments
        }, {
          where: {
            id: swapRequestId,
            companyId: tenantContext.companyId
          },
          transaction
        });

        // Release reserved slot if request is rejected
        if (finalStatus === 'rejected') {
          const swapRequest = await ShiftSwapRequest.findByPk(swapRequestId, { transaction });
          if (swapRequest && swapRequest.reservedSlotInfo) {
            console.log('🔓 Releasing reserved slot for rejected swap request:', swapRequestId);

            await swapRequest.update({
              reservedSlotInfo: null
            }, { transaction });

            console.log('✅ Reserved slot released for rejected request');
          }
        }

        console.log(`✅ Updated shift swap request ${swapRequestId} with status: ${finalStatus}`);
      }

      await transaction.commit();

    } catch (error) {
      await transaction.rollback();
      console.error(`❌ Failed to update swap request from workflow:`, error);
      throw error;
    }
  }

  /**
   * Check swap availability at approval time (CRITICAL CHECK)
   * This is where we prevent multiple requests from being approved for same slot
   * @param {Object} swapRequest - Swap request object
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Availability check result
   */
  async checkSwapAvailabilityAtApproval(swapRequest, tenantContext, transaction) {
    console.log('🔍 CRITICAL: Checking swap availability at approval time for request:', swapRequest.id);

    // Get requester's designation
    const requester = await Employee.findByPk(swapRequest.requesterId, {
      include: [{ model:Designation, as: 'designation' }],
      transaction
    });

    if (!requester || !requester.designationId) {
      return {
        canExecute: false,
        reason: 'Requester designation not found',
        details: { requesterId: swapRequest.requesterId }
      };
    }

    // Check availability based on swap request type
    if (swapRequest.targetEmployeeId) {
      // CASE 1: Target Employee - Always allow (direct swap)
      return await this.checkTargetEmployeeAvailability(swapRequest, requester, tenantContext, transaction);
    }
    else if (swapRequest.desiredShiftId) {
      // CASE 2: Target Shift - Check designation-based availability
      return await this.checkTargetShiftAvailability(swapRequest, requester, tenantContext, transaction);
    }
    else if (swapRequest.swapWithAnyAvailableShift) {
      // CASE 3: Any Available Shift - Check if any shift has availability
      return await this.checkAnyAvailableShiftAvailability(swapRequest, requester, tenantContext, transaction);
    }
    else {
      return {
        canExecute: false,
        reason: 'Invalid swap request configuration - no valid swap option specified',
        details: { swapRequestId: swapRequest.id }
      };
    }
  }

  /**
   * Check target employee availability (Case 1)
   */
  async checkTargetEmployeeAvailability(swapRequest, requester, tenantContext, transaction) {

    console.log('🔍 Checking target employee availability...');

    // Find target employee's assignment on swap date
    const targetAssignment = await ShiftAssignment.findOne({
      where: {
        employeeId: swapRequest.targetEmployeeId
      },
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: {
            date: swapRequest.swapDate
          }
        }
      ],
      transaction
    });

    if (!targetAssignment) {
      return {
        canExecute: false,
        reason: `Target employee has no assignment on ${swapRequest.swapDate}`,
        details: {
          targetEmployeeId: swapRequest.targetEmployeeId,
          swapDate: swapRequest.swapDate
        }
      };
    }

    console.log('✅ Target employee availability check passed');
    return {
      canExecute: true,
      reason: 'Target employee is available for direct swap',
      details: {
        targetAssignmentId: targetAssignment.id,
        targetShiftId: targetAssignment.shiftInstance.rotaShiftId
      }
    };
  }

  /**
   * Check target shift availability (Case 2)
   */
  async checkTargetShiftAvailability(swapRequest, requester, tenantContext, transaction) {

    console.log('🔍 Checking target shift availability...');

    // Get desired shift instance
    const desiredShiftInstance = await RotaShiftInstance.findOne({
      where: {
        date: swapRequest.swapDate,
        rotaShiftId: swapRequest.desiredShiftId
      },
      transaction
    });

    if (!desiredShiftInstance) {
      return {
        canExecute: false,
        reason: `Desired shift not found on ${swapRequest.swapDate}`,
        details: {
          desiredShiftId: swapRequest.desiredShiftId,
          swapDate: swapRequest.swapDate
        }
      };
    }

    // Check designation-based availability
    const availability = await this.checkDesignationAvailability(
      desiredShiftInstance,
      requester.designationId,
      transaction
    );

    if (!availability.hasAvailableSlots) {
      // If desired shift is full, check if fallback is enabled
      if (swapRequest.swapWithAnyAvailableShift) {
        console.log('⚠️ Desired shift full, checking fallback to any available shift...');
        return await this.checkAnyAvailableShiftAvailability(swapRequest, requester, tenantContext, transaction);
      } else {
        return {
          canExecute: false,
          reason: `Desired shift has no available slots for ${requester.designation.name} designation`,
          details: {
            desiredShiftId: swapRequest.desiredShiftId,
            designationName: requester.designation.name,
            availableSlots: availability.availableSlots,
            requiredCount: availability.requiredCount,
            assignedCount: availability.assignedCount,
            pendingRequests: availability.pendingRequests,
            sourceType: availability.sourceType
          }
        };
      }
    }

    console.log('✅ Target shift availability check passed');
    return {
      canExecute: true,
      reason: `Desired shift has available slots for ${requester.designation.name} designation`,
      details: {
        desiredShiftId: swapRequest.desiredShiftId,
        designationName: requester.designation.name,
        availableSlots: availability.availableSlots,
        sourceType: availability.sourceType
      }
    };
  }

  /**
   * Check any available shift availability (Case 3)
   */
  async checkAnyAvailableShiftAvailability(swapRequest, requester, tenantContext, transaction) {

    console.log('🔍 Checking any available shift availability...');

    // Get current assignment to exclude current shift
    const currentAssignment = await ShiftAssignment.findByPk(swapRequest.currentShiftAssignmentId, {
      include: [{ model: RotaShiftInstance, as: 'shiftInstance' }],
      transaction
    });

    if (!currentAssignment) {
      return {
        canExecute: false,
        reason: 'Current assignment not found',
        details: { currentShiftAssignmentId: swapRequest.currentShiftAssignmentId }
      };
    }

    // Find all shift instances on swap date (excluding current shift)
    const allShiftInstances = await RotaShiftInstance.findAll({
      where: {
        date: swapRequest.swapDate,
        id: { [Op.ne]: currentAssignment.shiftInstance.id }
      },
      include: [
        {
          model: RotaShift,
          as: 'rotaShift',
          where: {
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId,
            isActive: true
          }
        }
      ],
      transaction
    });

    if (allShiftInstances.length === 0) {
      return {
        canExecute: false,
        reason: `No other shifts are scheduled on ${swapRequest.swapDate}`,
        details: { swapDate: swapRequest.swapDate }
      };
    }

    // Check each shift for designation-based availability
    const availableShifts = [];

    for (const shiftInstance of allShiftInstances) {
      const availability = await this.checkDesignationAvailability(
        shiftInstance,
        requester.designationId,
        transaction
      );

      if (availability.hasAvailableSlots) {
        availableShifts.push({
          shiftInstanceId: shiftInstance.id,
          rotaShiftId: shiftInstance.rotaShiftId,
          shiftName: shiftInstance.rotaShift.name,
          availableSlots: availability.availableSlots,
          sourceType: availability.sourceType
        });
      }
    }

    if (availableShifts.length === 0) {
      return {
        canExecute: false,
        reason: `No shifts have available slots for ${requester.designation.name} designation on ${swapRequest.swapDate}`,
        details: {
          designationName: requester.designation.name,
          swapDate: swapRequest.swapDate,
          totalShiftsChecked: allShiftInstances.length
        }
      };
    }

    console.log('✅ Any available shift availability check passed');
    return {
      canExecute: true,
      reason: `Found ${availableShifts.length} shifts with available slots for ${requester.designation.name} designation`,
      details: {
        designationName: requester.designation.name,
        availableShifts: availableShifts
      }
    };
  }

  /**
   * Execute swap based on request configuration
   * @param {Object} swapRequest - Swap request object
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Execution result
   */
  async executeSwapBasedOnRequest(swapRequest, tenantContext, transaction) {

    console.log('🔄 Executing swap based on request configuration:', {
      targetEmployeeId: swapRequest.targetEmployeeId,
      desiredShiftId: swapRequest.desiredShiftId,
      swapWithAnyAvailableEmployee: swapRequest.swapWithAnyAvailableEmployee,
      swapWithAnyAvailableShift: swapRequest.swapWithAnyAvailableShift
    });

    // Determine execution strategy based on swap request configuration
    if (swapRequest.targetEmployeeId && swapRequest.desiredShiftId) {
      // CASE 1: Specific employee + specific shift
      return await this.executeSpecificEmployeeShiftSwap(swapRequest, tenantContext, transaction);
    }
    else if (swapRequest.targetEmployeeId) {
      // CASE 2: Specific employee (any shift)
      return await this.executeSpecificEmployeeSwap(swapRequest, tenantContext, transaction);
    }
    else if (swapRequest.desiredShiftId) {
      // CASE 3: Specific shift (any employee)
      return await this.executeSpecificShiftSwap(swapRequest, tenantContext, transaction);
    }
    // else if (swapRequest.swapWithAnyAvailableEmployee) {
    //   // CASE 4: Any available employee
    //   return await this.executeAnyAvailableEmployeeSwap(swapRequest, tenantContext, transaction);
    // }
    else if (swapRequest.swapWithAnyAvailableShift) {
      // CASE 5: Any available shift
      return await this.executeAnyAvailableShiftSwap(swapRequest, tenantContext, transaction);
    }
    else {
      throw new Error('Invalid swap request configuration - no valid swap option specified');
    }
  }

  /**
   * CASE 1: Target Employee Selected - Direct Assignment Swap
   * Find target employee's assignment on swap date and swap with requester
   */
  async executeSpecificEmployeeShiftSwap(swapRequest, tenantContext, transaction) {

    console.log('🔄 CASE 1: Target employee selected - Direct assignment swap');

    // Find target employee's assignment on swap date (any shift)
    const targetAssignment = await ShiftAssignment.findOne({
      where: {
        employeeId: swapRequest.targetEmployeeId
      },
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: {
            date: swapRequest.swapDate
          }
        }
      ],
      transaction
    });

    if (!targetAssignment) {
      throw new Error('Target employee has no assignment on the specified date');
    }

    console.log(`📋 Found target assignment: ${targetAssignment.id} for employee ${swapRequest.targetEmployeeId}`);

    // Perform direct swap: Requester ↔ Target Employee
    return await this.performDirectAssignmentSwap(
      swapRequest.currentShiftAssignmentId,
      targetAssignment.id,
      tenantContext,
      transaction
    );
  }

  /**
   * Execute specific employee swap (any shift)
   */
  async executeSpecificEmployeeSwap(swapRequest, tenantContext, transaction) {

    console.log('🔄 Executing specific employee swap');

    // Find target employee's assignment on swap date
    const targetAssignment = await ShiftAssignment.findOne({
      where: {
        employeeId: swapRequest.targetEmployeeId
      },
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: {
            date: swapRequest.swapDate
          }
        }
      ],
      transaction
    });

    if (!targetAssignment) {
      throw new Error('Target employee has no assignment on the specified date');
    }

    // Perform the swap
    return await this.performDirectAssignmentSwap(
      swapRequest.currentShiftAssignmentId,
      targetAssignment.id,
      tenantContext,
      transaction
    );
  }

  /**
   * CASE 2: Target Shift Selected - Designation-based availability check
   * 1. Check if desired shift has available slots for requester's designation
   * 2. If no slots for designation → Fallback to any available shift
   */
  async executeSpecificShiftSwap(swapRequest, tenantContext, transaction) {

    console.log('🔄 CASE 2: Target shift selected - Designation-based availability check');

    // Get requester's designation
    const requester = await Employee.findByPk(swapRequest.requesterId, {
      include: [{ model:Designation, as: 'designation' }],
      transaction
    });

    if (!requester || !requester.designationId) {
      throw new Error('Requester designation not found');
    }

    console.log(`� Requester designation: ${requester.designation.name} (ID: ${requester.designationId})`);

    // Find the desired shift instance on swap date
    const desiredShiftInstance = await RotaShiftInstance.findOne({
      where: {
        date: swapRequest.swapDate,
        rotaShiftId: swapRequest.desiredShiftId
      },
      include: [
        {
          model: RotaShift,
          as: 'rotaShift',
          where: {
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId
          }
        }
      ],
      transaction
    });

    if (!desiredShiftInstance) {
      throw new Error('Desired shift not found on the specified date');
    }

    // Check designation-based availability
    const designationAvailability = await this.checkDesignationAvailability(
      desiredShiftInstance,
      requester.designationId,
      transaction
    );

    console.log(`📊 Designation availability:`, designationAvailability);

    if (designationAvailability.hasAvailableSlots) {
      console.log('✅ Desired shift has available slots for requester designation - Assigning');

      // Get current assignment to destroy
      const currentAssignment = await ShiftAssignment.findByPk(swapRequest.currentShiftAssignmentId, {
        include: [{ model: RotaShiftInstance, as: 'shiftInstance' }],
        transaction
      });

      // Create new assignment in desired shift
      const newAssignment = await ShiftAssignment.create({
        shiftInstanceId: desiredShiftInstance.id,
        employeeId: swapRequest.requesterId,
        assignmentType: 'swap_assigned',
        status: 'assigned',
        assignedBy: tenantContext.userId,
        notes: `Assigned to desired shift from assignment ${currentAssignment.id} (designation-based)`,
        createdById: tenantContext.userId
      }, { transaction });

      // Remove current assignment
      await currentAssignment.destroy({ transaction });

      // Update shift instance counters
      await desiredShiftInstance.increment('totalAssigned', { transaction });
      await currentAssignment.shiftInstance.decrement('totalAssigned', { transaction });

      // Update designation requirements in shift instance
      await this.updateDesignationRequirements(
        desiredShiftInstance,
        requester.designationId,
        'increment',
        transaction
      );
      await this.updateDesignationRequirements(
        currentAssignment.shiftInstance,
        requester.designationId,
        'decrement',
        transaction
      );

      return {
        type: 'designation_based_assignment',
        originalAssignment: currentAssignment.id,
        newAssignment: newAssignment.id,
        originalShift: currentAssignment.shiftInstance.rotaShiftId,
        newShift: desiredShiftInstance.rotaShiftId,
        designationId: requester.designationId,
        designationName: requester.designation.name,
        message: `Employee assigned to desired shift (${requester.designation.name} designation)`
      };

    } else {
      console.log('⚠️ Desired shift is full for requester designation - Finding any available shift');

      // Fallback: Find any available shift with designation-based logic
      return await this.executeAnyAvailableShiftSwap(swapRequest, tenantContext, transaction);
    }
  }

  /**
   * Execute any available employee swap
   */
  async executeAnyAvailableEmployeeSwap(swapRequest, tenantContext, transaction) {

    console.log('🔄 Executing any available employee swap');

    // Find any available employee assignment on swap date
    const targetAssignment = await ShiftAssignment.findOne({
      where: {
        employeeId: { [Op.ne]: swapRequest.requesterId } // Not the requester
      },
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: {
            date: swapRequest.swapDate
          }
        },
        {
          model: Employee,
          as: 'employee',
          where: {
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId,
            status: 'active'
          }
        }
      ],
      order: [['createdAt', 'ASC']], // First available
      transaction
    });

    if (!targetAssignment) {
      throw new Error('No available employee found for swap on the specified date');
    }

    // Perform the swap
    return await this.performDirectAssignmentSwap(
      swapRequest.currentShiftAssignmentId,
      targetAssignment.id,
      tenantContext,
      transaction
    );
  }

  /**
   * CASE 3: Any Available Shift - Designation-based availability search
   * Find any shift with available slots for requester's designation
   */
  async executeAnyAvailableShiftSwap(swapRequest, tenantContext, transaction) {
   

    console.log('🔄 CASE 3: Any available shift - Designation-based availability search');

    // Get requester's designation
    const requester = await Employee.findByPk(swapRequest.requesterId, {
      include: [{ model:Designation, as: 'designation' }],
      transaction
    });

    if (!requester || !requester.designationId) {
      throw new Error('Requester designation not found');
    }

    console.log(`� Requester designation: ${requester.designation.name} (ID: ${requester.designationId})`);

    // Find current assignment details
    const currentAssignment = await ShiftAssignment.findByPk(swapRequest.currentShiftAssignmentId, {
      include: [{ model: RotaShiftInstance, as: 'shiftInstance' }],
      transaction
    });

    if (!currentAssignment) {
      throw new Error('Current assignment not found');
    }

    console.log(`📋 Current assignment: ${currentAssignment.id}, Current shift: ${currentAssignment.shiftInstance.rotaShiftId}`);

    // Find all shift instances on swap date (excluding current shift)
    const allShiftInstances = await RotaShiftInstance.findAll({
      where: {
        date: swapRequest.swapDate,
        id: { [Op.ne]: currentAssignment.shiftInstance.id } // Different shift
      },
      include: [
        {
          model: RotaShift,
          as: 'rotaShift',
          where: {
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId
          }
        }
      ],
      order: [['createdAt', 'ASC']], // First available
      transaction
    });

    console.log(`🔍 Found ${allShiftInstances.length} shift instances to check for designation availability`);

    // Check each shift for designation-based availability
    let availableShiftInstance = null;
    let designationAvailability = null;

    for (const shiftInstance of allShiftInstances) {
      const availability = await this.checkDesignationAvailability(
        shiftInstance,
        requester.designationId,
        transaction
      );

      console.log(`📊 Shift ${shiftInstance.rotaShiftId} availability for ${requester.designation.name}:`, availability);

      if (availability.hasAvailableSlots) {
        availableShiftInstance = shiftInstance;
        designationAvailability = availability;
        break;
      }
    }

    if (!availableShiftInstance) {
      throw new Error(`No available shift found with slots for ${requester.designation.name} designation on the specified date`);
    }

    console.log(`✅ Found available shift: ${availableShiftInstance.rotaShiftId} with ${designationAvailability.availableSlots} slots for ${requester.designation.name}`);

    // Create new assignment for the available shift
    const newAssignment = await ShiftAssignment.create({
      shiftInstanceId: availableShiftInstance.id,
      employeeId: swapRequest.requesterId,
      assignmentType: 'swap_assigned',
      status: 'assigned',
      assignedBy: tenantContext.userId,
      notes: `Reassigned from assignment ${currentAssignment.id} to available shift (designation-based)`,
      createdById: tenantContext.userId
    }, { transaction });

    // Remove current assignment
    await currentAssignment.destroy({ transaction });

    // Update shift instance counters
    await availableShiftInstance.increment('totalAssigned', { transaction });
    await currentAssignment.shiftInstance.decrement('totalAssigned', { transaction });

    // Update designation requirements in shift instances
    await this.updateDesignationRequirements(
      availableShiftInstance,
      requester.designationId,
      'increment',
      transaction
    );
    await this.updateDesignationRequirements(
      currentAssignment.shiftInstance,
      requester.designationId,
      'decrement',
      transaction
    );

    console.log(`✅ Employee reassigned from shift ${currentAssignment.shiftInstance.rotaShiftId} to shift ${availableShiftInstance.rotaShiftId} (designation-based)`);

    return {
      type: 'designation_based_reassignment',
      originalAssignment: currentAssignment.id,
      newAssignment: newAssignment.id,
      originalShift: currentAssignment.shiftInstance.rotaShiftId,
      newShift: availableShiftInstance.rotaShiftId,
      designationId: requester.designationId,
      designationName: requester.designation.name,
      availableSlots: designationAvailability.availableSlots,
      message: `Employee reassigned to available shift (${requester.designation.name} designation)`
    };
  }

  /**
   * Perform direct assignment swap between two employees
   */
  async performDirectAssignmentSwap(assignment1Id, assignment2Id, tenantContext, transaction) {

    console.log(`🔄 Performing direct assignment swap: ${assignment1Id} ↔ ${assignment2Id}`);

    // Get both assignments
    const [assignment1, assignment2] = await Promise.all([
      ShiftAssignment.findByPk(assignment1Id, { transaction }),
      ShiftAssignment.findByPk(assignment2Id, { transaction })
    ]);

    if (!assignment1 || !assignment2) {
      throw new Error('One or both assignments not found for swap execution');
    }

    // Swap the employee assignments
    const tempEmployeeId = assignment1.employeeId;

    await assignment1.update({
      employeeId: assignment2.employeeId,
      assignmentType: 'swap_assigned',
      notes: `Swapped from assignment ${assignment2.id}`,
      updatedById: tenantContext.userId
    }, { transaction });

    await assignment2.update({
      employeeId: tempEmployeeId,
      assignmentType: 'swap_assigned',
      notes: `Swapped from assignment ${assignment1.id}`,
      updatedById: tenantContext.userId
    }, { transaction });

    return {
      type: 'direct_swap',
      assignment1: assignment1.id,
      assignment2: assignment2.id,
      employee1: tempEmployeeId,
      employee2: assignment1.employeeId, // Now has assignment2's original employee
      message: 'Direct employee assignment swap completed'
    };
  }

  /**
   * Check designation-based availability for a shift instance (Enhanced)
   * Priority: customRequirements > designationRequirements > fallback
   * Also considers pending swap requests to prevent over-booking
   * @param {Object} shiftInstance - Shift instance object
   * @param {number} designationId - Designation ID to check
   * @param {Object} transaction - Database transaction
   * @returns {Object} Availability information
   */
  async checkDesignationAvailability(shiftInstance, designationId, transaction) {

    console.log(`🔍 Checking designation availability for designation ${designationId} in shift instance ${shiftInstance.id}`);

    // Get both designation requirements and custom requirements
    const designationRequirements = shiftInstance.designationRequirements || [];
    const customRequirements = shiftInstance.customRequirements || [];

    console.log('📊 Raw requirements data:', {
      designationRequirements: designationRequirements.filter(req => req.designationId === designationId),
      customRequirements: customRequirements.filter(req => req.designationId === designationId),
      shiftInstanceId: shiftInstance.id,
      rotaShiftId: shiftInstance.rotaShiftId
    });

    // PRIORITY 1: Check customRequirements first (schedule-specific overrides)
    let designationRequirement = customRequirements.find(
      req => req.designationId === designationId
    );
    let sourceType = 'custom';

    // PRIORITY 2: If not found in custom, check designationRequirements (template-based)
    if (!designationRequirement) {
      designationRequirement = designationRequirements.find(
        req => req.designationId === designationId
      );
      sourceType = 'template';
    }

    // PRIORITY 3: If no specific requirement found, fallback to general availability
    if (!designationRequirement) {
      console.log(`⚠️ No specific requirement found for designation ${designationId} in shift ${shiftInstance.rotaShiftId} - Using fallback`);

      const hasGeneralAvailability = shiftInstance.totalAssigned < shiftInstance.totalRequired;

      return {
        hasAvailableSlots: hasGeneralAvailability,
        availableSlots: hasGeneralAvailability ? (shiftInstance.totalRequired - shiftInstance.totalAssigned) : 0,
        requiredCount: 0,
        assignedCount: 0,
        pendingRequests: 0,
        effectiveAssignedCount: 0,
        designationId,
        fallbackToGeneral: true,
        sourceType: 'fallback'
      };
    }

    console.log(`📋 Found designation requirement from ${sourceType}:`, designationRequirement);

    // Count current assignments for this designation in this shift
    const currentAssignedCount = await ShiftAssignment.count({
      where: {
        shiftInstanceId: shiftInstance.id
      },
      include: [
        {
          model: Employee,
          as: 'employee',
          where: {
            designationId: designationId
          }
        }
      ],
      transaction
    });

    // Count pending swap requests that would consume slots for this designation
    // This prevents over-booking when multiple requests are pending
    const pendingSwapRequests = await ShiftSwapRequest.count({
      where: {
        desiredShiftId: shiftInstance.rotaShiftId,
        swapDate: shiftInstance.date,
        status: 'pending'
      },
      include: [
        {
          model: Employee,
          as: 'requester',
          where: {
            designationId: designationId
          }
        }
      ],
      transaction
    });

    console.log(`📊 Availability calculation:`, {
      requiredCount: designationRequirement.requiredCount,
      currentAssignedCount,
      pendingSwapRequests,
      sourceType
    });

    const requiredCount = designationRequirement.requiredCount || 0;
    const effectiveAssignedCount = currentAssignedCount + pendingSwapRequests; // Include pending requests
    const availableSlots = Math.max(0, requiredCount - effectiveAssignedCount);
    const hasAvailableSlots = availableSlots > 0;

    return {
      hasAvailableSlots,
      availableSlots,
      requiredCount,
      assignedCount: currentAssignedCount,
      pendingRequests: pendingSwapRequests,
      effectiveAssignedCount,
      designationId,
      fallbackToGeneral: false,
      sourceType
    };
  }

  /**
   * Update designation requirements in shift instance
   * @param {Object} shiftInstance - Shift instance object
   * @param {number} designationId - Designation ID
   * @param {string} operation - 'increment' or 'decrement'
   * @param {Object} transaction - Database transaction
   */
  async updateDesignationRequirements(shiftInstance, designationId, operation, transaction) {
    try {
      const designationRequirements = shiftInstance.designationRequirements || [];

      // Find and update the designation requirement
      const requirementIndex = designationRequirements.findIndex(
        req => req.designationId === designationId
      );

      if (requirementIndex !== -1) {
        const currentAssigned = designationRequirements[requirementIndex].assignedCount || 0;

        if (operation === 'increment') {
          designationRequirements[requirementIndex].assignedCount = currentAssigned + 1;
        } else if (operation === 'decrement') {
          designationRequirements[requirementIndex].assignedCount = Math.max(0, currentAssigned - 1);
        }

        // Update the shift instance
        await shiftInstance.update({
          designationRequirements: designationRequirements
        }, { transaction });

        console.log(`✅ Updated designation requirements for shift ${shiftInstance.id}, designation ${designationId}: ${operation}`);
      } else {
        console.log(`⚠️ Designation requirement not found for designation ${designationId} in shift ${shiftInstance.id}`);
      }
    } catch (error) {
      console.error(`❌ Failed to update designation requirements:`, error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Get pending approvals for user
   * @param {number} userId - User ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} filters - Additional filters
   * @returns {Array} Pending swap approvals
   */
  async getPendingApprovalsForUser(userId, tenantContext, filters = {}) {
    const { page = 1, limit = 10, urgency, departmentId } = filters;
    const offset = (page - 1) * limit;

    const whereClause = {
      assigneeId: userId,
      status: 'pending'
    };

    const includeClause = [
      {
        model: WorkflowInstance,
        as: 'workflowInstance',
        where: {
          entityType: this.entityType,
          companyId: tenantContext.companyId
        },
        include: [
          {
            model: ShiftSwapRequest,
            as: 'shiftSwapRequest',
            where: urgency ? { urgency } : {},
            include: [
              {
                model: Employee,
                as: 'requester',
                attributes: ['id', 'firstName', 'lastName', 'email', 'employeeId'],
                include: departmentId ? [
                  {
                    model: Department,
                    as: 'department',
                    where: { id: departmentId }
                  }
                ] : []
              }
            ]
          }
        ]
      }
    ];

    const { count, rows: assignments } = await WorkflowAssignment.findAndCountAll({
      where: whereClause,
      include: includeClause,
      order: [['createdAt', 'ASC']],
      limit,
      offset,
      distinct: true
    });

    return {
      assignments,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
        hasNext: page < Math.ceil(count / limit),
        hasPrev: page > 1
      }
    };
  }

  /**
   * Bulk approve swap requests
   * @param {Array} swapRequestIds - Swap request IDs
   * @param {Object} approvalData - Approval data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Bulk approval results
   */
  async bulkApproveSwapRequests(swapRequestIds, approvalData, tenantContext) {
    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      approved: []
    };

    for (const swapRequestId of swapRequestIds) {
      try {
        const result = await this.processWorkflowAction(
          swapRequestId,
          'approve',
          approvalData,
          tenantContext
        );
        
        results.approved.push(result);
        results.successful++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          swapRequestId,
          error: error.message
        });
      }
    }

    return results;
  }

  // Helper Methods

  /**
   * Get shift swap workflow definition
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Workflow definition
   */
  async getShiftSwapWorkflowDefinition(tenantContext, transaction) {
    return await WorkflowDefinition.findOne({
      where: {
        entityType: this.entityType,
        companyId: tenantContext.companyId,
        isActive: true
      },
      include: [
        {
          model: WorkflowStep,
          as: 'steps',
          order: [['stepOrder', 'ASC']]
        }
      ],
      transaction
    });
  }

  /**
   * Create default shift swap workflow
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async createDefaultShiftSwapWorkflow(tenantContext, transaction) {
    // Create workflow definition
    const workflowDefinition = await WorkflowDefinition.create({
      name: 'Default Shift Swap Approval',
      description: 'Default approval workflow for shift swap requests',
      entityType: this.entityType,
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      isActive: true,
      autoStart: true,
      createdById: tenantContext.userId
    }, { transaction });

    // Create workflow steps
    await WorkflowStep.create({
      workflowDefinitionId: workflowDefinition.id,
      stepName: 'Manager Approval',
      stepType: 'approval',
      stepOrder: 1,
      isRequired: true,
      approverType: 'manager',
      approverLevel: 1,
      escalationTimeHours: 24,
      allowDelegation: true,
      createdById: tenantContext.userId
    }, { transaction });

    await WorkflowStep.create({
      workflowDefinitionId: workflowDefinition.id,
      stepName: 'HR Approval',
      stepType: 'approval',
      stepOrder: 2,
      isRequired: false,
      approverType: 'role',
      approverRoleId: await this.getHRRoleId(tenantContext, transaction),
      escalationTimeHours: 48,
      allowDelegation: true,
      createdById: tenantContext.userId
    }, { transaction });

    return workflowDefinition;
  }

  /**
   * Map urgency to workflow priority
   * @param {string} urgency - Urgency level
   * @returns {string} Priority level
   */
  mapUrgencyToPriority(urgency) {
    const mapping = {
      'low': 'low',
      'medium': 'medium',
      'high': 'high'
    };
    return mapping[urgency] || 'medium';
  }

  /**
   * Map workflow status to swap status
   * @param {string} workflowStatus - Workflow status
   * @param {string} action - Action taken
   * @returns {string} Swap status
   */
  mapWorkflowStatusToSwapStatus(workflowStatus, action) {
    if (action === 'reject') return 'rejected';
    
    const mapping = {
      'pending': 'pending_approval',
      'in_progress': 'pending_approval',
      'completed': 'approved',
      'rejected': 'rejected',
      'cancelled': 'cancelled'
    };
    
    return mapping[workflowStatus] || 'pending_approval';
  }

  /**
   * Handle workflow completion
   * @param {Object} swapRequest - Swap request
   * @param {Object} workflowResult - Workflow result
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async handleWorkflowCompletion(swapRequest, workflowResult, tenantContext, transaction) {
    // Mark as approved and ready for execution
    await swapRequest.update({
      status: 'approved',
      workflowCompletedAt: new Date()
    }, { transaction });

    // Auto-execute if configured
    const autoExecute = await this.shouldAutoExecuteSwap(swapRequest, tenantContext);
    if (autoExecute) {
      // Execute the actual shift swap
      console.log(`🔄 Auto-executing swap request ${swapRequest.id}`);
      await this.executeApprovedShiftSwap(swapRequest.id, tenantContext);
    }
  }

  /**
   * Send workflow notifications
   * @param {Object} swapRequest - Swap request
   * @param {string} action - Action taken
   * @param {Object} workflowResult - Workflow result
   * @param {Object} tenantContext - Tenant context
   */
  async sendWorkflowNotifications(swapRequest, action, workflowResult, tenantContext) {
    // This would integrate with notification service
    console.log(`📧 Sending ${action} notification for swap request ${swapRequest.id}`);
    
    // Notify requester
    // Notify target employee
    // Notify next approvers if workflow continues
  }

  /**
   * Check if swap should auto-execute
   * @param {Object} swapRequest - Swap request
   * @param {Object} tenantContext - Tenant context
   * @returns {boolean} Should auto-execute
   */
  async shouldAutoExecuteSwap(swapRequest, tenantContext) {
    // Check company settings for auto-execution
    // Check if swap is within auto-execution timeframe
    // Check if both parties have confirmed
    return true; // Enable auto-execution for now
  }

  /**
   * Execute approved shift swap - Actually swap the shift assignments
   * @param {number} swapRequestId - Swap request ID
   * @param {Object} tenantContext - Tenant context
   */
  async executeApprovedShiftSwap(swapRequestId, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      console.log(`🔄 Executing approved shift swap ${swapRequestId}`);

      // Get the swap request with assignments
      const swapRequest = await ShiftSwapRequest.findOne({
        where: { id: swapRequestId },
        include: [
          {
            model: ShiftAssignment,
            as: 'requestingEmployeeAssignment',
            required: true
          },
          {
            model: ShiftAssignment,
            as: 'targetEmployeeAssignment',
            required: true
          }
        ],
        transaction
      });

      if (!swapRequest) {
        throw new NotFoundError('Shift swap request not found');
      }

      if (swapRequest.status !== 'approved') {
        throw new ValidationError('Only approved swap requests can be executed');
      }

      // Get the two assignments to swap
      const assignment1 = swapRequest.requestingEmployeeAssignment;
      const assignment2 = swapRequest.targetEmployeeAssignment;

      // Store original employee IDs
      const originalEmployee1Id = assignment1.employeeId;
      const originalEmployee2Id = assignment2.employeeId;

      // Perform the actual swap
      await assignment1.update({
        employeeId: originalEmployee2Id,
        updatedById: tenantContext.userId,
        updatedAt: new Date()
      }, { transaction });

      await assignment2.update({
        employeeId: originalEmployee1Id,
        updatedById: tenantContext.userId,
        updatedAt: new Date()
      }, { transaction });

      // Update swap request status to executed
      await swapRequest.update({
        status: 'executed',
        executedAt: new Date(),
        executedById: tenantContext.userId,
        updatedById: tenantContext.userId
      }, { transaction });

      await transaction.commit();

      console.log(`✅ Successfully executed shift swap ${swapRequestId}`);
      console.log(`   Employee ${originalEmployee1Id} → Assignment ${assignment2.id}`);
      console.log(`   Employee ${originalEmployee2Id} → Assignment ${assignment1.id}`);

      return swapRequest;

    } catch (error) {
      await transaction.rollback();
      console.error(`❌ Error executing shift swap ${swapRequestId}:`, error);
      throw error;
    }
  }

  /**
   * Get HR role ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {number} HR role ID
   */
  async getHRRoleId(tenantContext, transaction) {
    
    const hrRole = await Role.findOne({
      where: {
        name: { [Op.iLike]: '%HR_Manager%' },
        companyId: tenantContext.companyId
      },
      transaction
    });

    return hrRole?.id || null;
  }
}

module.exports = new ShiftSwapWorkflowService();
