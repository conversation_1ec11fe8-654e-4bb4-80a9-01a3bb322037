'use strict';

/**
 * Rota Analytics Service - PRD Implementation
 * 
 * Comprehensive analytics and reporting for rota management:
 * - Schedule performance metrics
 * - Employee utilization analysis
 * - Forecast accuracy tracking
 * - Operational insights
 */

const { 
  RotaSchedule,
  RotaShift,
  RotaShiftInstance,
  ShiftAssignment,
  ShiftSwapRequest,
  DemandForecast,
  Employee,
  Department,
  sequelize
} = require('../../data/models');
const { Op } = require('sequelize');
const moment = require('moment');

class RotaAnalyticsService {

  /**
   * Get comprehensive schedule analytics
   * @param {Object} filters - Analytics filters
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Schedule analytics
   */
  async getScheduleAnalytics(filters, tenantContext) {
    const {
      startDate,
      endDate,
      departmentId,
      scheduleId,
      period = '30d'
    } = filters;

    const dateRange = this.calculateDateRange(startDate, endDate, period);

    const [
      scheduleMetrics,
      assignmentMetrics,
      swapMetrics,
      forecastMetrics,
      utilizationMetrics
    ] = await Promise.all([
      this.getScheduleMetrics(dateRange, departmentId, scheduleId, tenantContext),
      this.getAssignmentMetrics(dateRange, departmentId, tenantContext),
      this.getSwapMetrics(dateRange, departmentId, tenantContext),
      this.getForecastMetrics(dateRange, departmentId, tenantContext),
      this.getUtilizationMetrics(dateRange, departmentId, tenantContext)
    ]);

    return {
      period: dateRange,
      scheduleMetrics,
      assignmentMetrics,
      swapMetrics,
      forecastMetrics,
      utilizationMetrics,
      summary: this.generateAnalyticsSummary({
        scheduleMetrics,
        assignmentMetrics,
        swapMetrics,
        forecastMetrics,
        utilizationMetrics
      })
    };
  }

  /**
   * Get employee performance analytics
   * @param {Object} filters - Analytics filters
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Employee analytics
   */
  async getEmployeeAnalytics(filters, tenantContext) {
    const {
      employeeId,
      departmentId,
      startDate,
      endDate,
      period = '30d'
    } = filters;

    const dateRange = this.calculateDateRange(startDate, endDate, period);

    const whereClause = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId
    };

    if (employeeId) whereClause.id = employeeId;
    if (departmentId) whereClause.departmentId = departmentId;

    const employees = await Employee.findAll({
      where: whereClause,
      include: [
        {
          model: ShiftAssignment,
          as: 'assignments',
          include: [
            {
              model: RotaShiftInstance,
              as: 'shiftInstance',
              where: {
                date: {
                  [Op.between]: [dateRange.startDate, dateRange.endDate]
                }
              },
              include: [
                {
                  model: RotaShift,
                  as: 'rotaShift'
                }
              ]
            }
          ]
        }
      ]
    });

    const employeeMetrics = employees.map(employee => {
      const assignments = employee.assignments || [];
      
      return {
        employeeId: employee.id,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        department: employee.department?.name,
        metrics: this.calculateEmployeeMetrics(assignments)
      };
    });

    return {
      period: dateRange,
      employees: employeeMetrics,
      departmentSummary: this.calculateDepartmentSummary(employeeMetrics)
    };
  }

  /**
   * Get forecast accuracy analytics
   * @param {Object} filters - Analytics filters
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Forecast accuracy analytics
   */
  async getForecastAccuracyAnalytics(filters, tenantContext) {
    const {
      departmentId,
      forecastType,
      startDate,
      endDate,
      period = '30d'
    } = filters;

    const dateRange = this.calculateDateRange(startDate, endDate, period);

    const whereClause = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      forecastDate: {
        [Op.between]: [dateRange.startDate, dateRange.endDate]
      },
      actualRequirement: { [Op.not]: null },
      accuracyScore: { [Op.not]: null }
    };

    if (departmentId) whereClause.departmentId = departmentId;
    if (forecastType) whereClause.forecastType = forecastType;

    const forecasts = await DemandForecast.findAll({
      where: whereClause,
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        }
      ],
      order: [['forecastDate', 'ASC']]
    });

    const accuracyAnalytics = {
      totalForecasts: forecasts.length,
      averageAccuracy: 0,
      accuracyByType: {},
      accuracyByDepartment: {},
      accuracyTrend: this.calculateAccuracyTrend(forecasts),
      confidenceVsAccuracy: this.analyzeConfidenceVsAccuracy(forecasts)
    };

    if (forecasts.length > 0) {
      accuracyAnalytics.averageAccuracy = Math.round(
        forecasts.reduce((sum, f) => sum + f.accuracyScore, 0) / forecasts.length
      );

      // Group by forecast type
      const typeGroups = this.groupBy(forecasts, 'forecastType');
      Object.keys(typeGroups).forEach(type => {
        const typeForecasts = typeGroups[type];
        accuracyAnalytics.accuracyByType[type] = {
          count: typeForecasts.length,
          averageAccuracy: Math.round(
            typeForecasts.reduce((sum, f) => sum + f.accuracyScore, 0) / typeForecasts.length
          )
        };
      });

      // Group by department
      const deptGroups = this.groupBy(forecasts, 'departmentId');
      Object.keys(deptGroups).forEach(deptId => {
        const deptForecasts = deptGroups[deptId];
        const deptName = deptForecasts[0].department?.name || 'Unknown';
        accuracyAnalytics.accuracyByDepartment[deptName] = {
          count: deptForecasts.length,
          averageAccuracy: Math.round(
            deptForecasts.reduce((sum, f) => sum + f.accuracyScore, 0) / deptForecasts.length
          )
        };
      });
    }

    return {
      period: dateRange,
      accuracyAnalytics
    };
  }

  /**
   * Get operational insights
   * @param {Object} filters - Analytics filters
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Operational insights
   */
  async getOperationalInsights(filters, tenantContext) {
    const {
      departmentId,
      startDate,
      endDate,
      period = '30d'
    } = filters;

    const dateRange = this.calculateDateRange(startDate, endDate, period);

    const [
      coverageInsights,
      efficiencyInsights,
      qualityInsights,
      trendInsights
    ] = await Promise.all([
      this.getCoverageInsights(dateRange, departmentId, tenantContext),
      this.getEfficiencyInsights(dateRange, departmentId, tenantContext),
      this.getQualityInsights(dateRange, departmentId, tenantContext),
      this.getTrendInsights(dateRange, departmentId, tenantContext)
    ]);

    return {
      period: dateRange,
      insights: {
        coverage: coverageInsights,
        efficiency: efficiencyInsights,
        quality: qualityInsights,
        trends: trendInsights
      },
      recommendations: this.generateRecommendations({
        coverageInsights,
        efficiencyInsights,
        qualityInsights,
        trendInsights
      })
    };
  }

  // Helper Methods

  /**
   * Calculate date range from filters
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {string} period - Period string
   * @returns {Object} Date range
   */
  calculateDateRange(startDate, endDate, period) {
    if (startDate && endDate) {
      return { startDate, endDate };
    }

    const daysBack = parseInt(period.replace('d', '')) || 30;
    const end = moment().format('YYYY-MM-DD');
    const start = moment().subtract(daysBack, 'days').format('YYYY-MM-DD');

    return { startDate: start, endDate: end };
  }

  /**
   * Get schedule metrics
   * @param {Object} dateRange - Date range
   * @param {number} departmentId - Department ID
   * @param {number} scheduleId - Schedule ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Schedule metrics
   */
  async getScheduleMetrics(dateRange, departmentId, scheduleId, tenantContext) {
    const whereClause = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId
    };

    if (scheduleId) whereClause.id = scheduleId;

    const schedules = await RotaSchedule.findAll({
      where: whereClause,
      include: [
        {
          model: RotaShiftInstance,
          as: 'instances',
          where: {
            date: {
              [Op.between]: [dateRange.startDate, dateRange.endDate]
            }
          },
          include: [
            {
              model: RotaShift,
              as: 'rotaShift',
              where: departmentId ? { departmentId } : {}
            }
          ]
        }
      ]
    });

    const totalSchedules = schedules.length;
    const totalInstances = schedules.reduce((sum, s) => sum + (s.instances?.length || 0), 0);
    const publishedSchedules = schedules.filter(s => s.status === 'published').length;

    return {
      totalSchedules,
      totalInstances,
      publishedSchedules,
      publishRate: totalSchedules > 0 ? Math.round((publishedSchedules / totalSchedules) * 100) : 0
    };
  }

  /**
   * Get assignment metrics
   * @param {Object} dateRange - Date range
   * @param {number} departmentId - Department ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Assignment metrics
   */
  async getAssignmentMetrics(dateRange, departmentId, tenantContext) {
    const assignments = await ShiftAssignment.findAll({
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: {
            date: {
              [Op.between]: [dateRange.startDate, dateRange.endDate]
            }
          },
          include: [
            {
              model: RotaShift,
              as: 'rotaShift',
              where: departmentId ? { departmentId } : {}
            }
          ]
        },
        {
          model: Employee,
          as: 'employee',
          where: {
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId
          }
        }
      ]
    });

    const totalAssignments = assignments.length;
    const confirmedAssignments = assignments.filter(a => a.status === 'confirmed').length;
    const completedAssignments = assignments.filter(a => a.status === 'completed').length;
    const noShowAssignments = assignments.filter(a => a.status === 'no_show').length;

    return {
      totalAssignments,
      confirmedAssignments,
      completedAssignments,
      noShowAssignments,
      confirmationRate: totalAssignments > 0 ? Math.round((confirmedAssignments / totalAssignments) * 100) : 0,
      completionRate: totalAssignments > 0 ? Math.round((completedAssignments / totalAssignments) * 100) : 0,
      noShowRate: totalAssignments > 0 ? Math.round((noShowAssignments / totalAssignments) * 100) : 0
    };
  }

  /**
   * Get swap metrics
   * @param {Object} dateRange - Date range
   * @param {number} departmentId - Department ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Swap metrics
   */
  async getSwapMetrics(dateRange, departmentId, tenantContext) {
    const swaps = await ShiftSwapRequest.findAll({
      where: {
        createdAt: {
          [Op.between]: [
            new Date(dateRange.startDate),
            new Date(dateRange.endDate + 'T23:59:59')
          ]
        }
      },
      include: [
        {
          model: Employee,
          as: 'requester',
          where: {
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId
          }
        }
      ]
    });

    const totalSwaps = swaps.length;
    const approvedSwaps = swaps.filter(s => s.status === 'approved').length;
    const executedSwaps = swaps.filter(s => s.status === 'executed').length;
    const rejectedSwaps = swaps.filter(s => s.status === 'rejected').length;

    return {
      totalSwaps,
      approvedSwaps,
      executedSwaps,
      rejectedSwaps,
      approvalRate: totalSwaps > 0 ? Math.round((approvedSwaps / totalSwaps) * 100) : 0,
      executionRate: totalSwaps > 0 ? Math.round((executedSwaps / totalSwaps) * 100) : 0
    };
  }

  /**
   * Get forecast metrics
   * @param {Object} dateRange - Date range
   * @param {number} departmentId - Department ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Forecast metrics
   */
  async getForecastMetrics(dateRange, departmentId, tenantContext) {
    const forecasts = await DemandForecast.findAll({
      where: {
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        forecastDate: {
          [Op.between]: [dateRange.startDate, dateRange.endDate]
        },
        ...(departmentId && { departmentId })
      }
    });

    const totalForecasts = forecasts.length;
    const activeForecasts = forecasts.filter(f => f.status === 'active').length;
    const averageConfidence = totalForecasts > 0 ? 
      Math.round(forecasts.reduce((sum, f) => sum + f.confidence, 0) / totalForecasts) : 0;

    return {
      totalForecasts,
      activeForecasts,
      averageConfidence
    };
  }

  /**
   * Get utilization metrics
   * @param {Object} dateRange - Date range
   * @param {number} departmentId - Department ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Utilization metrics
   */
  async getUtilizationMetrics(dateRange, departmentId, tenantContext) {
    // This would calculate employee utilization, shift coverage, etc.
    // Placeholder implementation
    return {
      averageUtilization: 85,
      peakUtilization: 95,
      lowUtilization: 70,
      optimalCoverage: 90
    };
  }

  /**
   * Calculate employee metrics
   * @param {Array} assignments - Employee assignments
   * @returns {Object} Employee metrics
   */
  calculateEmployeeMetrics(assignments) {
    const totalAssignments = assignments.length;
    const confirmedAssignments = assignments.filter(a => a.status === 'confirmed').length;
    const completedAssignments = assignments.filter(a => a.status === 'completed').length;
    const noShowAssignments = assignments.filter(a => a.status === 'no_show').length;

    const totalHours = assignments.reduce((sum, assignment) => {
      const shift = assignment.shiftInstance?.rotaShift;
      if (shift) {
        const duration = this.calculateShiftDuration(shift.startTime, shift.endTime);
        return sum + duration;
      }
      return sum;
    }, 0);

    return {
      totalAssignments,
      confirmedAssignments,
      completedAssignments,
      noShowAssignments,
      totalHours,
      confirmationRate: totalAssignments > 0 ? Math.round((confirmedAssignments / totalAssignments) * 100) : 0,
      completionRate: totalAssignments > 0 ? Math.round((completedAssignments / totalAssignments) * 100) : 0,
      noShowRate: totalAssignments > 0 ? Math.round((noShowAssignments / totalAssignments) * 100) : 0
    };
  }

  /**
   * Calculate shift duration
   * @param {string} startTime - Start time
   * @param {string} endTime - End time
   * @returns {number} Duration in hours
   */
  calculateShiftDuration(startTime, endTime) {
    const start = moment(startTime, 'HH:mm');
    const end = moment(endTime, 'HH:mm');
    
    if (end.isBefore(start)) {
      end.add(1, 'day');
    }
    
    return end.diff(start, 'hours', true);
  }

  /**
   * Group array by property
   * @param {Array} array - Array to group
   * @param {string} property - Property to group by
   * @returns {Object} Grouped object
   */
  groupBy(array, property) {
    return array.reduce((groups, item) => {
      const key = item[property];
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
      return groups;
    }, {});
  }

  /**
   * Calculate accuracy trend
   * @param {Array} forecasts - Forecasts array
   * @returns {Array} Trend data
   */
  calculateAccuracyTrend(forecasts) {
    // Group by week and calculate weekly accuracy
    const weeklyData = {};
    
    forecasts.forEach(forecast => {
      const week = moment(forecast.forecastDate).format('YYYY-WW');
      if (!weeklyData[week]) {
        weeklyData[week] = { total: 0, sum: 0 };
      }
      weeklyData[week].total++;
      weeklyData[week].sum += forecast.accuracyScore;
    });

    return Object.keys(weeklyData).map(week => ({
      week,
      accuracy: Math.round(weeklyData[week].sum / weeklyData[week].total)
    }));
  }

  /**
   * Analyze confidence vs accuracy correlation
   * @param {Array} forecasts - Forecasts array
   * @returns {Object} Correlation analysis
   */
  analyzeConfidenceVsAccuracy(forecasts) {
    const confidenceRanges = {
      'low': { min: 0, max: 50, forecasts: [] },
      'medium': { min: 51, max: 80, forecasts: [] },
      'high': { min: 81, max: 100, forecasts: [] }
    };

    forecasts.forEach(forecast => {
      Object.keys(confidenceRanges).forEach(range => {
        const { min, max } = confidenceRanges[range];
        if (forecast.confidence >= min && forecast.confidence <= max) {
          confidenceRanges[range].forecasts.push(forecast);
        }
      });
    });

    const analysis = {};
    Object.keys(confidenceRanges).forEach(range => {
      const rangeForecasts = confidenceRanges[range].forecasts;
      analysis[range] = {
        count: rangeForecasts.length,
        averageAccuracy: rangeForecasts.length > 0 ? 
          Math.round(rangeForecasts.reduce((sum, f) => sum + f.accuracyScore, 0) / rangeForecasts.length) : 0
      };
    });

    return analysis;
  }

  /**
   * Generate analytics summary
   * @param {Object} metrics - All metrics
   * @returns {Object} Summary
   */
  generateAnalyticsSummary(metrics) {
    return {
      overallHealth: this.calculateOverallHealth(metrics),
      keyInsights: this.generateKeyInsights(metrics),
      alerts: this.generateAlerts(metrics)
    };
  }

  /**
   * Calculate overall health score
   * @param {Object} metrics - All metrics
   * @returns {number} Health score (0-100)
   */
  calculateOverallHealth(metrics) {
    const scores = [
      metrics.assignmentMetrics.completionRate || 0,
      metrics.assignmentMetrics.confirmationRate || 0,
      100 - (metrics.assignmentMetrics.noShowRate || 0),
      metrics.swapMetrics.approvalRate || 0,
      metrics.forecastMetrics.averageConfidence || 0
    ];

    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  }

  /**
   * Generate key insights
   * @param {Object} metrics - All metrics
   * @returns {Array} Key insights
   */
  generateKeyInsights(metrics) {
    const insights = [];

    if (metrics.assignmentMetrics.noShowRate > 10) {
      insights.push('High no-show rate detected - consider improving communication');
    }

    if (metrics.swapMetrics.approvalRate < 70) {
      insights.push('Low swap approval rate - review swap policies');
    }

    if (metrics.forecastMetrics.averageConfidence < 60) {
      insights.push('Low forecast confidence - consider improving historical data');
    }

    return insights;
  }

  /**
   * Generate alerts
   * @param {Object} metrics - All metrics
   * @returns {Array} Alerts
   */
  generateAlerts(metrics) {
    const alerts = [];

    if (metrics.assignmentMetrics.noShowRate > 15) {
      alerts.push({
        type: 'warning',
        message: 'Critical no-show rate detected',
        value: `${metrics.assignmentMetrics.noShowRate}%`
      });
    }

    return alerts;
  }

  // Placeholder methods for additional insights
  async getCoverageInsights(dateRange, departmentId, tenantContext) {
    return { optimalCoverage: 90, actualCoverage: 85 };
  }

  async getEfficiencyInsights(dateRange, departmentId, tenantContext) {
    return { schedulingEfficiency: 88, resourceUtilization: 92 };
  }

  async getQualityInsights(dateRange, departmentId, tenantContext) {
    return { scheduleQuality: 85, employeeSatisfaction: 78 };
  }

  async getTrendInsights(dateRange, departmentId, tenantContext) {
    return { demandTrend: 'increasing', efficiencyTrend: 'stable' };
  }

  calculateDepartmentSummary(employeeMetrics) {
    return {
      totalEmployees: employeeMetrics.length,
      averageUtilization: 85,
      topPerformers: employeeMetrics.slice(0, 5)
    };
  }

  generateRecommendations(insights) {
    return [
      'Consider implementing automated scheduling for better efficiency',
      'Review forecast accuracy and adjust algorithms',
      'Improve employee communication to reduce no-shows'
    ];
  }
}

module.exports = new RotaAnalyticsService();
