'use strict';

const { RotaShiftInstance, ShiftAssignment, RotaShift, RotaSchedule, BusinessUnit } = require('../../data/models');
const { Op } = require('sequelize');
const moment = require('moment-timezone');

/**
 * Shift Completion Service
 * 
 * Handles automatic completion of shifts based on time and attendance integration
 * Implements the recommended approach: Shift End Time + 2 hours = Auto Complete
 */
class ShiftCompletionService {
  constructor() {
    this.gracePeriodHours = 2; // Grace period after shift end time
  }

  /**
   * Auto-complete shifts that have passed their completion time
   * Called by cron job every hour
   * @returns {Array} Array of completion results
   */
  async autoCompleteShifts() {
    try {
      console.log('🔄 Starting automatic shift completion process...');

      // Get all business units to handle timezone-specific completion
      const businessUnits = await BusinessUnit.findAll({
        attributes: ['id', 'companyId', 'timezone'],
        where: {
          status: 'active'
        }
      });

      const allResults = [];

      for (const businessUnit of businessUnits) {
        try {
          const results = await this.autoCompleteShiftsForBusinessUnit(businessUnit);
          allResults.push(...results);
        } catch (error) {
          console.error(`❌ Error completing shifts for business unit ${businessUnit.id}:`, error);
          allResults.push({
            success: false,
            businessUnitId: businessUnit.id,
            error: error.message
          });
        }
      }

      console.log(`✅ Completed automatic shift completion: ${allResults.length} results`);
      return allResults;
    } catch (error) {
      console.error('❌ Error in automatic shift completion:', error);
      throw error;
    }
  }

  /**
   * Auto-complete shifts for a specific business unit
   * @param {Object} businessUnit - Business unit object
   * @returns {Array} Array of completion results
   */
  async autoCompleteShiftsForBusinessUnit(businessUnit) {
    const timezone = businessUnit.timezone || 'UTC';
    const currentTime = moment().tz(timezone);
    
    console.log(`🔄 Processing shifts for business unit ${businessUnit.id} (${timezone})`);

    // Find shift instances that should be completed
    const eligibleInstances = await this.findEligibleInstancesForCompletion(
      businessUnit.id, 
      currentTime, 
      timezone
    );

    console.log(`📋 Found ${eligibleInstances.length} eligible instances for completion`);

    const results = [];

    for (const instance of eligibleInstances) {
      try {
        const result = await this.completeShiftInstance(instance, currentTime);
        results.push(result);
      } catch (error) {
        console.error(`❌ Error completing instance ${instance.id}:`, error);
        results.push({
          success: false,
          instanceId: instance.id,
          shiftId: instance.rotaShiftId,
          date: instance.date,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Find shift instances eligible for completion
   * @param {number} businessUnitId - Business unit ID
   * @param {Object} currentTime - Current moment object
   * @param {string} timezone - Timezone
   * @returns {Array} Array of eligible instances
   */
  async findEligibleInstancesForCompletion(businessUnitId, currentTime, timezone) {
    // Calculate the cutoff time (current time - grace period)
    const cutoffTime = currentTime.clone().subtract(this.gracePeriodHours, 'hours');
    const cutoffDate = cutoffTime.format('YYYY-MM-DD');
    const cutoffTimeOnly = cutoffTime.format('HH:mm:ss');

    console.log(`🕐 Cutoff time for completion: ${cutoffTime.format('YYYY-MM-DD HH:mm:ss')} (${timezone})`);

    // Find instances where shift end time + grace period has passed
    const instances = await RotaShiftInstance.findAll({
      include: [
        {
          model: RotaShift,
          as: 'rotaShift',
          where: {
            businessUnitId: businessUnitId
          }
        },
        {
          model: RotaSchedule,
          as: 'schedule',
          where: {
            status: 'published' // Only complete published schedules
          }
        },
        {
          model: ShiftAssignment,
          as: 'assignments',
          required: false,
          where: {
            status: { [Op.in]: ['assigned', 'confirmed', 'checked_in'] }
          }
        }
      ],
      where: {
        [Op.or]: [
          // Shifts from previous dates
          {
            date: { [Op.lt]: cutoffDate }
          },
          // Shifts from today that have passed cutoff time
          {
            date: cutoffDate,
            '$rotaShift.end_time$': { [Op.lte]: cutoffTimeOnly }
          }
        ]
      }
    });

    // Filter instances that have assignments needing completion
    return instances.filter(instance => 
      instance.assignments && instance.assignments.length > 0
    );
  }

  /**
   * Complete a specific shift instance
   * @param {Object} instance - Shift instance object
   * @param {Object} currentTime - Current moment object
   * @returns {Object} Completion result
   */
  async completeShiftInstance(instance, currentTime) {
    const transaction = await RotaShiftInstance.sequelize.transaction();

    try {
      console.log(`🔄 Completing shift instance ${instance.id} (${instance.rotaShift.name} on ${instance.date})`);

      const completionResults = {
        success: true,
        instanceId: instance.id,
        shiftId: instance.rotaShiftId,
        shiftName: instance.rotaShift.name,
        date: instance.date,
        completedAssignments: 0,
        absentAssignments: 0,
        totalAssignments: instance.assignments.length,
        completedAt: currentTime.toISOString()
      };

      // Process each assignment
      for (const assignment of instance.assignments) {
        const assignmentResult = await this.completeShiftAssignment(
          assignment, 
          instance, 
          currentTime, 
          transaction
        );

        if (assignmentResult.status === 'completed') {
          completionResults.completedAssignments++;
        } else if (assignmentResult.status === 'absent') {
          completionResults.absentAssignments++;
        }
      }

      await transaction.commit();

      console.log(`✅ Completed shift instance ${instance.id}: ${completionResults.completedAssignments} completed, ${completionResults.absentAssignments} absent`);
      return completionResults;

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Complete a specific shift assignment
   * @param {Object} assignment - Shift assignment object
   * @param {Object} instance - Shift instance object
   * @param {Object} currentTime - Current moment object
   * @param {Object} transaction - Database transaction
   * @returns {Object} Assignment completion result
   */
  async completeShiftAssignment(assignment, instance, currentTime, transaction) {
    let newStatus = assignment.status;
    let actualEndTime = assignment.actualEndTime;

    // Determine completion status based on current assignment status
    switch (assignment.status) {
      case 'checked_in':
        // Employee checked in but didn't check out - mark as completed
        newStatus = 'completed';
        if (!actualEndTime) {
          // Set actual end time to shift end time if not recorded
          actualEndTime = instance.rotaShift.endTime;
        }
        break;

      case 'confirmed':
        // Employee confirmed but never checked in - mark as absent
        newStatus = 'absent';
        break;

      case 'assigned':
        // Employee was assigned but never confirmed - mark as absent
        newStatus = 'absent';
        break;

      default:
        // Assignment already in final state, skip
        return { status: assignment.status, action: 'skipped' };
    }

    // Update assignment
    await assignment.update({
      status: newStatus,
      actualEndTime: actualEndTime,
      notes: assignment.notes ? 
        `${assignment.notes}\nAuto-completed by system at ${currentTime.format('YYYY-MM-DD HH:mm:ss')}` :
        `Auto-completed by system at ${currentTime.format('YYYY-MM-DD HH:mm:ss')}`,
      updatedById: null // System update
    }, { transaction });

    return { 
      status: newStatus, 
      action: 'updated',
      assignmentId: assignment.id,
      employeeId: assignment.employeeId
    };
  }

  /**
   * Manually complete a specific shift instance
   * @param {number} instanceId - Shift instance ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Completion result
   */
  async manuallyCompleteShiftInstance(instanceId, tenantContext) {
    const instance = await RotaShiftInstance.findOne({
      where: {
        id: instanceId
      },
      include: [
        {
          model: RotaShift,
          as: 'rotaShift',
          where: {
            companyId: tenantContext.companyId
          }
        },
        {
          model: ShiftAssignment,
          as: 'assignments',
          where: {
            status: { [Op.in]: ['assigned', 'confirmed', 'checked_in'] }
          },
          required: false
        }
      ]
    });

    if (!instance) {
      throw new NotFoundError(`Shift instance ${instanceId} not found`);
    }

    const currentTime = moment();
    return await this.completeShiftInstance(instance, currentTime);
  }

  /**
   * Get completion statistics for a date range
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Completion statistics
   */
  async getCompletionStatistics(startDate, endDate, tenantContext) {
    const assignments = await ShiftAssignment.findAll({
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: {
            date: {
              [Op.between]: [startDate, endDate]
            }
          },
          include: [
            {
              model: RotaShift,
              as: 'rotaShift',
              where: {
                companyId: tenantContext.companyId
              }
            }
          ]
        }
      ]
    });

    const stats = {
      total: assignments.length,
      completed: 0,
      absent: 0,
      inProgress: 0,
      cancelled: 0
    };

    assignments.forEach(assignment => {
      switch (assignment.status) {
        case 'completed':
          stats.completed++;
          break;
        case 'absent':
          stats.absent++;
          break;
        case 'cancelled':
          stats.cancelled++;
          break;
        default:
          stats.inProgress++;
      }
    });

    return stats;
  }
}

module.exports = ShiftCompletionService;
