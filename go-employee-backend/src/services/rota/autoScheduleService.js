'use strict';

/**
 * Auto Schedule Service - PRD Implementation
 * 
 * Handles automatic schedule generation according to PRD specifications:
 * - Template-based instance generation
 * - Demand forecasting integration
 * - Employee auto-assignment with constraints
 * - Optimization algorithms for coverage
 */

const {
  RotaSchedule,
  RotaShift,
  RotaShiftInstance,
  RotaShiftDesignationRequirement,
  RotaShiftInstanceDesignationRequirement,
  ShiftAssignment,
  Employee,
  EmployeeAvailability,
  AvailabilityOverride,
  Department,
  Designation,
  DemandForecast,
  Holiday,
  ShiftTemplate,
  ShiftTemplateDayConfig,
  ShiftTemplateDayShift,
  ShiftTemplateDayShiftDesignation,
  ShiftAssignmentTracker,
  EmployeeWorkloadTracker,
  AutoScheduleSession,
  sequelize
} = require('../../data/models');
const { NotFoundError, ValidationError } = require('../../common/errors');
const { Op } = require('sequelize');
const moment = require('moment');
const instanceForecastingService = require('./instanceForecastingService');
const demandForecastService = require('./demandForecastService');

class AutoScheduleService {

  /**
   * Generate auto-schedule - ENHANCED for dual mode (existing schedule OR template-based)
   * Complete Flow: RotaShift → RotaShiftInstance → ShiftAssignment
   * @param {Object} scheduleRequest - Schedule generation request
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Generation results
   */
  async generateAutoSchedule(scheduleRequest, tenantContext) {
    const {
      mode, // 'existing' or 'template'
      scheduleId,
      businessUnitId,
      departmentIds = [],
      shiftTemplateId, // For template mode
      startDate,
      endDate,
      constraints = {},
      demandForecast = [],
      enableForecastOverride = true
    } = scheduleRequest;

    const transaction = await sequelize.transaction();

    try {
      console.log(`🎯 Auto-schedule generation mode: ${mode}`);

      // 1. Validate schedule exists
      const schedule = await RotaSchedule.findOne({
        where: {
          id: scheduleId,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        transaction
      });

      if (!schedule) {
        throw new NotFoundError('Schedule not found');
      }

      let instanceResults;
      let templatesCount = 0;

      // 2. Generate instances based on mode
      if (mode === 'template') {
        // MODE 1: Template-based generation
        console.log('🎯 Template-based instance generation');
        templatesCount = 1; // Single template mode
        instanceResults = await this.generateInstancesFromTemplate({
          scheduleId,
          shiftTemplateId,
          startDate,
          endDate,
          constraints,
          demandForecast,
          enableForecastOverride
        }, tenantContext, transaction);
      } else {
        // MODE 2: Existing schedule with departments (current functionality)
        console.log('🎯 Department-based instance generation');

        // Get templates for departments
        const templates = await this.getTemplatesForGeneration(
          departmentIds,
          tenantContext,
          transaction
        );

        templatesCount = templates.length;

        // Generate instances from templates with demand forecasting integration
        instanceResults = await this.generateInstancesWithDemandForecasting({
          scheduleId,
          templates,
          startDate,
          endDate,
          enableForecastOverride,
          minConfidenceThreshold: constraints.minForecastConfidence || 70
        }, tenantContext, transaction);
      }
      console.log(constraints.autoAssignEmployees !== false,"--------------")
      // 3. Auto-assign employees if enabled
      let assignmentResults = { successful: 0, failed: 0, assignments: [] };
      if (constraints.autoAssignEmployees !== false) {
        console.log('🤖 Auto-assigning employees to shift instances...');
        try {
          assignmentResults = await this.autoAssignEmployees(
            instanceResults.instances,
            constraints,
            tenantContext,
            transaction
          );
          console.log(`✅ Employee assignment completed: ${assignmentResults.successful} successful, ${assignmentResults.failed} failed`);
        } catch (error) {
          console.error('❌ Employee assignment failed:', error.message);
          assignmentResults = {
            successful: 0,
            failed: 0,
            assignments: [],
            errors: [error.message]
          };
        }
      }

      // Prepare response data before committing transaction
      const responseData = {
        schedule: {
          id: scheduleId,
          name: schedule.name,
          startDate,
          endDate
        },
        generation: {
          templates: templatesCount,
          instances: instanceResults,
          assignments: assignmentResults
        },
        summary: {
          totalInstances: instanceResults.successful,
          totalAssignments: assignmentResults.successful,
          coverage: this.calculateCoverage(instanceResults.instances || [], assignmentResults.assignments || [])
        }
      };

      // Format instances with proper designation requirements
      if (responseData.generation.instances.instances) {
        responseData.generation.instances.instances = await Promise.all(
          responseData.generation.instances.instances.map(async (instance) => {
            return await this.formatInstanceForResponse(instance, transaction);
          })
        );
      }

      // Commit transaction
      await transaction.commit();

      return responseData;

    } catch (error) {
      // Only rollback if transaction is still active
      if (transaction && !transaction.finished) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  /**
   * Get templates for generation based on departments
   * @param {Array} departmentIds - Department IDs
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Array} Templates
   */
  async getTemplatesForGeneration(departmentIds, tenantContext, transaction) {
    const whereClause = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      isActive: true
    };

    if (departmentIds.length > 0) {
      whereClause.departmentId = { [Op.in]: departmentIds };
    }

    // ✅ CRITICAL FIX: Order templates by start time for consistent shift sequence
    const templates = await RotaShift.findAll({
      where: whereClause,
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        },
        {
          model: RotaShiftDesignationRequirement,
          as: 'designationRequirements',
          include: [
            {
              model: Designation,
              as: 'designation',
              attributes: ['id', 'name']
            }
          ]
        }
      ],
      // ✅ CRITICAL: Order by start time to ensure consistent chronological sequence
      order: [
        ['startTime', 'ASC'], // Morning shifts first, then afternoon, then night
        ['id', 'ASC'] // Secondary sort for consistency when start times are equal
      ],
      transaction
    });

    return templates;
  }

  /**
   * Generate instances from templates
   * @param {number} scheduleId - Schedule ID
   * @param {Array} templates - Templates
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {Array} demandForecast - Demand forecast data
   * @param {boolean} enableForecastOverride - Enable forecast override
   * @param {Object} transaction - Database transaction
   * @returns {Object} Instance generation results
   */
  async generateInstancesFromTemplates(scheduleId, templates, startDate, endDate, demandForecast, enableForecastOverride, transaction) {
    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      instances: []
    };

    // Create forecast lookup for quick access
    const forecastLookup = this.createForecastLookup(demandForecast);

    // Generate instances for each day in range
    const start = moment(startDate);
    const end = moment(endDate);

    for (let date = start.clone(); date.isSameOrBefore(end); date.add(1, 'day')) {
      const currentDate = date.format('YYYY-MM-DD');
      const dayOfWeek = date.day(); // 0 = Sunday, 6 = Saturday

      for (const template of templates) {
        try {
          // Check if template should run on this day
          if (!this.shouldTemplateRunOnDay(template, dayOfWeek)) {
            continue;
          }

          // Check if instance already exists
          const existingInstance = await RotaShiftInstance.findOne({
            where: {
              scheduleId,
              rotaShiftId: template.id,
              date: currentDate
            },
            transaction
          });

          if (existingInstance) {
            results.failed++;
            results.errors.push({
              template: template.name,
              date: currentDate,
              error: 'Instance already exists'
            });
            continue;
          }

          // Determine required count (forecast vs template)
          let requiredCount = template.baseRequiredCount;
          
          if (enableForecastOverride) {
            const forecastKey = `${currentDate}_${template?.departmentId}_${template.id}`;
            const forecast = forecastLookup[forecastKey];
            if (forecast) {
              requiredCount = forecast.requiredCount;
            }
          }

          // Create instance
          const instance = await RotaShiftInstance.create({
            scheduleId,
            rotaShiftId: template.id,
            date: currentDate,
            actualRequiredCount: requiredCount,
            overrideReason: enableForecastOverride && forecastLookup[`${currentDate}_${template?.departmentId}_${template.id}`] ?
              'forecast_prediction' : 'template_default',
            notes: enableForecastOverride && forecastLookup[`${currentDate}_${template?.departmentId}_${template.id}`] ?
              'Generated with demand forecast' : 'Generated from template',
            createdById: 1 // System generated
          }, { transaction });

          results.instances.push(instance);
          results.successful++;

        } catch (error) {
          results.failed++;
          results.errors.push({
            template: template.name,
            date: currentDate,
            error: error.message
          });
        }
      }
    }

    return results;
  }

  /**
   * ENHANCED: Auto-assign employees with designation-based assignment
   * @param {Array} instances - Generated instances
   * @param {Object} constraints - Enhanced assignment constraints
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Assignment results
   */
  async autoAssignEmployees(instances, constraints, tenantContext, transaction) {
    const {
      respectAvailability = true,
      balanceWorkload = true,
      minimizeOvertime = true,
      maxConsecutiveDays = 7,
      minRestHours = 12,
      preferredEmployees = [],
      excludedEmployees = [],
      // ENHANCED: New designation-based constraints
      assignmentStrategy = 'smart',
      designationPriority = true, // Force designation-based assignment
      skillMatching = false,
      allowPartialAssignment = true,
      maxShiftsPerEmployee = 40,
      conflictResolution = 'auto'
    } = constraints;

    console.log(`🎯 Enhanced auto-assignment started with strategy: ${assignmentStrategy}`);

    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      assignments: [],
      // ENHANCED: Detailed assignment tracking
      byDesignation: {},
      byStrategy: {
        smart: 0,
        rotation: 0,
        balanced: 0,
        skill_based: 0
      },
      conflicts: {
        resolved: 0,
        pending: 0
      }
    };

    // ENHANCED: Use designation-based assignment if enabled
    if (designationPriority) {
      console.log('🎯 Using designation-based assignment strategy');
      return await this.assignEmployeesByDesignation(instances, constraints, tenantContext, transaction);
    }

    // FALLBACK: Original assignment logic for backward compatibility
    console.log('📋 Using legacy assignment logic');

    // Get all eligible employees
    const employees = await this.getEligibleEmployees(tenantContext, excludedEmployees, transaction);

    // ✅ FIXED: Enhanced workload tracker for long-term schedules with existing assignments
    const scheduleId = instances.length > 0 ? instances[0].scheduleId : null;
    const workloadTracker = await this.initializeEnhancedWorkloadTracker(employees, tenantContext, transaction, scheduleId);

    // Sort instances by date and priority
    const sortedInstances = instances.sort((a, b) => {
      const dateCompare = moment(a.date).diff(moment(b.date));
      if (dateCompare !== 0) return dateCompare;
      return (b.rotaShift?.priority || 5) - (a.rotaShift?.priority || 5);
    });

    // ✅ FIXED: Periodic rebalancing for long-term schedules
    let processedDays = 0;
    const rebalanceInterval = 5; // Rebalance every 5 days for better long-term performance

    for (const instance of sortedInstances) {
      try {
        console.log(`\n📋 Processing instance ${instance.id} for ${instance.date}`);

        // ✅ FIXED: Periodic workload rebalancing for long-term schedules
        processedDays++;
        if (processedDays % rebalanceInterval === 0) {
          console.log(`🔄 Performing periodic workload rebalancing (every ${rebalanceInterval} days)`);
          await this.rebalanceWorkloadTracker(workloadTracker, tenantContext, transaction);
        }

        // ✅ FIXED: Handle template gaps in workload tracking
        this.handleTemplateGapInWorkload(workloadTracker, instance.date);

        const requiredCount = instance.actualRequiredCount || instance.rotaShift?.baseRequiredCount || 1;
        const assignedEmployees = [];

        // Get eligible employees for this instance using proper designation matching
        const eligibleEmployees = await this.getEligibleEmployeesForInstance(
          instance,
          employees,
          constraints,
          workloadTracker,
          respectAvailability,
          transaction
        );

        // Sort employees by priority/preference
        const sortedEmployees = this.sortEmployeesByPriority(
          eligibleEmployees,
          preferredEmployees,
          balanceWorkload,
          workloadTracker
        );
        

        // Assign employees up to required count
        for (let i = 0; i < Math.min(requiredCount, sortedEmployees.length); i++) {
          const employee = sortedEmployees[i];

          // Create assignment
          const assignment = await ShiftAssignment.create({
            shiftInstanceId: instance.id,
            employeeId: employee.id,
            status: 'assigned',
            assignmentType: 'auto_assigned',
            assignedBy: 1, // System
            assignedAt: new Date(),
            notes: 'Auto-assigned by system'
          }, { transaction });

          assignedEmployees.push(assignment);
          results.assignments.push(assignment);
          results.successful++;

          // Update workload tracker
          this.updateWorkloadTracker(workloadTracker, employee.id, instance);
        }

        // Log if under-staffed
        if (assignedEmployees.length < requiredCount) {
          results.errors.push({
            instance: `${instance.rotaShift?.name} on ${instance.date}`,
            error: `Under-staffed: ${assignedEmployees.length}/${requiredCount} assigned`
          });
        }

      } catch (error) {
        results.failed++;
        results.errors.push({
          instance: `${instance.rotaShift?.name} on ${instance.date}`,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Get eligible employees for auto-assignment
   * @param {Object} tenantContext - Tenant context
   * @param {Array} excludedEmployees - Excluded employee IDs
   * @param {Object} transaction - Database transaction
   * @returns {Array} Eligible employees
   */
  async getEligibleEmployees(tenantContext, excludedEmployees, transaction) {
    const whereClause = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      status: 'active'
    };

    if (excludedEmployees.length > 0) {
      whereClause.id = { [Op.notIn]: excludedEmployees };
    }

    const employees = await Employee.findAll({
      where: whereClause,
      transaction
    });

    return employees;
  }

  /**
   * Get eligible employees for specific instance
   * @param {Object} instance - Shift instance
   * @param {Array} employees - All employees
   * @param {Object} constraints - Constraints
   * @param {Object} workloadTracker - Workload tracker
   * @param {boolean} respectAvailability - Respect availability
   * @param {Object} transaction - Database transaction
   * @returns {Array} Eligible employees
   */
  async getEligibleEmployeesForInstance(instance, employees, constraints, workloadTracker, respectAvailability, transaction) {
    const eligibleEmployees = [];

    for (const employee of employees) {
      // Skip department check - only use designation matching
      // Check if employee's designation matches any of the shift's required designations
      let isDesignationMatch = false;

      if (instance.rotaShift?.designation && Array.isArray(instance.rotaShift.designation)) {
        // Check if employee's designation is in the shift's required designations
        isDesignationMatch = instance.rotaShift.designation.some(
          designation => designation.id === employee.designationId
        );
      } else if (instance.rotaShift?.designationRequirements && Array.isArray(instance.rotaShift.designationRequirements)) {
        // Alternative: check through designationRequirements
        isDesignationMatch = instance.rotaShift.designationRequirements.some(
          req => req.designationId === employee.designationId
        );
      } else {
        // If no designation requirements, allow all employees (fallback)
        isDesignationMatch = true;
      }

      if (!isDesignationMatch) {
        continue;
      }

      // Check availability if enabled
      if (respectAvailability) {
        const isAvailable = await this.checkEmployeeAvailabilityForInstance(
          employee,
          instance,
          transaction
        );
        if (!isAvailable) continue;
      }

      // Check workload constraints
      if (!this.checkWorkloadConstraints(employee.id, instance, workloadTracker, constraints)) {
        continue;
      }

      // Check existing assignments for conflicts
      const hasConflict = await this.checkAssignmentConflicts(
        employee.id,
        instance,
        transaction
      );
      if (hasConflict) continue;

      eligibleEmployees.push(employee);
    }

    return eligibleEmployees;
  }

  // Helper Methods

  /**
   * Create forecast lookup for quick access
   * @param {Array} demandForecast - Demand forecast data
   * @returns {Object} Forecast lookup
   */
  createForecastLookup(demandForecast) {
    const lookup = {};
    
    demandForecast.forEach(forecast => {
      const key = `${forecast.date}_${forecast?.departmentId}_${forecast.shiftTemplateId || 'all'}`;
      lookup[key] = forecast;
    });

    return lookup;
  }

  /**
   * Check if template should run on specific day
   * @param {Object} template - Template object
   * @param {number} dayOfWeek - Day of week (0-6)
   * @returns {boolean} Should run
   */
  shouldTemplateRunOnDay(template, dayOfWeek) {
    // Check if template has specific allowed days
    if (template.allowedDays && template.allowedDays.length > 0) {
      return template.allowedDays.includes(dayOfWeek);
    }

    // Check if weekends are included
    if (!template.includeWeekends && (dayOfWeek === 0 || dayOfWeek === 6)) {
      return false;
    }

    return true;
  }

  /**
   * ✅ FIXED: Mark template gap to prevent incorrect reassignments
   * @param {string} date - Gap date
   * @param {Object} tenantContext - Tenant context
   */
  markTemplateGap(date, tenantContext) {
    if (!this.templateGaps) {
      this.templateGaps = new Set();
    }
    this.templateGaps.add(date);
    console.log(`📝 Marked ${date} as template gap`);
  }

  /**
   * ✅ FIXED: Check if date is a template gap
   * @param {string} date - Date to check
   * @returns {boolean} Is template gap
   */
  isTemplateGap(date) {
    return this.templateGaps && this.templateGaps.has(date);
  }

  /**
   * ✅ FIXED: Reset consecutive days after template gaps
   * @param {Object} workloadTracker - Workload tracker
   * @param {string} currentDate - Current date
   */
  handleTemplateGapInWorkload(workloadTracker, currentDate) {
    const previousDate = moment(currentDate).subtract(1, 'day').format('YYYY-MM-DD');

    if (this.isTemplateGap(previousDate)) {
      console.log(`🔄 Handling template gap for ${previousDate} - PRESERVING SHIFT CONSISTENCY`);

      // ✅ ENHANCED: Handle gap while preserving 15-day shift consistency
      Object.keys(workloadTracker).forEach(employeeId => {
        const tracker = workloadTracker[employeeId];

        if (tracker.lastAssignmentDate === previousDate) {
          console.log(`📌 Employee ${employeeId}: Template gap detected, preserving shift type '${tracker.lastAssignedShiftType}'`);

          // ✅ CRITICAL: Don't reset consecutive days - preserve 15-day consistency period
          // Reset only the daily consecutive counter, not the shift type consistency
          if (tracker.consecutiveDays) {
            tracker.consecutiveDays = 0; // Reset daily consecutive counter
          }

          // ✅ IMPORTANT: Preserve shift type and position preferences
          if (!tracker.gapDays) {
            tracker.gapDays = [];
          }
          tracker.gapDays.push({
            date: previousDate,
            type: 'template_gap',
            preservedShiftType: tracker.lastAssignedShiftType,
            preservedPosition: tracker.assignmentSequence?.lastShiftPosition
          });

          // ✅ CRITICAL: Don't change lastAssignmentDate - this maintains consistency calculation
          // The 15-day period continues from the last actual working day
          console.log(`📊 Employee ${employeeId}: Gap handled, shift consistency PRESERVED (${tracker.lastAssignedShiftType})`);
        }
      });
    }
  }

  /**
   * ✅ SHIFT CONSISTENCY: Calculate shift consistency score for employee assignment
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Shift instance
   * @param {Object} workloadTracker - Workload tracker
   * @returns {Object} Consistency score and details
   */
  calculateShiftConsistencyScore(employeeId, instance, workloadTracker) {
    const tracker = workloadTracker[employeeId];
    const currentShiftType = instance.rotaShift?.name;

    if (!tracker || !currentShiftType) {
      return {
        score: 50, // Neutral score
        reason: 'no_history',
        isConsistent: true,
        details: 'No previous assignments to compare'
      };
    }

    // If no previous shift assignments, this is the first assignment
    if (!tracker.lastAssignedShiftType) {
      return {
        score: 100, // Perfect score for first assignment
        reason: 'first_assignment',
        isConsistent: true,
        details: 'First shift assignment for employee - starting 15-day consistency period'
      };
    }

    // ✅ ENHANCED: 15-Day Minimum Consistency Enforcement with Gap Handling
    const currentDate = moment(instance.date);
    const lastAssignmentDate = tracker.lastAssignmentDate ? moment(tracker.lastAssignmentDate) : null;

    // ✅ CRITICAL: Calculate working days since last assignment (excluding gaps)
    let daysSinceLastAssignment = 0;
    if (lastAssignmentDate) {
      daysSinceLastAssignment = currentDate.diff(lastAssignmentDate, 'days');

      // ✅ IMPORTANT: Subtract gap days from the calculation
      if (tracker.gapDays && tracker.gapDays.length > 0) {
        const gapDaysInPeriod = tracker.gapDays.filter(gap => {
          const gapDate = moment(gap.date);
          return gapDate.isAfter(lastAssignmentDate) && gapDate.isBefore(currentDate);
        }).length;

        console.log(`📊 Employee ${employeeId}: ${gapDaysInPeriod} gap days found between ${lastAssignmentDate.format('YYYY-MM-DD')} and ${currentDate.format('YYYY-MM-DD')}`);
        // Don't subtract gap days - they don't break the 15-day consistency period
        // The 15-day period is about maintaining the same shift type, not consecutive working days
      }
    }

    // Check if current shift matches last assigned shift type
    const matchesLast = tracker.lastAssignedShiftType === currentShiftType;
    const matchesPreferred = tracker.preferredShiftType === currentShiftType;

    if (matchesLast) {
      // ✅ PERFECT: Continuing same shift type
      return {
        score: 100, // Perfect consistency
        reason: 'continues_shift_type',
        isConsistent: true,
        details: `Continuing ${currentShiftType} shift (day ${daysSinceLastAssignment + 1} of consistency period)`,
        consistencyDays: daysSinceLastAssignment + 1
      };
    }

    // ✅ CRITICAL: Check 15-day minimum before allowing shift change
    if (tracker.lastAssignedShiftType && daysSinceLastAssignment < 15) {
      // Employee hasn't completed 15-day minimum - heavily penalize shift change
      return {
        score: 10, // Very low score to discourage shift change
        reason: 'premature_shift_change',
        isConsistent: false,
        details: `Attempting to change from ${tracker.lastAssignedShiftType} to ${currentShiftType} after only ${daysSinceLastAssignment} days (minimum: 15 days)`,
        consistencyDays: daysSinceLastAssignment,
        minimumRequired: 15,
        daysRemaining: 15 - daysSinceLastAssignment
      };
    }

    // ✅ ALLOWED: Employee has completed 15-day minimum, shift change is permitted
    if (matchesPreferred) {
      return {
        score: 80, // Good score for returning to preferred shift
        reason: 'returning_to_preferred',
        isConsistent: true,
        details: `Returning to preferred shift type: ${currentShiftType} (completed ${daysSinceLastAssignment} days of ${tracker.lastAssignedShiftType})`,
        consistencyDays: daysSinceLastAssignment
      };
    }

    // Calculate consistency penalty based on shift type history
    const totalAssignments = Object.values(tracker.shiftTypeHistory || {}).reduce((sum, count) => sum + count, 0);
    const currentShiftCount = tracker.shiftTypeHistory?.[currentShiftType] || 0;
    const consistencyRatio = totalAssignments > 0 ? currentShiftCount / totalAssignments : 0;

    let score = 40; // Base score for legitimate shift change after 15 days
    if (consistencyRatio > 0.2) score = 50; // Has some history with this shift type
    if (consistencyRatio > 0.4) score = 60; // Significant history

    return {
      score,
      reason: 'legitimate_shift_change',
      isConsistent: false,
      details: `Changing from ${tracker.lastAssignedShiftType} to ${currentShiftType} after ${daysSinceLastAssignment} days (${Math.round(consistencyRatio * 100)}% history with new shift)`,
      consistencyDays: daysSinceLastAssignment,
      shiftChangeDetails: {
        from: tracker.lastAssignedShiftType,
        to: currentShiftType,
        previousCount: currentShiftCount,
        totalAssignments
      }
    };
  }

  /**
   * ✅ EMPLOYEE SEQUENCING: Calculate employee assignment sequence score for multi-shift consistency
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Shift instance
   * @param {Object} workloadTracker - Workload tracker
   * @param {Array} allInstancesForDate - All shift instances for the same date
   * @returns {Object} Sequence score and details
   */
  calculateEmployeeSequenceScore(employeeId, instance, workloadTracker, allInstancesForDate) {
    const tracker = workloadTracker[employeeId];
    const currentShiftType = instance.rotaShift?.name;

    if (!tracker || !currentShiftType) {
      return {
        score: 50,
        reason: 'no_history',
        isConsistent: true,
        details: 'No previous assignments to compare'
      };
    }

    // Determine shift position (Morning=1, Afternoon=2, Night=3, etc.)
    const sortedInstances = [...allInstancesForDate].sort((a, b) => {
      const timeA = a.rotaShift?.startTime || '00:00';
      const timeB = b.rotaShift?.startTime || '00:00';
      return timeA.localeCompare(timeB);
    });

    const currentShiftPosition = sortedInstances.findIndex(inst => inst.id === instance.id) + 1;
    const sequenceTracker = tracker.assignmentSequence;

    // Initialize sequence tracking if not exists
    if (!sequenceTracker.shiftPositionHistory) {
      sequenceTracker.shiftPositionHistory = {};
    }

    // First assignment - establish preferred position
    if (!sequenceTracker.preferredShiftPosition) {
      return {
        score: 100,
        reason: 'establishing_preference',
        isConsistent: true,
        details: `Establishing preferred shift position: ${currentShiftPosition} (${currentShiftType})`,
        shiftPosition: currentShiftPosition
      };
    }

    // Check consistency with preferred position
    if (sequenceTracker.preferredShiftPosition === currentShiftPosition) {
      return {
        score: 100,
        reason: 'perfect_sequence_match',
        isConsistent: true,
        details: `Matches preferred shift position: ${currentShiftPosition} (${currentShiftType})`,
        shiftPosition: currentShiftPosition
      };
    }

    // Check consistency with last assigned position
    if (sequenceTracker.lastShiftPosition === currentShiftPosition) {
      return {
        score: 85,
        reason: 'continues_last_position',
        isConsistent: true,
        details: `Continues last shift position: ${currentShiftPosition} (${currentShiftType})`,
        shiftPosition: currentShiftPosition
      };
    }

    // Calculate position consistency based on history
    const totalPositionAssignments = Object.values(sequenceTracker.shiftPositionHistory).reduce((sum, count) => sum + count, 0);
    const currentPositionCount = sequenceTracker.shiftPositionHistory[currentShiftPosition] || 0;
    const positionConsistencyRatio = totalPositionAssignments > 0 ? currentPositionCount / totalPositionAssignments : 0;

    let score = 30; // Base score for position change
    if (positionConsistencyRatio > 0.2) score = 45; // Has some history with this position
    if (positionConsistencyRatio > 0.4) score = 60; // Significant history

    return {
      score,
      reason: 'position_change',
      isConsistent: false,
      details: `Changing from position ${sequenceTracker.lastShiftPosition} to ${currentShiftPosition} (${Math.round(positionConsistencyRatio * 100)}% history)`,
      shiftPosition: currentShiftPosition,
      positionChangeDetails: {
        from: sequenceTracker.lastShiftPosition,
        to: currentShiftPosition,
        previousCount: currentPositionCount,
        totalAssignments: totalPositionAssignments
      }
    };
  }

  /**
   * ✅ HOTEL STANDARD: Check if employee needs rest day (6 working days = 1 rest day)
   * @param {number} employeeId - Employee ID
   * @param {Object} workloadTracker - Workload tracker
   * @param {string} currentDate - Current date being processed
   * @returns {Object} Rest day requirement details
   */
  checkRestDayRequirement(employeeId, workloadTracker, currentDate) {
    const tracker = workloadTracker[employeeId];
    const sequenceTracker = tracker?.assignmentSequence;

    if (!sequenceTracker) {
      return { needsRest: false, reason: 'no_history' };
    }

    // Hotel standard: Maximum 6 consecutive working days
    if (sequenceTracker.workingDaysStreak >= 6) {
      return {
        needsRest: true,
        reason: 'max_consecutive_days',
        details: `Employee has worked ${sequenceTracker.workingDaysStreak} consecutive days`,
        priority: 'high'
      };
    }

    // Check monthly working days (hotel standard: 26-28 days per month)
    if (sequenceTracker.monthlyWorkingDays >= 28) {
      return {
        needsRest: true,
        reason: 'monthly_limit',
        details: `Employee has worked ${sequenceTracker.monthlyWorkingDays} days this month`,
        priority: 'medium'
      };
    }

    // Check if last rest was too long ago
    if (sequenceTracker.lastRestDate) {
      const daysSinceRest = moment(currentDate).diff(moment(sequenceTracker.lastRestDate), 'days');
      if (daysSinceRest >= 7) {
        return {
          needsRest: true,
          reason: 'overdue_rest',
          details: `${daysSinceRest} days since last rest`,
          priority: 'medium'
        };
      }
    }

    return { needsRest: false, reason: 'within_limits' };
  }

  /**
   * ✅ HELPER: Determine shift position based on start time (Morning=1, Afternoon=2, Night=3)
   * @param {Object} instance - Shift instance
   * @returns {number} Shift position
   */
  determineShiftPosition(instance) {
    if (!instance.rotaShift?.startTime) {
      return 1; // Default to first position
    }

    const startTime = instance.rotaShift.startTime;
    const hour = parseInt(startTime.split(':')[0]);

    // Hotel industry standard shift times
    if (hour >= 6 && hour < 14) {
      return 1; // Morning shift (6:00 AM - 2:00 PM)
    } else if (hour >= 14 && hour < 22) {
      return 2; // Afternoon/Evening shift (2:00 PM - 10:00 PM)
    } else {
      return 3; // Night shift (10:00 PM - 6:00 AM)
    }
  }

  /**
   * ✅ CRITICAL: Employee Assignment Tracking System - Maintains 15-day consistency
   * @param {Object} workloadTracker - Workload tracker
   * @param {Array} instances - All instances for the schedule
   * @param {string} currentDate - Current date being processed
   * @returns {Object} Assignment recommendations
   */
  calculateEmployeeAssignmentPriority(workloadTracker, instances, currentDate) {
    console.log(`🎯 Calculating employee assignment priorities for ${currentDate}`);

    const assignmentPriorities = {};
    const currentDateInstances = instances.filter(inst => inst.date === currentDate);

    // ✅ ENHANCED: Sort instances by start time - Morning → Afternoon → Night
    const sortedInstances = currentDateInstances.sort((a, b) => {
      // Convert time to minutes for proper sorting
      const getMinutes = (timeStr) => {
        const [hours, minutes] = (timeStr || '00:00').split(':').map(Number);
        return hours * 60 + minutes;
      };

      const timeA = a.rotaShift?.startTime || '00:00';
      const timeB = b.rotaShift?.startTime || '00:00';
      return getMinutes(timeA) - getMinutes(timeB);
    });

    console.log(`📊 Processing ${sortedInstances.length} instances for ${currentDate}: ${sortedInstances.map(i => `${i.rotaShift?.name}(${i.rotaShift?.startTime})`).join(' → ')}`);

    // For each instance, calculate employee priorities
    sortedInstances.forEach((instance, index) => {
      const shiftType = instance.rotaShift?.name;
      const shiftPosition = index + 1; // Morning=1, Afternoon=2, Night=3

      assignmentPriorities[instance.id] = {
        instance,
        shiftType,
        shiftPosition,
        employeePriorities: []
      };

      // Calculate priority for each employee
      Object.keys(workloadTracker).forEach(employeeId => {
        const tracker = workloadTracker[employeeId];
        const priority = this.calculateEmployeePriorityForShift(employeeId, instance, tracker, currentDate);

        assignmentPriorities[instance.id].employeePriorities.push({
          employeeId: parseInt(employeeId),
          priority: priority.score,
          reason: priority.reason,
          details: priority.details,
          shouldAssign: priority.shouldAssign
        });
      });

      // Sort employees by priority (highest first)
      assignmentPriorities[instance.id].employeePriorities.sort((a, b) => b.priority - a.priority);

      console.log(`📋 Instance ${instance.id} (${shiftType}): Top 3 employee priorities: ${assignmentPriorities[instance.id].employeePriorities.slice(0, 3).map(ep => `${ep.employeeId}(${ep.priority})`).join(', ')}`);
    });

    return assignmentPriorities;
  }

  /**
   * ✅ CRITICAL: Calculate individual employee priority for specific shift
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Shift instance
   * @param {Object} tracker - Employee tracker
   * @param {string} currentDate - Current date
   * @returns {Object} Priority calculation result
   */
  calculateEmployeePriorityForShift(employeeId, instance, tracker, currentDate) {
    const currentShiftType = instance.rotaShift?.name;
    const currentDate_moment = moment(currentDate);
    const lastAssignmentDate = tracker.lastAssignmentDate ? moment(tracker.lastAssignmentDate) : null;
    const daysSinceLastAssignment = lastAssignmentDate ? currentDate_moment.diff(lastAssignmentDate, 'days') : 999;

    let score = 0;
    let reason = 'default';
    let details = '';
    let shouldAssign = true;

    // ✅ ENHANCED: Log employee priority calculation details with more context
    console.log(`🔍 Employee ${employeeId} priority for ${currentShiftType}:`);
    console.log(`   - Last assigned shift: ${tracker.lastAssignedShiftType || 'None'}`);
    console.log(`   - Last assignment date: ${tracker.lastAssignmentDate || 'None'}`);
    console.log(`   - Days since last assignment: ${daysSinceLastAssignment}`);
    console.log(`   - Current date: ${currentDate}`);
    console.log(`   - Preferred shift type: ${tracker.preferredShiftType || 'None'}`);
    console.log(`   - Assignment history: ${JSON.stringify(tracker.shiftTypeHistory || {})}`);

    // ✅ CRITICAL FIX: PRIORITY 1: Employee continuing same shift type (HIGHEST PRIORITY)
    // This is the most important priority - ensure same employee continues after gaps
    if (tracker.lastAssignedShiftType === currentShiftType) {
      // ✅ ENHANCED: Boost priority for first assignment after template gap
      if (daysSinceLastAssignment <= 1) {
        // Consecutive day assignment - perfect continuity
        score = 1000;
        reason = 'consecutive_same_shift';
        details = `Continuing ${currentShiftType} from previous day`;
      } else if (daysSinceLastAssignment <= 3) {
        // ✅ CRITICAL FIX: Week-off के बाद same employee को HIGHEST priority
        // This ensures employee continues after first template gap
        score = 1100; // ✅ BOOSTED from 950 to 1100 to ensure highest priority
        reason = 'resuming_after_weekoff';
        details = `Resuming ${currentShiftType} after ${daysSinceLastAssignment} days (likely week-off)`;
      } else if (daysSinceLastAssignment <= 7) {
        // Resuming after short gap (weekend/few days)
        score = 950; // ✅ BOOSTED from 900 to 950
        reason = 'resuming_same_shift';
        details = `Resuming ${currentShiftType} after ${daysSinceLastAssignment} days`;
      } else {
        // Resuming after longer gap
        score = 850; // ✅ BOOSTED from 800 to 850
        reason = 'resuming_preferred_shift';
        details = `Resuming preferred ${currentShiftType} after ${daysSinceLastAssignment} days`;
      }

      console.log(`   ✅ MATCH: Same shift type! Score: ${score} (${reason})`);
    }
    // ✅ ENHANCED: Add special case for preferred shift type even if not last assigned
    else if (tracker.preferredShiftType === currentShiftType && tracker.lastAssignedShiftType) {
      score = 800;
      reason = 'preferred_shift_type';
      details = `Assigning to preferred shift type ${currentShiftType} (most frequent historically)`;
      console.log(`   ✅ PREFERRED: Matching preferred shift type! Score: ${score} (${reason})`);
    }

    // ✅ PRIORITY 2: Employee with no recent assignments (NEW EMPLOYEE)
    else if (!tracker.lastAssignedShiftType) {
      score = 700;
      reason = 'new_employee';
      details = 'First assignment for employee';
      console.log(`   ✅ NEW: First assignment! Score: ${score} (${reason})`);
    }

    // ✅ PRIORITY 3: Employee changing shift type
    else {
      console.log(`   🔄 DIFFERENT SHIFT: Last was ${tracker.lastAssignedShiftType}, current is ${currentShiftType}`);

      // Check if 15-day minimum is completed
      if (daysSinceLastAssignment < 15) {
        // 15-day minimum not completed - heavily discourage
        score = 10;
        reason = '15day_violation';
        details = `Only ${daysSinceLastAssignment} days since last assignment (minimum: 15)`;
        shouldAssign = false;
        console.log(`   ❌ 15-DAY VIOLATION: Score: ${score} (${reason})`);
      } else {
        // 15-day minimum completed - allow change but lower priority
        score = 300;
        reason = 'legitimate_shift_change';
        details = `Changing from ${tracker.lastAssignedShiftType} to ${currentShiftType} after ${daysSinceLastAssignment} days`;
        console.log(`   ✅ LEGITIMATE CHANGE: Score: ${score} (${reason})`);
      }
    }

    // ✅ BOOST: Check for preserved preferences after gaps
    if (tracker.gapDays && tracker.gapDays.length > 0) {
      const recentGap = tracker.gapDays
        .filter(gap => moment(gap.date).isBefore(currentDate_moment))
        .sort((a, b) => moment(b.date).diff(moment(a.date)))
        [0];

      if (recentGap && recentGap.preservedShiftType === currentShiftType) {
        score += 200; // Boost for preserved preference
        details += ` (preserved after gap)`;
        console.log(`   🎯 GAP BOOST: +200 points for preserved preference`);
      }
    }

    console.log(`   📊 FINAL: Employee ${employeeId} score for ${currentShiftType}: ${score} (${reason})`);
    console.log(`   📝 Details: ${details}`);

    return { score, reason, details, shouldAssign };
  }

  /**
   * ✅ FIXED: Periodic workload rebalancing for long-term schedules
   * @param {Object} workloadTracker - Workload tracker
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async rebalanceWorkloadTracker(workloadTracker, tenantContext, transaction) {
    console.log('🔄 Starting periodic workload rebalancing...');

    const employees = Object.keys(workloadTracker);
    const totalShifts = employees.reduce((sum, empId) => sum + workloadTracker[empId].totalShifts, 0);
    const averageShifts = totalShifts / employees.length;

    console.log(`📊 Rebalancing stats: ${employees.length} employees, ${totalShifts} total shifts, ${averageShifts.toFixed(1)} average`);

    // Identify overworked and underworked employees
    const overworked = [];
    const underworked = [];

    employees.forEach(empId => {
      const tracker = workloadTracker[empId];
      const deviation = tracker.totalShifts - averageShifts;

      if (deviation > 2) { // More than 2 shifts above average
        overworked.push({ employeeId: empId, deviation, tracker });
      } else if (deviation < -2) { // More than 2 shifts below average
        underworked.push({ employeeId: empId, deviation, tracker });
      }
    });

    console.log(`📈 Overworked employees: ${overworked.length}, Underworked: ${underworked.length}`);

    // Adjust workload scores for future assignments
    overworked.forEach(emp => {
      workloadTracker[emp.employeeId].balanceScore = Math.max(
        workloadTracker[emp.employeeId].balanceScore - 10, 0
      );
      console.log(`📉 Reduced balance score for overworked employee ${emp.employeeId}`);
    });

    underworked.forEach(emp => {
      workloadTracker[emp.employeeId].balanceScore = Math.min(
        workloadTracker[emp.employeeId].balanceScore + 10, 100
      );
      console.log(`📈 Increased balance score for underworked employee ${emp.employeeId}`);
    });

    // ✅ ENHANCED: More aggressive constraint reset for long-term schedules
    employees.forEach(empId => {
      try {
        const tracker = workloadTracker[empId];
        if (!tracker) {
          console.warn(`⚠️ No tracker found for employee ${empId} during rebalancing`);
          return;
        }

        // ✅ SAFE ACCESS: Ensure properties exist
        const rotationFrequency = tracker.rotationFrequency || 0;
        const totalShifts = tracker.totalShifts || 0;
        const consecutiveDays = tracker.consecutiveDays || 0;

        const rotationRatio = rotationFrequency / Math.max(totalShifts, 1);

        // Reset rotation frequency if too high
        if (rotationRatio > 0.6) { // More than 60% rotations
          tracker.rotationFrequency = Math.floor(rotationFrequency * 0.8); // Reduce by 20%
          console.log(`🔄 Reduced rotation frequency for employee ${empId} (was ${rotationRatio.toFixed(2)})`);
        }

        // ✅ AGGRESSIVE RESET: Reset consecutive days if over 5 days
        if (consecutiveDays > 5) {
          console.log(`🔄 Resetting consecutive days for employee ${empId} (was ${consecutiveDays})`);
          tracker.consecutiveDays = Math.max(consecutiveDays - 3, 1); // Reduce by 3, minimum 1
        }

        // ✅ WORKLOAD RESET: If severely overworked, reduce total shifts count
        if (totalShifts > averageShifts + 5) {
          console.log(`📉 Reducing total shifts count for severely overworked employee ${empId}`);
          tracker.totalShifts = Math.max(totalShifts - 2, averageShifts); // Reduce by 2
        }
      } catch (error) {
        console.error(`❌ Error during rebalancing for employee ${empId}:`, error.message);
        // Continue with next employee
      }
    });

    console.log('✅ Periodic workload rebalancing completed');
  }

  /**
   * Initialize workload tracker
   * @param {Array} employees - Employees
   * @returns {Object} Workload tracker
   */
  initializeWorkloadTracker(employees) {
    const tracker = {};
    
    employees.forEach(employee => {
      tracker[employee.id] = {
        totalHours: 0,
        consecutiveDays: 0,
        lastShiftDate: null,
        assignments: []
      };
    });

    return tracker;
  }

  /**
   * Sort employees by priority
   * @param {Array} employees - Eligible employees
   * @param {Array} preferredEmployees - Preferred employee IDs
   * @param {boolean} balanceWorkload - Balance workload
   * @param {Object} workloadTracker - Workload tracker
   * @returns {Array} Sorted employees
   */
  sortEmployeesByPriority(employees, preferredEmployees, balanceWorkload, workloadTracker) {
    return employees.sort((a, b) => {
      // 1. Preferred employees first
      const aPreferred = preferredEmployees.includes(a.id);
      const bPreferred = preferredEmployees.includes(b.id);
      if (aPreferred !== bPreferred) {
        return bPreferred - aPreferred;
      }

      // 2. Balance workload (less worked employees first)
      if (balanceWorkload) {
        const aWorkload = workloadTracker[a.id]?.totalHours || 0;
        const bWorkload = workloadTracker[b.id]?.totalHours || 0;
        if (aWorkload !== bWorkload) {
          return aWorkload - bWorkload;
        }
      }

      // 3. Employee priority
      const aPriority = a.priority || 0;
      const bPriority = b.priority || 0;
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      // 4. Random for fairness
      return Math.random() - 0.5;
    });
  }

  /**
   * Update workload tracker
   * @param {Object} workloadTracker - Workload tracker
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Instance
   */
  updateWorkloadTrackerold(workloadTracker, employeeId, instance) {
    if (!workloadTracker[employeeId]) return;

    const shiftDuration = this.calculateShiftDuration(
      instance.rotaShift?.startTime,
      instance.rotaShift?.endTime
    );

    workloadTracker[employeeId].totalHours += shiftDuration;
    workloadTracker[employeeId].assignments.push(instance);
    
    // Update consecutive days
    const lastDate = workloadTracker[employeeId].lastShiftDate;
    if (lastDate && moment(instance.date).diff(moment(lastDate), 'days') === 1) {
      workloadTracker[employeeId].consecutiveDays++;
    } else {
      workloadTracker[employeeId].consecutiveDays = 1;
    }
    
    workloadTracker[employeeId].lastShiftDate = instance.date;
  }

  /**
   * Check employee availability for instance
   * @param {Object} employee - Employee
   * @param {Object} instance - Instance
   * @param {Object} transaction - Transaction
   * @returns {boolean} Is available
   */
  async checkEmployeeAvailabilityForInstance(employee, instance, transaction) {
    // Check base availability
    if (employee.availability) {
      const dayOfWeek = moment(instance.date).format('dddd').toLowerCase();
      const dayStart = employee.availability[`${dayOfWeek}Start`];
      const dayEnd = employee.availability[`${dayOfWeek}End`];
      
      if (!dayStart || !dayEnd) return false;
      
      if (instance.rotaShift?.startTime < dayStart || instance.rotaShift?.endTime > dayEnd) {
        return false;
      }
    }

    // Check overrides
    const override = await AvailabilityOverride.findOne({
      where: {
        employeeId: employee.id,
        isActive: true,
        startDate: { [Op.lte]: instance.date },
        endDate: { [Op.gte]: instance.date },
        type: 'unavailable'
      },
      transaction
    });

    return !override;
  }

  /**
   * Check workload constraints
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Instance
   * @param {Object} workloadTracker - Workload tracker
   * @param {Object} constraints - Constraints
   * @returns {boolean} Meets constraints
   */
  checkWorkloadConstraints(employeeId, instance, workloadTracker, constraints) {
    const tracker = workloadTracker[employeeId];
    if (!tracker) return true;

    // Check consecutive days
    if (constraints.maxConsecutiveDays && tracker.consecutiveDays >= constraints.maxConsecutiveDays) {
      return false;
    }

    // Check weekly hours (simplified)
    const weeklyLimit = 40; // Could be from employee consents
    if (tracker.totalHours >= weeklyLimit) {
      return false;
    }

    return true;
  }

  /**
   * Check assignment conflicts
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Instance
   * @param {Object} transaction - Transaction
   * @returns {boolean} Has conflict
   */
  async checkAssignmentConflicts(employeeId, instance, transaction) {
    const existingAssignment = await ShiftAssignment.count({
      where: {
        employeeId,
        status: { [Op.notIn]: ['cancelled', 'absent'] }
      },
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: { date: instance.date }
        }
      ],
      transaction
    });

    return existingAssignment > 0;
  }

  /**
   * Calculate shift duration
   * @param {string} startTime - Start time
   * @param {string} endTime - End time
   * @returns {number} Duration in hours
   */
  calculateShiftDuration(startTime, endTime) {
    if (!startTime || !endTime) return 8; // Default 8 hours
    
    const start = moment(startTime, 'HH:mm');
    const end = moment(endTime, 'HH:mm');
    
    if (end.isBefore(start)) {
      end.add(1, 'day');
    }
    
    return end.diff(start, 'hours', true);
  }

  /**
   * Calculate coverage percentage
   * @param {Array} instances - Generated instances
   * @param {Array} assignments - Generated assignments
   * @returns {number} Coverage percentage
   */
  calculateCoverage(instances, assignments) {
    if (instances.length === 0) return 0;

    const totalRequired = instances.reduce((sum, instance) => 
      sum + (instance.actualRequiredCount || instance.rotaShift?.baseRequiredCount || 1), 0
    );

    const totalAssigned = assignments.length;

    return totalRequired > 0 ? Math.round((totalAssigned / totalRequired) * 100) : 0;
  }

  /**
   * Generate instances with demand forecasting integration
   * Flow: For each template × each date, create RotaShiftInstance
   * Uses demand forecast if available, otherwise falls back to template baseRequiredCount
   * @param {Object} params - Generation parameters
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Generation results
   */
  async generateInstancesWithDemandForecasting(params, tenantContext, transaction) {
    const {
      scheduleId,
      templates,
      startDate,
      endDate,
      enableForecastOverride,
      minConfidenceThreshold = 70
    } = params;

    const results = {
      successful: 0,
      failed: 0,
      instances: [],
      errors: [],
      forecastsUsed: 0,
      templatesUsed: 0,
      // ✅ ENHANCED: Detailed conflict tracking
      conflicts: {
        total: 0,
        resolved: 0,
        pending: 0,
        byType: {
          employee_pool_exhaustion: 0,
          constraint_violation: 0,
          already_assigned: 0,
          availability_conflict: 0,
          designation_mismatch: 0,
          time_overlap: 0,
          workload_limit: 0
        },
        details: [], // Store detailed conflict information
        byDate: {}, // Conflicts grouped by date
        byDesignation: {}, // Conflicts grouped by designation
        recommendations: [] // Actionable recommendations
      },
      summary: {
        totalDays: 0,
        totalTemplates: templates.length,
        totalInstances: 0
      }
    };

    try {
      console.log(`🔄 Generating instances for ${templates.length} templates from ${startDate} to ${endDate}`);

      // Get demand forecasts for the date range if enabled
      let forecastLookup = {};
      if (enableForecastOverride) {
        console.log('🔍 Loading demand forecasts...');

        // Get all designations from templates for forecasting lookup
        const designationIds = [...new Set(
          templates.flatMap(t =>
            t.designationRequirements?.map(dr => dr.designationId) || []
          ).filter(Boolean)
        )];

        const forecasts = await DemandForecast.findAll({
          where: {
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId,
            date: {
              [Op.between]: [startDate, endDate]
            },
            status: 'active',
            confidence: {
              [Op.gte]: minConfidenceThreshold
            },
            ...(designationIds.length > 0 && { designationId: { [Op.in]: designationIds } })
          },
          include: [
            {
              model: Department,
              as: 'department',
              attributes: ['id', 'name']
            },
            {
              model: Designation,
              as: 'designation',
              attributes: ['id', 'name']
            }
          ]
        });

        // Create lookup map: date_departmentId_designationId -> forecast
        forecasts.forEach(forecast => {
          const key = `${forecast.date}_${forecast.departmentId}_${forecast.designationId}`;
          forecastLookup[key] = forecast;
        });

        console.log(`📊 Found ${forecasts.length} demand forecasts for date range`);
      }

      // Generate instances for each template × each date
      const currentDate = moment(startDate);
      const endMoment = moment(endDate);
      let totalDays = 0;

      while (currentDate.isSameOrBefore(endMoment)) {
        const dateStr = currentDate.format('YYYY-MM-DD');
        const dayName = currentDate.format('dddd').toLowerCase();
        totalDays++;

        console.log(`📅 Processing ${dateStr} (${dayName}) for ${templates.length} templates`);

        // ✅ ENHANCED: Log template processing order for shift sequence verification
        const templateOrder = templates.map(t => `${t.name}(${t.startTime})`).join(' → ');
        console.log(`🔄 Template processing order: ${templateOrder}`);

        // ✅ FIXED: Track template gaps to prevent incorrect reassignments
        let isTemplateGap = false;

        // Step 1: Check if it's weekend (Saturday/Sunday)
        if (dayName === 'saturday' || dayName === 'sunday') {
          console.log(`⏭️ Skipping ${dateStr} (${dayName}) - Weekend`);
          isTemplateGap = true;
          currentDate.add(1, 'day');
          continue;
        }

        // Step 2: Check if date is holiday
        const isHoliday = await this.checkIfHoliday(dateStr, tenantContext, transaction);
        if (isHoliday.isHoliday) {
          console.log(`🏖️ Skipping ${dateStr} (${dayName}) - Holiday: ${isHoliday.holidayName}`);
          isTemplateGap = true;
          currentDate.add(1, 'day');
          continue;
        }

        // ✅ ENHANCED: Mark previous day as template gap for workload tracking
        if (isTemplateGap) {
          this.markTemplateGap(dateStr, tenantContext);
        }

        for (const template of templates) {
          try {
            // Check if instance already exists
            const existingInstance = await RotaShiftInstance.findOne({
              where: {
                scheduleId,
                rotaShiftId: template.id,
                date: dateStr
              },
              transaction
            });

            if (existingInstance) {
              results.failed++;
              results.errors.push({
                template: template.name,
                date: dateStr,
                error: 'Instance already exists'
              });
              continue;
            }

            // Determine required count (forecast vs template)
            let requiredCount = template.baseRequiredCount || 1;
            let usedForecast = false;
            let forecastNotes = '';

            if (enableForecastOverride && template.designationRequirements?.length > 0) {
              // For templates with multiple designations, sum up forecasts or use template counts
              let totalForecastCount = 0;
              let forecastsFound = 0;

              for (const designationReq of template.designationRequirements) {
                const forecastKey = `${dateStr}_${template.departmentId}_${designationReq.designationId}`;
                const forecast = forecastLookup[forecastKey];

                if (forecast) {
                  totalForecastCount += forecast.requiredCount;
                  forecastsFound++;
                } else {
                  totalForecastCount += designationReq.requiredCount || 1;
                }
              }

              if (forecastsFound > 0) {
                requiredCount = totalForecastCount;
                usedForecast = true;
                forecastNotes = `Using ${forecastsFound}/${template.designationRequirements.length} demand forecasts`;
                results.forecastsUsed++;
              } else {
                forecastNotes = 'No forecasts available, using template counts';
                results.templatesUsed++;
              }
            } else {
              results.templatesUsed++;
            }

            // Create instance (without JSON fields)
            const instance = await RotaShiftInstance.create({
              scheduleId,
              rotaShiftId: template.id,
              date: dateStr,
              actualRequiredCount: requiredCount,
              totalRequired: requiredCount,
              totalAssigned: 0,
              status: 'open',
              sourceType: 'rotaShift',
              sourceId: template.id,
              notes: forecastNotes,
              createdById: tenantContext.userId
            }, { transaction });

            // Create designation requirements in junction table
            if (template.designationRequirements && template.designationRequirements.length > 0) {
              let finalRequirements = template.designationRequirements;
              let requirementType = 'base';
              let sourceType = 'rotaShift';

              // If forecast was used, modify requirements and use custom type
              if (usedForecast) {
                finalRequirements = template.designationRequirements.map(req => {
                  const forecastKey = `${dateStr}_${template.departmentId}_${req.designationId}`;
                  const forecast = forecastLookup[forecastKey];
                  return {
                    designationId: req.designationId,
                    requiredCount: forecast ? forecast.requiredCount : req.requiredCount,
                    assignedCount: 0,
                    priority: req.priority || 0
                  };
                });
                requirementType = 'custom';
                sourceType = 'forecast';
              }

              await this.createInstanceDesignationRequirements(
                instance.id,
                finalRequirements,
                requirementType,
                sourceType,
                tenantContext.userId,
                transaction
              );
            }

            // Load instance with associations for assignment logic
            const instanceWithAssociations = await RotaShiftInstance.findByPk(instance.id, {
              include: [
                {
                  model: RotaShift,
                  as: 'rotaShift',
                  include: [
                    {
                      model: Designation,
                      as: 'designation',
                      attributes: ['id', 'name'],
                      through: { attributes: ['requiredCount'] },
                      required: false
                    }
                  ]
                }
              ],
              transaction
            });

            results.instances.push(instanceWithAssociations);
            results.successful++;

            console.log(`✅ Created instance: ${template.name} on ${dateStr} (${requiredCount} required, ${usedForecast ? 'forecast' : 'template'})`);

          } catch (error) {
            results.failed++;
            results.errors.push({
              template: template.name,
              date: dateStr,
              error: error.message
            });
            console.error(`❌ Failed to create instance for ${template.name} on ${dateStr}:`, error.message);
          }
        }

        currentDate.add(1, 'day');
      }

      results.summary = {
        totalDays,
        totalTemplates: templates.length,
        totalInstances: results.successful,
        forecastsUsed: results.forecastsUsed,
        templatesUsed: results.templatesUsed
      };

      console.log(`🎉 Instance generation completed: ${results.successful} created, ${results.failed} failed`);
      return results;

    } catch (error) {
      console.error('❌ Error generating instances with demand forecasting:', error);
      throw new ValidationError(`Failed to generate instances: ${error.message}`);
    }
  }

  /**
   * Generate instances from ShiftTemplate (Template-based mode)
   * @param {Object} templateRequest - Template generation request
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Generation results
   */
  async generateInstancesFromTemplate(templateRequest, tenantContext, transaction) {
    const {
      scheduleId,
      shiftTemplateId,
      startDate,
      endDate,
      constraints = {},
      demandForecast = [],
      enableForecastOverride = false
    } = templateRequest;

    console.log(`🎯 Generating instances from template ${shiftTemplateId} for schedule ${scheduleId}`);

    try {
      // Get ShiftTemplate with normalized day configurations
      const template = await ShiftTemplate.findOne({
        where: {
          id: shiftTemplateId,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId,
          isActive: true
        },
        include: [
          {
            model: ShiftTemplateDayConfig,
            as: 'dayConfigs',
            where: { isActive: true },
            required: false,
            include: [
              {
                model: ShiftTemplateDayShift,
                as: 'dayShifts',
                where: { isActive: true },
                required: false,
                // ✅ CRITICAL: Order day shifts by start time for consistent sequence
                order: [['startTime', 'ASC']],
                include: [
                  {
                    model: ShiftTemplateDayShiftDesignation,
                    as: 'designationRequirements',
                    where: { isActive: true },
                    required: false
                  }
                ]
              }
            ]
          }
        ],
        transaction
      });

      if (!template) {
        throw new NotFoundError('Shift template not found or inactive');
      }

      console.log(`🔍 Template ${shiftTemplateId} loaded:`, {
        id: template.id,
        name: template.name,
        dayConfigsCount: template.dayConfigs?.length || 0,
        dayConfigs: template.dayConfigs?.map(dc => ({
          dayOfWeek: dc.dayOfWeek,
          isWorkingDay: dc.isWorkingDay,
          dayShiftsCount: dc.dayShifts?.length || 0
        })) || []
      });

      const results = {
        successful: 0,
        failed: 0,
        instances: [],
        errors: [],
        forecastsUsed: 0,
        templatesUsed: 0
      };

      // Parse day configurations from normalized template structure
      const dayConfigsMap = {};
      if (template.dayConfigs) {
        for (const dayConfig of template.dayConfigs) {
          console.log(`📋 Processing day config: ${dayConfig.dayOfWeek}, isWorkingDay: ${dayConfig.isWorkingDay}, dayShifts: ${dayConfig.dayShifts?.length || 0}`);
          dayConfigsMap[dayConfig.dayOfWeek] = dayConfig;
        }
      }
      console.log(`📋 Template has configurations for: ${Object.keys(dayConfigsMap).join(', ')}`);

      // Generate instances for each day in the date range
      const currentDate = moment(startDate);
      const endMoment = moment(endDate);
      let totalDays = 0;

      while (currentDate.isSameOrBefore(endMoment)) {
        totalDays++;
        const dateStr = currentDate.format('YYYY-MM-DD');
        const dayName = currentDate.format('dddd').toLowerCase();

        console.log(`📅 Processing ${dateStr} (${dayName})`);

        // Step 1: Check if it's weekend (Saturday/Sunday)
        if (dayName === 'saturday' || dayName === 'sunday') {
          console.log(`⏭️ Skipping ${dateStr} (${dayName}) - Weekend`);
          currentDate.add(1, 'day'); //check from business unit or department
          continue;
        }

        // Step 2: Get day configuration from normalized template
        const dayConfig = dayConfigsMap[dayName];
        if (!dayConfig || !dayConfig.isWorkingDay || !dayConfig.dayShifts || dayConfig.dayShifts.length === 0) {
          console.log(`⏭️ Skipping ${dateStr} (${dayName}) - No template configuration`);
          currentDate.add(1, 'day');
          continue;
        }

        // Step 3: Check if date is holiday
        const isHoliday = await this.checkIfHoliday(dateStr, tenantContext, transaction);
        if (isHoliday.isHoliday) {
          console.log(`🏖️ Skipping ${dateStr} (${dayName}) - Holiday: ${isHoliday.holidayName}`);
          currentDate.add(1, 'day');
          continue;
        }

        // ✅ CRITICAL FIX: Sort dayShifts by start time - Morning → Afternoon → Night
        const sortedDayShifts = [...(dayConfig.dayShifts || [])].sort((a, b) => {
          // Convert time to minutes for proper sorting
          const getMinutes = (timeStr) => {
            const [hours, minutes] = (timeStr || '00:00').split(':').map(Number);
            return hours * 60 + minutes;
          };

          const timeA = a.startTime || '00:00';
          const timeB = b.startTime || '00:00';
          return getMinutes(timeA) - getMinutes(timeB);
        });

        console.log(`📊 Processing ${sortedDayShifts.length} shifts for ${dateStr} in CONSISTENT chronological order: ${sortedDayShifts.map(s => `${s.name || 'Unnamed'}(${s.startTime})`).join(' → ')}`);

        // ✅ ENHANCED: Process each shift in CONSISTENT chronological order (Morning → Afternoon → Night)
        // This ensures that even after template gaps, the sequence remains consistent
        for (let shiftIndex = 0; shiftIndex < sortedDayShifts.length; shiftIndex++) {
          const dayShift = sortedDayShifts[shiftIndex];
          const shiftSequencePosition = shiftIndex + 1; // 1=Morning, 2=Afternoon, 3=Night
          // Extract shift data outside try block for error handling access
          const {
            rotaShiftId,
            priority = 1,
            isFlexible = false,
            useDesignationOverride = false
          } = dayShift;

          try {

            // Get designation overrides from normalized structure
            const designationOverrides = dayShift.designationRequirements ?
              dayShift.designationRequirements.map(req => ({
                designationId: req.designationId,
                requiredCount: req.requiredCount
              })) : [];

            // Check if there's demand forecast override for this date/shift
            let finalRequirements = [];
            let forecastUsed = false;

            if (enableForecastOverride) {
              const forecast = demandForecast.find(f =>
                f.date === dateStr &&
                (f.rotaShiftId === rotaShiftId || !f.rotaShiftId)
              );

              if (forecast && forecast.designationOverrides) {
                finalRequirements = forecast.designationOverrides;
                forecastUsed = true;
                results.forecastsUsed++;
                console.log(`📊 Using forecast data for ${dateStr} - ${rotaShiftId}`);
              }
            }

            // Fallback to template designation overrides
            if (!forecastUsed && useDesignationOverride && designationOverrides.length > 0) {
              finalRequirements = designationOverrides;
              results.templatesUsed++;
              console.log(`📋 Using template designation overrides for ${dateStr} - ${rotaShiftId}`);
            }

            // If still no requirements, get default from RotaShift
            if (!forecastUsed && finalRequirements.length === 0) {
              const rotaShift = await RotaShift.findByPk(rotaShiftId, {
                include: [{
                  model: RotaShiftDesignationRequirement,
                  as: 'designationRequirements'
                }],
                transaction
              });

              if (rotaShift && rotaShift.designationRequirements && rotaShift.designationRequirements.length > 0) {
                finalRequirements = rotaShift.designationRequirements.map(req => ({
                  designationId: req.designationId,
                  requiredCount: req.requiredCount,
                  assignedCount: 0,
                  priority: req.priority || 0
                }));
                results.templatesUsed++;
                console.log(`📋 Using RotaShift default designation requirements for ${dateStr} - ${rotaShiftId}`);
              } else {
                console.log(`⚠️ No designation requirements found for RotaShift ${rotaShiftId}, using fallback`);
                // Get any available designation from the company as fallback
                const fallbackDesignation = await Designation.findOne({
                  where: {
                    companyId: tenantContext.companyId,
                    businessUnitId: tenantContext.businessUnitId,
                    status: 'active'
                  },
                  order: [['id', 'ASC']], // Get the first available designation
                  transaction
                });

                if (fallbackDesignation) {
                  finalRequirements = [{
                    designationId: fallbackDesignation.id,
                    requiredCount: 1,
                    assignedCount: 0,
                    priority: 0
                  }];
                  console.log(`📋 Using fallback designation: ${fallbackDesignation.name} (ID: ${fallbackDesignation.id})`);
                } else {
                  console.log(`❌ No designations found for fallback, skipping instance creation`);
                  currentDate.add(1, 'day');
                  continue; // Skip this instance creation
                }
              }
            }

            // Create shift instance (without JSON fields)
            const totalRequired = finalRequirements.reduce((sum, req) => sum + req.requiredCount, 0) || 1;
            const instanceData = {
              scheduleId,
              rotaShiftId,
              date: dateStr,
              totalRequired,
              totalAssigned: 0,
              status: 'open',
              priority,
              isFlexible,
              sourceType: 'template',
              sourceId: shiftTemplateId,
              createdById: tenantContext.userId
            };

            console.log(`📊 Creating instance for ${dateStr} with ${finalRequirements.length} designation requirements, total required: ${totalRequired}`);

            // ✅ ENHANCED: Log shift instance creation with CONSISTENT sequence information
            console.log(`🔄 Creating shift instance: ${dayShift.name || 'Unnamed'} (${dayShift.startTime}) for ${dateStr} - CONSISTENT Sequence position: ${shiftSequencePosition}/${sortedDayShifts.length}`);
            console.log(`   📌 Shift sequence maintained: Position ${shiftSequencePosition} ensures consistent ordering even after template gaps`);

            const instance = await RotaShiftInstance.create(instanceData, { transaction });

            // Create designation requirements in junction table (always create)
            if (finalRequirements && finalRequirements.length > 0) {
              // Determine requirement type based on forecast usage
              const requirementType = forecastUsed ? 'custom' : 'base';
              const sourceType = forecastUsed ? 'forecast' : 'template';

              await this.createInstanceDesignationRequirements(
                instance.id,
                finalRequirements,
                requirementType, // Use appropriate type
                sourceType, // Use appropriate source
                tenantContext.userId,
                transaction
              );

              console.log(`✅ Created ${finalRequirements.length} designation requirements in junction table for instance ${instance.id}`);
            } else {
              console.log(`⚠️ No designation requirements to create for instance ${instance.id}`);
            }

            results.instances.push(instance);
            results.successful++;

            console.log(`✅ Created instance ${instance.id} for ${dateStr} - shift ${rotaShiftId}`);

          } catch (error) {
            results.failed++;
            results.errors.push({
              date: dateStr,
              rotaShiftId: rotaShiftId,
              error: error.message
            });
            console.error(`❌ Failed to create instance for ${dateStr} - shift ${rotaShiftId}:`, error.message);
          }
        }

        currentDate.add(1, 'day');
      }

      results.summary = {
        totalDays,
        templateId: shiftTemplateId,
        templateName: template.name,
        totalInstances: results.successful,
        forecastsUsed: results.forecastsUsed,
        templatesUsed: results.templatesUsed
      };

      console.log(`🎉 Template-based instance generation completed: ${results.successful} created, ${results.failed} failed`);
      return results;

    } catch (error) {
      console.error('❌ Error generating instances from template:', error);
      throw new ValidationError(`Failed to generate instances from template: ${error.message}`);
    }
  }

  /**
   * Check if a date is a holiday
   * @param {string} dateStr - Date string (YYYY-MM-DD)
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Holiday check result
   */
  async checkIfHoliday(dateStr, tenantContext, transaction) {
    try {
      console.log(`🔍 Checking if ${dateStr} is a holiday`);

      // Check for holidays in the company/business unit
      const holiday = await Holiday.findOne({
        where: {
          date: dateStr,
          companyId: tenantContext.companyId,
          [Op.or]: [
            { businessUnitId: tenantContext.businessUnitId },
            { businessUnitId: null }, // Company-wide holidays
            { isCompanyWide: true }
          ]
        },
        transaction
      });

      if (holiday) {
        console.log(`🏖️ Found holiday: ${holiday.name} on ${dateStr}`);
        return {
          isHoliday: true,
          holidayName: holiday.name,
          holidayType: holiday.optionalHoliday ? 'optional' : 'mandatory',
          isRecurring: holiday.isRecurring,
          holiday: holiday
        };
      }

      console.log(`✅ ${dateStr} is not a holiday`);
      return {
        isHoliday: false,
        holidayName: null,
        holidayType: null,
        isRecurring: false,
        holiday: null
      };

    } catch (error) {
      console.error(`❌ Error checking holiday for ${dateStr}:`, error.message);
      // Don't throw error - assume not a holiday if check fails
      return {
        isHoliday: false,
        holidayName: null,
        holidayType: null,
        isRecurring: false,
        holiday: null,
        error: error.message
      };
    }
  }

  /**
   * ENHANCED: Assign employees based on designation requirements with tracking
   * @param {Array} instances - Shift instances
   * @param {Object} constraints - Assignment constraints
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Assignment results
   */
  async assignEmployeesByDesignation(instances, constraints, tenantContext, transaction) {
    const {
      assignmentStrategy = 'smart',
      respectAvailability = true,
      balanceWorkload = true,
      // ✅ ENHANCED: 15-day consistency alignment
      maxConsecutiveDays = 15, // ✅ INCREASED: Allow 15 consecutive days for shift consistency
      minRestHours = 12,
      minimizeOvertime = true,
      preferredEmployees = [],
      excludedEmployees = [],
      allowPartialAssignment = true,
      conflictResolution = 'auto',
      // ✅ NEW: Extended consistency constraints
      maxShiftsPerEmployee = 60, // ✅ INCREASED: Support 15-day periods (15 days × 4 periods = 60 shifts)
      maxOvertimeHours = 30, // ✅ INCREASED: Allow more overtime for extended periods
      minDaysBetweenRotations = 15, // ✅ ALIGNED: Minimum 15 days between rotations
      maxRotationFrequency = 0.3, // ✅ REDUCED: Lower rotation frequency for longer consistency
      enforceMinimumConsistencyDays = 15, // ✅ NEW: Minimum days before allowing shift type change
      consistencyPenaltyMultiplier = 10, // ✅ NEW: Heavy penalty for premature shift changes
      extendedConsistencyMode = true // ✅ NEW: Enable extended consistency features
    } = constraints;

    console.log(`🎯 Starting designation-based assignment with strategy: ${assignmentStrategy}`);

    // Generate unique session ID for tracking
    const sessionId = require('uuid').v4();
    console.log(`📋 Session ID: ${sessionId}`);

    // Create auto-schedule session record
    const session = await this.createAutoScheduleSession(
      sessionId,
      instances[0]?.scheduleId,
      constraints,
      tenantContext,
      transaction
    );

    const results = {
      sessionId,
      session,
      successful: 0,
      failed: 0,
      errors: [],
      assignments: [],
      byDesignation: {},
      byStrategy: {
        smart: 0,
        rotation: 0,
        balanced: 0,
        skill_based: 0
      },
      conflicts: {
        resolved: 0,
        pending: 0
      }
    };

    // Get all employees grouped by designation
    const employeesByDesignation = await this.getEmployeesGroupedByDesignation(tenantContext, excludedEmployees, transaction);

    // ✅ CRITICAL: Initialize enhanced workload tracker with existing assignments to preserve employee preferences
    const scheduleId = instances.length > 0 ? instances[0].scheduleId : null;
    const workloadTracker = await this.initializeEnhancedWorkloadTracker(
      Object.values(employeesByDesignation).flat(),
      tenantContext,
      transaction,
      scheduleId // ✅ CRITICAL: Pass scheduleId to load existing assignments
    );

    // ✅ ENHANCED: Initialize tracking tables with assignment priority system
    const dateAssignmentTracker = new Map(); // date -> Set(employeeIds)
    const instanceAssignmentTracker = new Map(); // instanceId -> Set(employeeIds)

    // ✅ CRITICAL: Calculate assignment priorities for all dates to maintain consistency
    const allDates = [...new Set(instances.map(inst => inst.date))].sort();
    const assignmentPriorityMap = {};

    console.log(`🎯 Pre-calculating assignment priorities for ${allDates.length} dates to ensure 15-day consistency`);

    allDates.forEach(date => {
      assignmentPriorityMap[date] = this.calculateEmployeeAssignmentPriority(workloadTracker, instances, date);
    });

    // ✅ ENHANCED: Group instances by date with multi-shift coverage guarantee
    const instancesByDate = {};
    instances.forEach(instance => {
      const dateKey = instance.date;
      if (!instancesByDate[dateKey]) {
        instancesByDate[dateKey] = [];
      }
      instancesByDate[dateKey].push(instance);
    });

    // ✅ CRITICAL: Sort instances within each date by start time - CONSISTENT Morning → Afternoon → Night
    Object.keys(instancesByDate).forEach(date => {
      instancesByDate[date].sort((a, b) => {
        const timeA = a.rotaShift?.startTime || '00:00';
        const timeB = b.rotaShift?.startTime || '00:00';

        // Convert time to minutes for proper sorting
        const getMinutes = (timeStr) => {
          const [hours, minutes] = timeStr.split(':').map(Number);
          return hours * 60 + minutes;
        };

        const minutesA = getMinutes(timeA);
        const minutesB = getMinutes(timeB);

        // ✅ ENHANCED: Add sequence position to ensure consistent ordering
        // Morning (06:00-12:00) = Position 1, Afternoon (12:00-18:00) = Position 2, Night (18:00-06:00) = Position 3
        a._sequencePosition = minutesA < 720 ? 1 : (minutesA < 1080 ? 2 : 3);
        b._sequencePosition = minutesB < 720 ? 1 : (minutesB < 1080 ? 2 : 3);

        return minutesA - minutesB;
      });

      console.log(`📊 ${date}: ${instancesByDate[date].length} shifts in CONSISTENT sequence: ${instancesByDate[date].map(i => `${i.rotaShift?.name}(${i.rotaShift?.startTime})[Pos:${i._sequencePosition}]`).join(' → ')}`);

      // ✅ ENHANCED: Verify sequence consistency
      const sequencePositions = instancesByDate[date].map(i => i._sequencePosition);
      const isSequenceConsistent = sequencePositions.every((pos, index) => index === 0 || pos >= sequencePositions[index - 1]);
      console.log(`   📌 Sequence consistency check: ${isSequenceConsistent ? '✅ CONSISTENT' : '❌ INCONSISTENT'} - Positions: [${sequencePositions.join(', ')}]`);
    });

    console.log(`📅 Processing ${Object.keys(instancesByDate).length} dates with ${instances.length} total instances`);

    // Process each date separately to ensure no employee is assigned to multiple shifts on same date
    for (const [date, dateInstances] of Object.entries(instancesByDate)) {
      console.log(`\n📅 Processing date: ${date} with ${dateInstances.length} instances`);

      // CRITICAL: Track already assigned employees for this DATE (across all shifts)
      const dateAssignedEmployees = new Set();

      // Sort instances within the date by priority (higher priority first)
      const sortedDateInstances = dateInstances.sort((a, b) => {
        const aPriority = (a.rotaShift?.priority || a.priority || 5);
        const bPriority = (b.rotaShift?.priority || b.priority || 5);
        return bPriority - aPriority;
      });

      console.log(`📊 Sorted ${sortedDateInstances.length} instances by priority for date ${date}`);

      // Process each instance for this date
      for (const instance of sortedDateInstances) {
        try {
          console.log(`\n🏗️ Processing instance ${instance.id} (priority: ${instance.rotaShift?.priority || instance.priority || 5}) for date ${instance.date}`);

          // Track already assigned employees for this INSTANCE (within the shift)
          const instanceAssignedEmployees = new Set();

          // STEP 1: Get designation requirements for this instance
          let designationRequirements = await this.getInstanceDesignationRequirements(instance, transaction);

          // STEP 2: If no instance-specific requirements, try to get from rotaShift
          if (!designationRequirements || designationRequirements.length === 0) {
            console.log(`⚠️ No instance designation requirements found for instance ${instance.id}, checking rotaShift...`);

            if (instance.rotaShift && instance.rotaShift.designationRequirements) {
              console.log(`🔄 Using rotaShift designation requirements as source`);
              designationRequirements = instance.rotaShift.designationRequirements.map(req => ({
                designationId: req.designationId,
                requiredCount: req.requiredCount,
                assignedCount: 0,
                priority: req.priority || 0,
                designation: req.designation
              }));
            } else {
              console.log(`❌ No designation requirements found in rotaShift either for instance ${instance.id}`);

              // STEP 3: Create instance designation requirements from rotaShift if available
              if (instance.rotaShiftId) {
                const rotaShift = await RotaShift.findByPk(instance.rotaShiftId, {
                  include: [{
                    model: RotaShiftDesignationRequirement,
                    as: 'designationRequirements',
                    include: [{ model: Designation, as: 'designation' }]
                  }],
                  transaction
                });

                if (rotaShift && rotaShift.designationRequirements && rotaShift.designationRequirements.length > 0) {
                  console.log(`🔧 Creating instance designation requirements from rotaShift ${rotaShift.id}`);

                  // Create instance designation requirements
                  await this.createInstanceDesignationRequirements(
                    instance.id,
                    rotaShift.designationRequirements,
                    'base',
                    'rotaShift',
                    tenantContext.userId,
                    transaction
                  );

                  // Now get the created requirements
                  designationRequirements = await this.getInstanceDesignationRequirements(instance, transaction);
                }
              }
            }
          }

          // STEP 4: If still no requirements, use fallback
          if (!designationRequirements || designationRequirements.length === 0) {
            console.log(`⚠️ Using fallback assignment for instance ${instance.id} - no designation requirements available`);
            await this.assignInstanceFallback(instance, employeesByDesignation, constraints, workloadTracker, results, tenantContext, transaction, instanceAssignedEmployees, dateAssignedEmployees);
            continue;
          }

          console.log(`📋 Processing ${designationRequirements.length} designation requirements for instance ${instance.id}`);

          // STEP 5: Process each designation requirement independently
          for (const designationReq of designationRequirements) {
            console.log(`\n🎯 Processing designation ${designationReq.designationId} requirement: ${designationReq.requiredCount} employees needed`);

            await this.processDesignationRequirement(
              instance,
              designationReq,
              employeesByDesignation,
              constraints,
              workloadTracker,
              results,
              tenantContext,
              transaction,
              instanceAssignedEmployees, // Track within instance (per designation)
              dateAssignedEmployees, // Track across date (across all shifts)
              instances // ✅ CRITICAL: Pass all instances for 15-day consistency tracking
            );
          }

          console.log(`✅ Completed processing instance ${instance.id}. Date assigned: ${dateAssignedEmployees.size}, Instance assigned: ${instanceAssignedEmployees.size}`);

        } catch (error) {
          console.error(`❌ Error processing instance ${instance.id}:`, error.message);
          results.errors.push(`Instance ${instance.id}: ${error.message}`);
          results.failed++;
        }
      }

      console.log(`📅 Completed date ${date}. Total employees assigned on this date: ${dateAssignedEmployees.size}`);
    }

    // Calculate coverage percentages
    for (const designationId in results.byDesignation) {
      const designation = results.byDesignation[designationId];
      designation.coverage = designation.required > 0
        ? Math.round((designation.assigned / designation.required) * 100)
        : 100;
    }

    // Update session with final results
    await this.updateAutoScheduleSession(sessionId, {
      status: results.failed > results.successful ? 'failed' : 'completed',
      successfulAssignments: results.successful,
      failedAssignments: results.failed,
      overallCoverage: this.calculateOverallCoverage(results),
      designationCoverage: results.byDesignation,
      results: results,
      errors: results.errors,
      endTime: new Date()
    }, transaction);

    console.log(`🎉 Designation-based assignment completed: ${results.successful} successful, ${results.failed} failed`);
    console.log(`📊 Coverage by designation:`, results.byDesignation);
    console.log(`📋 Session ${sessionId} completed with status: ${results.failed > results.successful ? 'failed' : 'completed'}`);

    return results;
  }

  /**
   * Get designation requirements for a shift instance
   * @param {Object} instance - Shift instance
   * @param {Object} transaction - Database transaction
   * @returns {Array} Designation requirements
   */
  async getInstanceDesignationRequirements(instance, transaction) {
    try {
      const requirements = await RotaShiftInstanceDesignationRequirement.findAll({
        where: {
          shiftInstanceId: instance.id
        },
        include: [
          {
            model: Designation,
            as: 'designation',
            required: false
          }
        ],
        order: [['priority', 'DESC'], ['designationId', 'ASC']],
        transaction
      });

      console.log(`📋 Found ${requirements.length} designation requirements for instance ${instance.id}`);
      return requirements;
    } catch (error) {
      console.error(`❌ Error getting designation requirements for instance ${instance.id}:`, error.message);
      return [];
    }
  }

  /**
   * ENHANCED: Get employees grouped by designation with eligibility checking
   * @param {Object} tenantContext - Tenant context
   * @param {Array} excludedEmployees - Excluded employee IDs
   * @param {Object} transaction - Database transaction
   * @returns {Object} Employees grouped by designation ID
   */
  async getEmployeesGroupedByDesignation(tenantContext, excludedEmployees = [], transaction) {
    // Get all active employees with their designations
    const employees = await Employee.findAll({
      where: {
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        status:'active',
        id: {
          [Op.notIn]: excludedEmployees
        }
      },
      include: [
        {
          model: Designation,
          as: 'designation',
          required: true
        }
      ],
      transaction
    });

    console.log(`📊 Found ${employees.length} total active employees`);

    const groupedEmployees = {};
    for (const employee of employees) {
      const designationId = employee.designationId;

      if (!groupedEmployees[designationId]) {
        groupedEmployees[designationId] = [];
      }

      groupedEmployees[designationId].push(employee);

      // DEBUG: Log employee details
      console.log(`👤 Employee ${employee.id} (${employee.name || 'Unknown'}) → Designation ${designationId} (${employee.designation?.name || 'Unknown'})`);
    }

    console.log(`📊 Employees grouped by designations: ${Object.keys(groupedEmployees).map(id => `${id}(${groupedEmployees[id].length})`).join(', ')}`);

    // DEBUG: Detailed breakdown
    for (const [designationId, empList] of Object.entries(groupedEmployees)) {
      console.log(`🎯 Designation ${designationId}: ${empList.map(emp => `${emp.id}(${emp.name || 'Unknown'})`).join(', ')}`);
    }

    return groupedEmployees;
  }

  /**
   * Create or update designation requirements in junction table (UPSERT logic)
   * @param {number} instanceId - Shift instance ID
   * @param {Array} requirements - Designation requirements array
   * @param {string} requirementType - Type: 'base', 'custom', 'override'
   * @param {string} sourceType - Source: 'template', 'rotaShift', 'manual', 'forecast'
   * @param {number} userId - User ID for audit
   * @param {Object} transaction - Database transaction
   */
  async createInstanceDesignationRequirements(instanceId, requirements, requirementType = 'base', sourceType = 'template', userId, transaction) {
    if (!requirements || requirements.length === 0) return;

    console.log(`🔧 Creating/updating ${requirements.length} designation requirements for instance ${instanceId} (type: ${requirementType})`);

    for (const req of requirements) {
      try {
        // Check if requirement already exists for this instance + designation
        const existingRequirement = await RotaShiftInstanceDesignationRequirement.findOne({
          where: {
            shiftInstanceId: instanceId,
            designationId: req.designationId
          },
          transaction
        });

        const requirementData = {
          shiftInstanceId: instanceId,
          designationId: req.designationId,
          requiredCount: req.requiredCount || 1,
          assignedCount: req.assignedCount || 0,
          priority: req.priority || 0,
          requirementType,
          sourceType,
          overrideReason: req.overrideReason || null,
          updatedById: userId
        };

        if (existingRequirement) {
          // UPDATE: Override with higher priority requirement type
          const typePriority = { 'override': 3, 'custom': 2, 'base': 1 };
          const existingPriority = typePriority[existingRequirement.requirementType] || 1;
          const newPriority = typePriority[requirementType] || 1;

          if (newPriority >= existingPriority) {
            await existingRequirement.update(requirementData, { transaction });
            console.log(`✅ Updated designation ${req.designationId} requirement (${existingRequirement.requirementType} → ${requirementType})`);
          } else {
            console.log(`⚠️ Skipped designation ${req.designationId} - existing ${existingRequirement.requirementType} has higher priority than ${requirementType}`);
          }
        } else {
          // CREATE: New requirement
          requirementData.createdById = userId;
          await RotaShiftInstanceDesignationRequirement.create(requirementData, { transaction });
          console.log(`✅ Created designation ${req.designationId} requirement (type: ${requirementType})`);
        }

      } catch (error) {
        console.error(`❌ Error processing designation ${req.designationId} requirement:`, error.message);
        throw error;
      }
    }
  }

  /**
   * Update assigned count for designation requirement
   * @param {number} instanceId - Shift instance ID
   * @param {number} designationId - Designation ID
   * @param {number} increment - Count to add (can be negative)
   * @param {Object} transaction - Database transaction
   */
  async updateInstanceDesignationAssignedCount(instanceId, designationId, increment, transaction) {
    await RotaShiftInstanceDesignationRequirement.increment(
      'assignedCount',
      {
        by: increment,
        where: {
          shiftInstanceId: instanceId,
          designationId: designationId
        },
        transaction
      }
    );
  }

  /**
   * Format instance with designation requirements for API response
   * Maintains same response format as before (backward compatibility)
   * @param {Object} instance - Shift instance
   * @param {Object} transaction - Database transaction
   * @returns {Object} Formatted instance
   */
  async formatInstanceForResponse(instance, transaction) {
    // Get designation requirements from junction table
    const designationRequirements = await this.getInstanceDesignationRequirements(instance, transaction);

    // Format as JSON array (same as before) with designation details
    const formattedRequirements = designationRequirements.map(req => ({
      designationId: req.designationId,
      requiredCount: req.requiredCount,
      assignedCount: req.assignedCount,
      priority: req.priority,
      designation: req.designation ? {
        id: req.designation.id,
        name: req.designation.name,
        code: req.designation.code
      } : null
    }));

    // Return instance with formatted requirements
    return {
      ...instance.toJSON(),
      designationRequirements: formattedRequirements
    };
  }

  /**
   * Get designation requirements for a shift instance
   * @param {Object} instance - Shift instance
   * @param {Object} transaction - Database transaction
   * @returns {Array} Designation requirements
   */
  async getInstanceDesignationRequirements(instance, transaction) {
    // Try junction table first (new approach)
    const junctionRequirements = await RotaShiftInstanceDesignationRequirement.findAll({
      where: {
        shiftInstanceId: instance.id
      },
      include: [
        {
          model: Designation,
          as: 'designation'
        }
      ],
      transaction
    });

    if (junctionRequirements.length > 0) {
      // Resolve conflicts: Override > Custom > Base
      const resolvedRequirements = {};
      const typePriority = { 'override': 3, 'custom': 2, 'base': 1 };

      junctionRequirements.forEach(req => {
        const key = req.designationId;
        const currentPriority = typePriority[req.requirementType] || 1;

        if (!resolvedRequirements[key] ||
            currentPriority > (typePriority[resolvedRequirements[key].requirementType] || 1)) {
          resolvedRequirements[key] = req;
        }
      });

      return Object.values(resolvedRequirements).map(req => ({
        designationId: req.designationId,
        requiredCount: req.requiredCount,
        assignedCount: req.assignedCount,
        priority: req.priority,
        requirementType: req.requirementType,
        sourceType: req.sourceType,
        designation: req.designation ? {
          id: req.designation.id,
          name: req.designation.name,
          code: req.designation.code
        } : null
      }));
    }

    // Fallback to JSON field (backward compatibility)
    const requirements = instance.designationRequirements || [];

    if (requirements.length === 0) {
      return [];
    }

    // Get designation details for each requirement
    const designationIds = requirements.map(req => req.designationId);
    const designations = await Designation.findAll({
      where: {
        id: designationIds
      },
      transaction
    });

    // Create a map for quick lookup
    const designationMap = {};
    designations.forEach(designation => {
      designationMap[designation.id] = designation;
    });

    return requirements.map(req => ({
      designationId: req.designationId,
      requiredCount: req.requiredCount,
      assignedCount: req.assignedCount || 0,
      priority: req.priority || 0,
      designation: designationMap[req.designationId]
    }));
  }

  /**
   * Apply assignment strategy to select employees
   * @param {Array} eligibleEmployees - Eligible employees
   * @param {number} requiredCount - Required employee count
   * @param {string} strategy - Assignment strategy
   * @param {Object} instance - Shift instance
   * @param {Object} constraints - Assignment constraints
   * @param {Object} workloadTracker - Workload tracker
   * @param {Object} transaction - Database transaction
   * @returns {Array} Selected employees
   */
  async applyAssignmentStrategy(eligibleEmployees, requiredCount, strategy, instance, constraints, workloadTracker, transaction, allInstances = []) {
    console.log(`🎯 Applying ${strategy} strategy to select ${requiredCount} from ${eligibleEmployees.length} eligible employees`);

    switch (strategy) {
      case 'rotation':
        return await this.applyRotationStrategy(eligibleEmployees, requiredCount, instance, workloadTracker);

      case 'balanced':
        return await this.applyBalancedStrategy(eligibleEmployees, requiredCount, constraints, workloadTracker);

      case 'skill_based':
        return await this.applySkillBasedStrategy(eligibleEmployees, requiredCount, instance, transaction);

      case 'smart':
      default:
        return await this.applySmartStrategy(eligibleEmployees, requiredCount, instance, constraints, workloadTracker, transaction, allInstances);
    }
  }

  /**
   * Apply rotation-based assignment strategy
   * @param {Array} eligibleEmployees - Eligible employees
   * @param {number} requiredCount - Required count
   * @param {Object} instance - Shift instance
   * @param {Object} workloadTracker - Workload tracker
   * @returns {Array} Selected employees
   */
  async applyRotationStrategy(eligibleEmployees, requiredCount, instance, workloadTracker) {
    console.log('🔄 Applying rotation strategy');

    // Sort by least recent assignment for this type of shift
    const sortedEmployees = eligibleEmployees.sort((a, b) => {
      const aTracker = workloadTracker[a.id] || { lastAssignmentDate: null, totalShifts: 0 };
      const bTracker = workloadTracker[b.id] || { lastAssignmentDate: null, totalShifts: 0 };

      // Prioritize employees who haven't been assigned recently
      if (!aTracker.lastAssignmentDate && !bTracker.lastAssignmentDate) {
        return aTracker.totalShifts - bTracker.totalShifts;
      }
      if (!aTracker.lastAssignmentDate) return -1;
      if (!bTracker.lastAssignmentDate) return 1;

      return moment(aTracker.lastAssignmentDate).diff(moment(bTracker.lastAssignmentDate));
    });

    return sortedEmployees.slice(0, requiredCount);
  }

  /**
   * Apply balanced workload assignment strategy
   * @param {Array} eligibleEmployees - Eligible employees
   * @param {number} requiredCount - Required count
   * @param {Object} constraints - Assignment constraints
   * @param {Object} workloadTracker - Workload tracker
   * @returns {Array} Selected employees
   */
  async applyBalancedStrategy(eligibleEmployees, requiredCount, constraints, workloadTracker) {
    console.log('⚖️ Applying balanced workload strategy');

    // Sort by current workload (ascending)
    const sortedEmployees = eligibleEmployees.sort((a, b) => {
      const aTracker = workloadTracker[a.id] || { totalShifts: 0, totalHours: 0 };
      const bTracker = workloadTracker[b.id] || { totalShifts: 0, totalHours: 0 };

      // Primary sort: total shifts
      if (aTracker.totalShifts !== bTracker.totalShifts) {
        return aTracker.totalShifts - bTracker.totalShifts;
      }

      // Secondary sort: total hours
      return aTracker.totalHours - bTracker.totalHours;
    });

    return sortedEmployees.slice(0, requiredCount);
  }

  /**
   * Apply skill-based assignment strategy
   * @param {Array} eligibleEmployees - Eligible employees
   * @param {number} requiredCount - Required count
   * @param {Object} instance - Shift instance
   * @param {Object} transaction - Database transaction
   * @returns {Array} Selected employees
   */
  async applySkillBasedStrategy(eligibleEmployees, requiredCount, instance, transaction) {
    console.log('🎯 Applying skill-based strategy');

    // For now, prioritize by experience level and seniority
    // TODO: Implement actual skill matching when skill system is available
    const sortedEmployees = eligibleEmployees.sort((a, b) => {
      // Sort by joining date (more experienced first)
      return moment(a.joiningDate).diff(moment(b.joiningDate));
    });

    return sortedEmployees.slice(0, requiredCount);
  }

  /**
   * ENHANCED: Apply smart assignment strategy with database-backed workload analysis
   * @param {Array} eligibleEmployees - Eligible employees
   * @param {number} requiredCount - Required count
   * @param {Object} instance - Shift instance
   * @param {Object} constraints - Assignment constraints
   * @param {Object} workloadTracker - Workload tracker
   * @param {Object} transaction - Database transaction
   * @returns {Array} Selected employees
   */
  async applySmartStrategy(eligibleEmployees, requiredCount, instance, constraints, workloadTracker, transaction, allInstances = []) {
    console.log('🧠 Applying PRIORITY-BASED smart assignment strategy for 15-day consistency');

    // ✅ CRITICAL: Use priority-based assignment for 15-day consistency
    const currentDate = instance.date;

    // ✅ DEBUG: Check if allInstances is properly passed
    console.log(`📊 DEBUG: allInstances length: ${allInstances.length}, current date: ${currentDate}`);
    const instancesForDate = allInstances.filter(inst => inst.date === currentDate);
    console.log(`📊 DEBUG: instances for ${currentDate}: ${instancesForDate.length}`);

    if (instancesForDate.length === 0) {
      console.log('⚠️ No instances found for current date, falling back to standard scoring');
      // Fallback to original logic
      const employeeIds = eligibleEmployees.map(emp => emp.id);
      const dbWorkloadData = await this.getEmployeeWorkloadData(employeeIds, transaction);
      return this.applyStandardSmartStrategy(eligibleEmployees, requiredCount, instance, constraints, workloadTracker, dbWorkloadData, transaction);
    }

    const assignmentPriorities = this.calculateEmployeeAssignmentPriority(workloadTracker, allInstances, currentDate);
    const instancePriorities = assignmentPriorities[instance.id];

    if (!instancePriorities) {
      console.log('⚠️ No assignment priorities found for instance, falling back to standard scoring');
      // Fallback to original logic
      const employeeIds = eligibleEmployees.map(emp => emp.id);
      const dbWorkloadData = await this.getEmployeeWorkloadData(employeeIds, transaction);
      return this.applyStandardSmartStrategy(eligibleEmployees, requiredCount, instance, constraints, workloadTracker, dbWorkloadData, transaction);
    }

    console.log(`🎯 Using priority-based assignment for instance ${instance.id} (${instance.rotaShift?.name})`);

    // Filter eligible employees based on priority system
    const eligibleEmployeeIds = new Set(eligibleEmployees.map(emp => emp.id));
    const prioritizedEmployees = instancePriorities.employeePriorities
      .filter(ep => eligibleEmployeeIds.has(ep.employeeId) && ep.shouldAssign)
      .slice(0, requiredCount * 2) // Get top candidates (2x required for selection flexibility)
      .map(ep => {
        const employee = eligibleEmployees.find(emp => emp.id === ep.employeeId);
        return {
          ...employee,
          priorityScore: ep.priority,
          priorityReason: ep.reason,
          priorityDetails: ep.details
        };
      })
      .filter(emp => emp); // Remove any null entries

    console.log(`📊 Priority-based candidates: ${prioritizedEmployees.map(emp => `${emp.id}(${emp.priorityScore}-${emp.priorityReason})`).join(', ')}`);

    // Select top employees based on priority
    const selectedEmployees = prioritizedEmployees.slice(0, requiredCount);

    console.log(`✅ Selected ${selectedEmployees.length} employees based on 15-day consistency priorities:`);
    selectedEmployees.forEach(emp => {
      console.log(`   - Employee ${emp.id}: Priority ${emp.priorityScore} (${emp.priorityReason}) - ${emp.priorityDetails}`);
    });

    return selectedEmployees;
  }

  /**
   * ✅ FALLBACK: Standard smart strategy when priority system is not available
   */
  async applyStandardSmartStrategy(eligibleEmployees, requiredCount, instance, constraints, workloadTracker, dbWorkloadData, transaction = null) {

    // Calculate score for each employee based on multiple factors
    const scoredEmployees = await Promise.all(eligibleEmployees.map(async employee => {
      const tracker = workloadTracker[employee.id] || { totalShifts: 0, totalHours: 0, lastAssignmentDate: null };
      const dbWorkload = dbWorkloadData[employee.id] || {};
      let score = 0;
      const scoreDetails = {};

      // ✅ ENHANCED: Factor 1: Employee sequence consistency (35% weight - HIGHEST PRIORITY)
      // Note: In fallback mode, we don't have allInstances, so use simplified scoring
      const sequenceResult = { score: 50, reason: 'fallback_mode', isConsistent: true };
      const sequenceScore = sequenceResult.score / 100; // Normalize to 0-1
      score += sequenceScore * 35;
      scoreDetails.sequenceScore = sequenceScore * 35;
      scoreDetails.sequenceDetails = sequenceResult;

      // ✅ ENHANCED: Factor 2: Shift consistency/stickiness with gap handling (35% weight - HIGH PRIORITY)
      const consistencyResult = this.calculateShiftConsistencyScore(employee.id, instance, workloadTracker);
      let consistencyScore = consistencyResult.score / 100; // Normalize to 0-1

      // ✅ CRITICAL: Boost score for employees resuming after gaps with preserved shift type
      if (tracker && tracker.gapDays && tracker.gapDays.length > 0) {
        const currentShiftType = instance.rotaShift?.name;
        const lastWorkingDate = tracker.lastAssignmentDate ? moment(tracker.lastAssignmentDate) : null;
        const currentDate = moment(instance.date);
        const daysSinceLastWork = lastWorkingDate ? currentDate.diff(lastWorkingDate, 'days') : 0;

        if (daysSinceLastWork > 1) {
          // Find recent gap with preserved preferences
          const recentGap = tracker.gapDays
            .filter(gap => moment(gap.date).isBefore(currentDate))
            .sort((a, b) => moment(b.date).diff(moment(a.date)))
            [0];

          if (recentGap && recentGap.preservedShiftType === currentShiftType) {
            consistencyScore = 1.0; // Perfect score for resuming preserved shift type
            console.log(`🎯 Employee ${employee.id}: PRIORITY BOOST for resuming preserved shift type '${currentShiftType}' after gap`);
            scoreDetails.gapResumption = {
              resumingPreservedShift: true,
              preservedShiftType: recentGap.preservedShiftType,
              gapDate: recentGap.date
            };
          }
        }
      }

      score += consistencyScore * 35;
      scoreDetails.consistencyScore = consistencyScore * 35;
      scoreDetails.consistencyDetails = consistencyResult;

      // Factor 2: Workload balance (25% weight) - Reduced to accommodate consistency
      const maxShifts = Math.max(...Object.values(workloadTracker).map(t => t.totalShifts || 0));
      const dbMaxShifts = Math.max(...Object.values(dbWorkloadData).map(w => w.totalShiftsAssigned || 0));

      const memoryWorkloadScore = maxShifts > 0 ? (maxShifts - tracker.totalShifts) / maxShifts : 1;
      const dbWorkloadScore = dbMaxShifts > 0 ? (dbMaxShifts - (dbWorkload.totalShiftsAssigned || 0)) / dbMaxShifts : 1;
      const workloadScore = (memoryWorkloadScore * 0.6 + dbWorkloadScore * 0.4); // Weighted average

      score += workloadScore * 25;
      scoreDetails.workloadScore = workloadScore * 25;

      // Factor 3: Rotation fairness (20% weight) - Reduced to accommodate consistency
      let rotationScore = 0.5;
      const lastWorkDate = dbWorkload.lastWorkDate || tracker.lastAssignmentDate;

      if (lastWorkDate) {
        const daysSinceLastAssignment = moment(instance.date).diff(moment(lastWorkDate), 'days');
        rotationScore = Math.min(daysSinceLastAssignment / 7, 1);
      } else {
        rotationScore = 1; // Never assigned = highest score
      }

      score += rotationScore * 20;
      scoreDetails.rotationScore = rotationScore * 20;

      // Factor 4: Balance score from database (10% weight) - Reduced
      const balanceScore = (dbWorkload.balanceScore || 50) / 100; // Normalize to 0-1
      score += balanceScore * 10;
      scoreDetails.balanceScore = balanceScore * 10;

      // Factor 5: Experience/seniority (5% weight) - Reduced
      const experienceMonths = moment().diff(moment(employee.joiningDate), 'months');
      const experienceScore = Math.min(experienceMonths / 24, 1);
      score += experienceScore * 5;
      scoreDetails.experienceScore = experienceScore * 5;

      // Factor 5: Shift type preference (5% weight - reduced from original)
      const shiftTypeScore = await this.calculateShiftTypePreference(employee, instance, dbWorkload);
      score += shiftTypeScore * 5;
      scoreDetails.shiftTypeScore = shiftTypeScore * 5;

      // Factor 6: Availability and constraints (5% weight)
      const availabilityScore = await this.calculateAvailabilityScore(employee, instance, transaction);
      score += availabilityScore * 5;
      scoreDetails.availabilityScore = availabilityScore * 5;

      // ✅ ENHANCED: Log 15-day consistency details
      if (scoreDetails.consistencyDetails) {
        if (!scoreDetails.consistencyDetails.isConsistent) {
          console.log(`🔄 Employee ${employee.id} shift change: ${scoreDetails.consistencyDetails.details}`);

          // ✅ CRITICAL: Log 15-day minimum violations
          if (scoreDetails.consistencyDetails.reason === 'premature_shift_change') {
            console.log(`⚠️ Employee ${employee.id} 15-DAY VIOLATION: Only ${scoreDetails.consistencyDetails.consistencyDays} days completed (minimum: 15 days)`);
            console.log(`   - Days remaining: ${scoreDetails.consistencyDetails.daysRemaining}`);
            console.log(`   - Score penalty: ${scoreDetails.consistencyScore} (heavily penalized)`);
          }
        } else if (scoreDetails.consistencyDetails.consistencyDays) {
          console.log(`✅ Employee ${employee.id} consistency maintained: ${scoreDetails.consistencyDetails.consistencyDays} days in current shift type`);
        }
      }

      // ✅ ENHANCED: Log sequence consistency details
      if (scoreDetails.sequenceDetails && !scoreDetails.sequenceDetails.isConsistent) {
        console.log(`🔄 Employee ${employee.id} position change: ${scoreDetails.sequenceDetails.details}`);
      }

      return {
        employee,
        score,
        details: scoreDetails,
        workloadData: dbWorkload
      };
    }));

    // Sort by score (descending)
    scoredEmployees.sort((a, b) => b.score - a.score);

    // Log top candidates for debugging
    console.log(`📊 Smart strategy scores (top 5):`);
    scoredEmployees.slice(0, 5).forEach((item, index) => {
      console.log(`  ${index + 1}. Employee ${item.employee.id}: ${item.score.toFixed(2)} points`);
      console.log(`     - Workload: ${item.details.workloadScore.toFixed(1)}, Rotation: ${item.details.rotationScore.toFixed(1)}, Balance: ${item.details.balanceScore.toFixed(1)}`);
    });

    return scoredEmployees.slice(0, requiredCount).map(item => item.employee);
  }

  /**
   * Get employee workload data from database
   * @param {Array} employeeIds - Employee IDs
   * @param {Object} transaction - Database transaction
   * @returns {Object} Workload data by employee ID
   */
  async getEmployeeWorkloadData(employeeIds, transaction) {
    try {
      const currentDate = new Date();
      const weekStart = moment(currentDate).startOf('week').toDate();
      const weekEnd = moment(currentDate).endOf('week').toDate();

      const workloadData = await EmployeeWorkloadTracker.findAll({
        where: {
          employeeId: { [Op.in]: employeeIds },
          trackingPeriod: 'weekly',
          periodStartDate: weekStart,
          periodEndDate: weekEnd
        },
        transaction
      });

      const result = {};
      workloadData.forEach(data => {
        result[data.employeeId] = data.toJSON();
      });

      return result;
    } catch (error) {
      console.error(`❌ Error getting employee workload data:`, error.message);
      return {};
    }
  }

  /**
   * Calculate shift type preference score
   * @param {Object} employee - Employee
   * @param {Object} instance - Shift instance
   * @param {Object} workloadData - Employee workload data
   * @returns {number} Preference score (0-1)
   */
  async calculateShiftTypePreference(employee, instance, workloadData) {
    try {
      // Determine shift type
      const shiftType = this.determineShiftType(instance);

      // Get employee's shift distribution
      const totalShifts = workloadData.totalShiftsAssigned || 0;
      if (totalShifts === 0) return 0.5; // Neutral for new employees

      const shiftCounts = {
        morning: workloadData.morningShifts || 0,
        afternoon: workloadData.afternoonShifts || 0,
        evening: workloadData.eveningShifts || 0,
        night: workloadData.nightShifts || 0
      };

      // Calculate preference based on least assigned shift type
      const currentTypeCount = shiftCounts[shiftType] || 0;
      const averageCount = totalShifts / 4;

      // Higher score for shift types the employee has done less
      return Math.max(0, Math.min(1, (averageCount - currentTypeCount + averageCount) / (averageCount * 2)));
    } catch (error) {
      return 0.5; // Neutral score on error
    }
  }

  /**
   * Determine shift type based on start time
   * @param {Object} instance - Shift instance
   * @returns {string} Shift type
   */
  determineShiftType(instance) {
    try {
      if (instance.rotaShift && instance.rotaShift.startTime) {
        const startHour = moment(`2000-01-01 ${instance.rotaShift.startTime}`).hour();

        if (startHour >= 6 && startHour < 12) return 'morning';
        if (startHour >= 12 && startHour < 18) return 'afternoon';
        if (startHour >= 18 && startHour < 22) return 'evening';
        return 'night';
      }
      return 'morning'; // Default
    } catch (error) {
      return 'morning';
    }
  }

  /**
   * Calculate availability score
   * @param {Object} employee - Employee
   * @param {Object} instance - Shift instance
   * @param {Object} transaction - Database transaction
   * @returns {number} Availability score (0-1)
   */
  async calculateAvailabilityScore(employee, instance, transaction) {
    try {
      // TODO: Implement when EmployeeAvailability is fully implemented
      // For now, return neutral score
      return 0.5;
    } catch (error) {
      return 0.5;
    }
  }

  /**
   * Fallback assignment when no designation requirements found
   * @param {Object} instance - Shift instance
   * @param {Object} employeesByDesignation - Employees grouped by designation
   * @param {Object} constraints - Assignment constraints
   * @param {Object} workloadTracker - Workload tracker
   * @param {Object} results - Results object to update
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @param {Set} instanceAssignedEmployees - Set to track already assigned employees for this instance
   * @param {Set} dateAssignedEmployees - Set to track already assigned employees for this date
   */
  async assignInstanceFallback(instance, employeesByDesignation, constraints, workloadTracker, results, tenantContext, transaction, instanceAssignedEmployees = new Set(), dateAssignedEmployees = new Set()) {
    console.log(`🔄 Using fallback assignment for instance ${instance.id}`);

    // Use all available employees, excluding those assigned to this instance AND this date
    const allEmployees = Object.values(employeesByDesignation).flat();
    const availableEmployees = allEmployees.filter(emp =>
      !instanceAssignedEmployees.has(emp.id) && !dateAssignedEmployees.has(emp.id)
    );
    const requiredCount = instance.rotaShift?.baseRequiredCount || 1;

    console.log(`📊 Fallback: ${allEmployees.length} total employees, ${availableEmployees.length} available`);
    console.log(`   - Instance assigned: ${instanceAssignedEmployees.size}, Date assigned: ${dateAssignedEmployees.size}`);

    if (availableEmployees.length === 0) {
      console.log(`⚠️ No available employees for fallback assignment - trying emergency fallback`);

      // ✅ EMERGENCY FALLBACK: Relax all constraints for critical assignment
      const emergencyEmployees = allEmployees.filter(emp =>
        !instanceAssignedEmployees.has(emp.id) && // Only check instance conflicts
        !dateAssignedEmployees.has(emp.id) // Only check date conflicts
      );

      if (emergencyEmployees.length > 0) {
        console.log(`🚨 Emergency fallback: Found ${emergencyEmployees.length} employees (relaxed constraints)`);

        const selectedEmployees = await this.applySmartStrategy(
          emergencyEmployees,
          requiredCount,
          instance,
          { ...constraints, maxConsecutiveDays: 14, maxShiftsPerEmployee: 100 }, // Relaxed constraints
          workloadTracker,
          transaction
        );

        if (selectedEmployees.length > 0) {
          console.log(`✅ Emergency fallback successful: ${selectedEmployees.length} employees assigned`);

          for (const employee of selectedEmployees) {
            await this.createShiftAssignment(employee, instance, results, workloadTracker, transaction);
            instanceAssignedEmployees.add(employee.id);
            dateAssignedEmployees.add(employee.id);
          }
          return;
        }
      }

      console.log(`❌ No available employees even with emergency fallback`);
      results.errors.push(`No available employees for assignment on ${instance.date} (tried emergency fallback with relaxed constraints)`);
      results.failed += requiredCount;
      return;
    }

    const selectedEmployees = await this.applySmartStrategy(
      availableEmployees,
      requiredCount,
      instance,
      constraints,
      workloadTracker,
      transaction
    );

    // Create assignments
    for (const employee of selectedEmployees) {
      try {
        const assignment = await ShiftAssignment.create({
          shiftInstanceId: instance.id,
          employeeId: employee.id,
          status: 'assigned',
          assignmentType: 'auto_scheduled',
          assignedAt: new Date(),
          assignedBy: tenantContext.userId,
          createdById: tenantContext.userId,
          notes: 'Auto-scheduled via fallback strategy (no designation requirements)'
        }, { transaction });

        results.assignments.push(assignment);
        results.successful++;
        results.byStrategy.smart++;

        this.updateWorkloadTracker(workloadTracker, employee.id, instance);

        // CRITICAL: Track this employee as assigned for this instance AND this date
        instanceAssignedEmployees.add(employee.id);
        dateAssignedEmployees.add(employee.id);

        console.log(`✅ Fallback assigned employee ${employee.id} to instance ${instance.id} on ${instance.date}`);

      } catch (error) {
        console.error(`❌ Fallback assignment failed for employee ${employee.id}:`, error.message);
        results.errors.push(`Fallback assignment failed: ${error.message}`);
        results.failed++;
      }
    }
  }

  /**
   * Filter eligible employees for a specific instance based on constraints
   * @param {Array} employees - All employees for the designation
   * @param {Object} instance - Shift instance
   * @param {Object} constraints - Assignment constraints
   * @param {Object} workloadTracker - Workload tracker
   * @param {Object} transaction - Database transaction
   * @returns {Array} Eligible employees
   */
  async filterEligibleEmployeesForInstance(employees, instance, constraints, workloadTracker, transaction) {
    const {
      respectAvailability = true,
      maxConsecutiveDays = 15,
      minRestHours = 12,
      minimizeOvertime = true,
      preferredEmployees = [],
      excludedEmployees = []
    } = constraints;

    console.log(`🔍 Filtering ${employees.length} employees for instance ${instance.id}`);

    let eligibleEmployees = [...employees];

    // Filter out excluded employees
    if (excludedEmployees.length > 0) {
      eligibleEmployees = eligibleEmployees.filter(emp => !excludedEmployees.includes(emp.id));
      console.log(`📊 After excluding employees: ${eligibleEmployees.length} remaining`);
    }

    // ✅ FIXED: Check for existing assignments on the same date - ONLY in published schedules
    const existingAssignments = await ShiftAssignment.findAll({
      where: {
        employeeId: { [Op.in]: eligibleEmployees.map(emp => emp.id) },
        status: { [Op.in]: ['assigned', 'confirmed'] }
      },
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: { date: instance.date },
          required: true,
          include: [
            {
              model: RotaShift,
              as: 'rotaShift'
            },
            {
              model: RotaSchedule,
              as: 'schedule',
              attributes: ['id', 'name', 'status', 'scheduleType'],
              where: {
                // ✅ CRITICAL FIX: Only check published schedules
                status: { [Op.in]: ['published'] }
              }
            }
          ]
        }
      ],
      transaction
    });

    // Filter out employees with conflicting assignments
    const conflictingEmployeeIds = new Set();
    for (const assignment of existingAssignments) {
      const existingShift = assignment.shiftInstance.rotaShift;
      const currentShift = instance.rotaShift;

      if (existingShift && currentShift) {
        const hasOverlap = this.checkShiftTimeOverlap(existingShift, currentShift);
        if (hasOverlap) {
          conflictingEmployeeIds.add(assignment.employeeId);
          console.log(`🚫 Employee ${assignment.employeeId} has conflicting shift on ${instance.date}`);
        }
      } else {
        // If we can't determine overlap, assume conflict for safety
        conflictingEmployeeIds.add(assignment.employeeId);
        console.log(`🚫 Employee ${assignment.employeeId} already assigned on ${instance.date} (unknown overlap)`);
      }
    }

    eligibleEmployees = eligibleEmployees.filter(emp => !conflictingEmployeeIds.has(emp.id));
    console.log(`📊 After conflict detection: ${eligibleEmployees.length} remaining`);

    // TODO: Add availability checking when EmployeeAvailability is properly implemented
    if (respectAvailability) {
      // Placeholder for availability checking
      console.log(`⏰ Availability checking enabled but not yet implemented`);
    }

    // TODO: Add consecutive days checking
    if (maxConsecutiveDays < 15) {
      console.log(`📅 Consecutive days limit: ${maxConsecutiveDays} (not yet implemented)`);
    }

    // Prioritize preferred employees
    if (preferredEmployees.length > 0) {
      const preferred = eligibleEmployees.filter(emp => preferredEmployees.includes(emp.id));
      const others = eligibleEmployees.filter(emp => !preferredEmployees.includes(emp.id));
      eligibleEmployees = [...preferred, ...others];
      console.log(`⭐ Prioritized ${preferred.length} preferred employees`);
    }

    console.log(`✅ Final eligible employees: ${eligibleEmployees.length}`);
    return eligibleEmployees;
  }

  /**
   * ✅ FIXED: Enhanced workload tracker for multi-month schedules
   * @param {Object} workloadTracker - Workload tracker
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Shift instance
   */
  updateWorkloadTracker(workloadTracker, employeeId, instance) {
    if (!workloadTracker[employeeId]) {
      workloadTracker[employeeId] = {
        totalShifts: 0,
        totalHours: 0,
        lastAssignmentDate: null,
        consecutiveDays: 0,
        // ✅ ENHANCED: Multi-month tracking
        weeklyShifts: {},
        monthlyShifts: {},
        assignmentHistory: [],
        rotationFrequency: 0,
        lastRotationDate: null
      };
    }

    const tracker = workloadTracker[employeeId];
    const instanceDate = moment(instance.date);

    // ✅ FIXED: Use safer key formats
    const weekKey = `${instanceDate.year()}-W${instanceDate.week()}`;
    const monthKey = `${instanceDate.year()}-${String(instanceDate.month() + 1).padStart(2, '0')}`;

    console.log(`📊 Updating workload for employee ${employeeId}: week=${weekKey}, month=${monthKey}`);

    // ✅ FIXED: Proper consecutive days calculation
    if (tracker.lastAssignmentDate) {
      const lastDate = moment(tracker.lastAssignmentDate);
      const daysDiff = instanceDate.diff(lastDate, 'days');

      if (daysDiff === 1) {
        tracker.consecutiveDays++;
      } else if (daysDiff > 1) {
        tracker.consecutiveDays = 1; // Reset if gap exists
      }
    } else {
      tracker.consecutiveDays = 1;
    }

    // ✅ ENHANCED: Multi-period tracking
    tracker.totalShifts++;
    tracker.totalHours += instance.rotaShift?.duration || 8;
    tracker.lastAssignmentDate = instance.date;

    // ✅ FIXED: Ensure objects exist before accessing
    if (!tracker.weeklyShifts) {
      tracker.weeklyShifts = {};
    }
    if (!tracker.monthlyShifts) {
      tracker.monthlyShifts = {};
    }
    if (!tracker.assignmentHistory) {
      tracker.assignmentHistory = [];
    }

    // Weekly tracking
    tracker.weeklyShifts[weekKey] = (tracker.weeklyShifts[weekKey] || 0) + 1;

    // Monthly tracking
    tracker.monthlyShifts[monthKey] = (tracker.monthlyShifts[monthKey] || 0) + 1;

    // Assignment history for rotation analysis
    tracker.assignmentHistory.push({
      date: instance.date,
      shiftId: instance.rotaShiftId,
      shiftType: instance.rotaShift?.name || 'Unknown',
      shiftTypeName: instance.rotaShift?.name || 'Unknown'
    });

    // ✅ SHIFT CONSISTENCY: Update shift type tracking
    const currentShiftType = instance.rotaShift?.name;
    if (currentShiftType) {
      // Initialize shift type history if not exists
      if (!tracker.shiftTypeHistory) {
        tracker.shiftTypeHistory = {};
      }
      if (!tracker.shiftChangeReasons) {
        tracker.shiftChangeReasons = [];
      }

      // Update shift type history count
      tracker.shiftTypeHistory[currentShiftType] = (tracker.shiftTypeHistory[currentShiftType] || 0) + 1;

      // Set preferred shift type (most assigned shift type)
      const mostAssignedShiftType = Object.keys(tracker.shiftTypeHistory).reduce((a, b) =>
        tracker.shiftTypeHistory[a] > tracker.shiftTypeHistory[b] ? a : b
      );
      tracker.preferredShiftType = mostAssignedShiftType;

      // Check for shift type change
      if (tracker.lastAssignedShiftType && tracker.lastAssignedShiftType !== currentShiftType) {
        // Record shift change
        tracker.shiftChangeReasons.push({
          date: instance.date,
          from: tracker.lastAssignedShiftType,
          to: currentShiftType,
          reason: 'assignment_strategy' // Will be updated with actual reason
        });

        // Reduce consistency score
        tracker.shiftTypeConsistencyScore = Math.max(tracker.shiftTypeConsistencyScore - 10, 0);
        console.log(`🔄 Shift type change detected for employee ${employeeId}: ${tracker.lastAssignedShiftType} → ${currentShiftType}`);
      }

      // Update last assigned shift type
      tracker.lastAssignedShiftType = currentShiftType;

      console.log(`📊 Employee ${employeeId} shift consistency: preferred=${tracker.preferredShiftType}, current=${currentShiftType}, score=${tracker.shiftTypeConsistencyScore}`);
    }

    // ✅ ENHANCED: Employee sequencing with gap-aware tracking
    const sequenceTracker = tracker.assignmentSequence;
    if (sequenceTracker && instance.rotaShift) {
      // Determine shift position (Morning=1, Afternoon=2, Night=3)
      const shiftPosition = this.determineShiftPosition(instance);

      // ✅ CRITICAL: Check if this assignment continues after a gap
      const lastWorkingDate = tracker.lastAssignmentDate ? moment(tracker.lastAssignmentDate) : null;
      const currentDate = moment(instance.date);
      const daysSinceLastWork = lastWorkingDate ? currentDate.diff(lastWorkingDate, 'days') : 0;

      if (daysSinceLastWork > 1 && tracker.gapDays && tracker.gapDays.length > 0) {
        console.log(`📌 Employee ${employeeId}: Resuming work after gap, checking preserved preferences`);

        // Find the most recent gap that preserved shift preferences
        const recentGap = tracker.gapDays
          .filter(gap => moment(gap.date).isBefore(currentDate))
          .sort((a, b) => moment(b.date).diff(moment(a.date)))
          [0];

        if (recentGap && recentGap.preservedShiftType === currentShiftType && recentGap.preservedPosition === shiftPosition) {
          console.log(`✅ Employee ${employeeId}: Successfully resumed preserved shift type '${currentShiftType}' and position ${shiftPosition}`);
        } else if (recentGap) {
          console.log(`⚠️ Employee ${employeeId}: Shift change after gap - preserved: ${recentGap.preservedShiftType}(${recentGap.preservedPosition}), current: ${currentShiftType}(${shiftPosition})`);
        }
      }

      // Update position history
      if (!sequenceTracker.shiftPositionHistory) {
        sequenceTracker.shiftPositionHistory = {};
      }
      sequenceTracker.shiftPositionHistory[shiftPosition] = (sequenceTracker.shiftPositionHistory[shiftPosition] || 0) + 1;

      // Set preferred position (most assigned position)
      const mostAssignedPosition = Object.keys(sequenceTracker.shiftPositionHistory).reduce((a, b) =>
        sequenceTracker.shiftPositionHistory[a] > sequenceTracker.shiftPositionHistory[b] ? a : b
      );
      sequenceTracker.preferredShiftPosition = parseInt(mostAssignedPosition);

      // Check for position change (but be lenient after gaps)
      if (sequenceTracker.lastShiftPosition && sequenceTracker.lastShiftPosition !== shiftPosition) {
        if (daysSinceLastWork <= 1) {
          // Normal position change - apply penalty
          sequenceTracker.sequenceConsistencyScore = Math.max(sequenceTracker.sequenceConsistencyScore - 10, 0);
          console.log(`🔄 Shift position change detected for employee ${employeeId}: position ${sequenceTracker.lastShiftPosition} → ${shiftPosition}`);
        } else {
          // Position change after gap - reduced penalty
          sequenceTracker.sequenceConsistencyScore = Math.max(sequenceTracker.sequenceConsistencyScore - 5, 0);
          console.log(`🔄 Shift position change after gap for employee ${employeeId}: position ${sequenceTracker.lastShiftPosition} → ${shiftPosition} (reduced penalty)`);
        }
      }

      // Update last assigned position
      sequenceTracker.lastShiftPosition = shiftPosition;

      // Update working days streak (hotel standard tracking)
      sequenceTracker.workingDaysStreak++;
      sequenceTracker.monthlyWorkingDays++;
      sequenceTracker.yearlyWorkingDays++;

      // Add to assignment pattern
      if (!sequenceTracker.assignmentPattern) {
        sequenceTracker.assignmentPattern = [];
      }
      sequenceTracker.assignmentPattern.push({
        date: instance.date,
        shiftPosition: shiftPosition,
        shiftType: currentShiftType,
        shiftId: instance.rotaShiftId,
        resumedAfterGap: daysSinceLastWork > 1
      });

      console.log(`📊 Employee ${employeeId} sequence: preferred position=${sequenceTracker.preferredShiftPosition}, current=${shiftPosition}, streak=${sequenceTracker.workingDaysStreak} days`);
    }

    // ✅ ROTATION FREQUENCY TRACKING
    try {
      if (tracker.assignmentHistory && tracker.assignmentHistory.length > 1) {
        const lastAssignment = tracker.assignmentHistory[tracker.assignmentHistory.length - 2];
        if (lastAssignment && lastAssignment.shiftId !== instance.rotaShiftId) {
          tracker.rotationFrequency = (tracker.rotationFrequency || 0) + 1;
          tracker.lastRotationDate = instance.date;
        }
      }
    } catch (error) {
      console.error(`❌ Error updating rotation frequency for employee ${employeeId}:`, error.message);
      // Continue without rotation tracking
    }
  }

  /**
   * ENHANCED: Filter employees based on comprehensive eligibility criteria
   * @param {Array} employees - Employees to filter
   * @param {Object} instance - Shift instance
   * @param {Object} constraints - Assignment constraints
   * @param {Object} workloadTracker - Workload tracker
   * @param {Object} transaction - Database transaction
   * @returns {Array} Eligible employees
   */
  async filterEligibleEmployeesForInstance(employees, instance, constraints, workloadTracker, transaction) {
    const {
      respectAvailability = true,
      maxConsecutiveDays = 7,
      minRestHours = 12,
      maxShiftsPerEmployee = 40,
      maxOvertimeHours = 10
    } = constraints;

    console.log(`🔍 Filtering ${employees.length} employees for instance ${instance.id} on ${instance.date}`);

    const eligibleEmployees = [];
    const filterResults = {
      total: employees.length,
      onLeave: 0,
      unavailable: 0,
      maxShiftsExceeded: 0,
      consecutiveDaysExceeded: 0,
      restHoursViolation: 0,
      overtimeExceeded: 0,
      conflicted: 0,
      eligible: 0
    };

    for (const employee of employees) {
      let isEligible = true;
      const reasons = [];

      try {
        // 1. CRITICAL: Check if employee is on leave
        const isOnLeave = await this.checkEmployeeLeaveStatus(employee.id, instance.date, transaction);
        if (isOnLeave.onLeave) {
          isEligible = false;
          reasons.push(`On ${isOnLeave.leaveType} leave`);
          filterResults.onLeave++;
          continue;
        }

        // 2. Check availability if enabled
        if (respectAvailability) {
          const isAvailable = await this.checkEmployeeAvailability(employee.id, instance.date, instance, transaction);
          if (!isAvailable.available) {
            isEligible = false;
            reasons.push(`Not available: ${isAvailable.reason}`);
            filterResults.unavailable++;
            continue;
          }
        }

        // ✅ ENHANCED: Check workload constraints with detailed logging
        const workloadCheck = await this.checkEnhancedWorkloadConstraints(employee.id, instance, workloadTracker, constraints, transaction);
        if (!workloadCheck.eligible) {
          isEligible = false;
          reasons.push(...workloadCheck.reasons);

          // ✅ ENHANCED LOGGING: Show why employee is blocked
          console.log(`❌ Employee ${employee.id} blocked by workload constraints: ${workloadCheck.reasons.join(', ')}`);
          console.log(`   📊 Workload data: totalShifts=${workloadCheck.workloadDetails?.totalShifts}, consecutiveDays=${workloadCheck.workloadDetails?.consecutiveDays}`);

          // Update specific counters
          if (workloadCheck.reasons.some(r => r.includes('Max shifts'))) filterResults.maxShiftsExceeded++;
          if (workloadCheck.reasons.some(r => r.includes('Consecutive days'))) filterResults.consecutiveDaysExceeded++;
          if (workloadCheck.reasons.some(r => r.includes('Rest hours'))) filterResults.restHoursViolation++;
          if (workloadCheck.reasons.some(r => r.includes('Overtime'))) filterResults.overtimeExceeded++;

          continue;
        }

        // 4. Check for scheduling conflicts
        const conflictCheck = await this.checkSchedulingConflicts(employee.id, instance, transaction);
        if (conflictCheck.hasConflict) {
          isEligible = false;
          reasons.push(`Scheduling conflict: ${conflictCheck.reason}`);
          filterResults.conflicted++;
          continue;
        }

        // If all checks pass, employee is eligible
        if (isEligible) {
          eligibleEmployees.push({
            ...employee.toJSON(),
            eligibilityScore: workloadCheck.score || 100
          });
          filterResults.eligible++;
        }

      } catch (error) {
        console.error(`❌ Error checking eligibility for employee ${employee.id}:`, error.message);
        // Skip this employee if there's an error
        continue;
      }
    }

    console.log(`📊 Eligibility filtering results:`, filterResults);

    if (filterResults.eligible === 0) {
      console.log(`⚠️ No eligible employees found. Breakdown:`, {
        onLeave: filterResults.onLeave,
        unavailable: filterResults.unavailable,
        workloadIssues: filterResults.maxShiftsExceeded + filterResults.consecutiveDaysExceeded + filterResults.restHoursViolation + filterResults.overtimeExceeded,
        conflicts: filterResults.conflicted
      });
    }

    return eligibleEmployees;
  }

  /**
   * Check if employee is on leave for the given date
   * @param {number} employeeId - Employee ID
   * @param {string} date - Date to check
   * @param {Object} transaction - Database transaction
   * @returns {Object} Leave status
   */
  async checkEmployeeLeaveStatus(employeeId, date, transaction) {
    try {
      // Import LeaveRequest model dynamically to avoid circular dependency
      const { Leave } = require('../../data/models');

      const leaveRequest = await Leave.findOne({
        where: {
          employeeId: employeeId,
          startDate: { [Op.lte]: date },
          endDate: { [Op.gte]: date },
          status: 'approved'
        },
        transaction
      });

      if (leaveRequest) {
        return {
          onLeave: true,
          leaveType: leaveRequest.leaveType || 'leave',
          leaveId: leaveRequest.id
        };
      }

      return { onLeave: false };

    } catch (error) {
      console.error(`❌ Error checking leave status for employee ${employeeId}:`, error.message);
      // If there's an error checking leave, assume not on leave but log the error
      return { onLeave: false, error: error.message };
    }
  }

  /**
   * Check employee availability for the given date and shift
   * @param {number} employeeId - Employee ID
   * @param {string} date - Date to check
   * @param {Object} instance - Shift instance
   * @param {Object} transaction - Database transaction
   * @returns {Object} Availability status
   */
  async checkEmployeeAvailability(employeeId, date, instance, transaction) {
    try {
      // Check if employee has availability preferences/restrictions
      // For now, implement basic availability check
      // TODO: Implement comprehensive availability system when available

      const dayOfWeek = moment(date).format('dddd').toLowerCase();

      // Basic check: assume employees are available unless specifically restricted
      // This can be enhanced with actual availability model when implemented

      return {
        available: true,
        reason: null
      };

    } catch (error) {
      console.error(`❌ Error checking availability for employee ${employeeId}:`, error.message);
      return {
        available: true, // Default to available if check fails
        reason: `Availability check failed: ${error.message}`
      };
    }
  }

  /**
   * ENHANCED: Check workload constraints with comprehensive validation
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Shift instance
   * @param {Object} workloadTracker - Workload tracker
   * @param {Object} constraints - Assignment constraints
   * @param {Object} transaction - Database transaction
   * @returns {Object} Workload check result
   */
  async checkEnhancedWorkloadConstraints(employeeId, instance, workloadTracker, constraints, transaction) {
    const {
      maxConsecutiveDays = 15,
      minRestHours = 12,
      maxShiftsPerEmployee = 40,
      maxOvertimeHours = 10
    } = constraints;

    const tracker = workloadTracker[employeeId] || {
      totalShifts: 0,
      totalHours: 0,
      consecutiveDays: 0,
      lastAssignmentDate: null,
      overtimeHours: 0
    };

    const reasons = [];
    let eligible = true;
    let score = 100;

    try {
      // 1. Check maximum shifts per month - RELAXED for long-term schedules
      const adjustedMaxShifts = maxShiftsPerEmployee * 2; // Double for multi-month schedules
      if (tracker.totalShifts >= adjustedMaxShifts) {
        eligible = false;
        reasons.push(`Max shifts exceeded (${tracker.totalShifts}/${adjustedMaxShifts})`);
        score -= 50;
      } else if (tracker.totalShifts >= maxShiftsPerEmployee) {
        // Soft limit - reduce score but keep eligible
        score -= 20;
        reasons.push(`Approaching max shifts (${tracker.totalShifts}/${maxShiftsPerEmployee})`);
      }

      // ✅ FIXED: Declare adjustedMaxConsecutive outside conditional for scope access
      const adjustedMaxConsecutive = Math.min(maxConsecutiveDays + 2, 15); // Allow 2 extra days, max 15

      // 2. Check consecutive days limit - RELAXED for long-term schedules
      if (tracker.lastAssignmentDate) {
        const lastDate = moment(tracker.lastAssignmentDate);
        const currentDate = moment(instance.date);
        const daysDiff = currentDate.diff(lastDate, 'days');

        if (daysDiff === 1 && tracker.consecutiveDays >= adjustedMaxConsecutive) {
          // Still block if severely over limit
          eligible = false;
          reasons.push(`Consecutive days severely exceeded (${tracker.consecutiveDays}/${adjustedMaxConsecutive})`);
          score -= 40;
        } else if (daysDiff === 1 && tracker.consecutiveDays >= maxConsecutiveDays) {
          // Soft limit - reduce score but keep eligible
          score -= 15;
          reasons.push(`Consecutive days over preferred limit (${tracker.consecutiveDays}/${maxConsecutiveDays})`);
        }
      }

      // ✅ FIXED: Check rotation frequency constraints
      const minDaysBetweenRotations = constraints.minDaysBetweenRotations || 15;
      if (tracker.lastRotationDate && instance.rotaShiftId) {
        const lastRotationDate = moment(tracker.lastRotationDate);
        const currentDate = moment(instance.date);
        const daysSinceRotation = currentDate.diff(lastRotationDate, 'days');

        // Check if this would be a rotation (different shift type)
        const lastAssignment = tracker.assignmentHistory?.[tracker.assignmentHistory.length - 1];
        const wouldBeRotation = lastAssignment && lastAssignment.shiftId !== instance.rotaShiftId;

        if (wouldBeRotation && daysSinceRotation < minDaysBetweenRotations) {
          eligible = false;
          reasons.push(`Rotation too frequent (${daysSinceRotation}/${minDaysBetweenRotations} days since last rotation)`);
          score -= 30;
        }
      }

      // ✅ ENHANCED: Rotation frequency scoring
      const rotationRatio = tracker.rotationFrequency / Math.max(tracker.totalShifts, 1);
      if (rotationRatio > 0.5) { // More than 50% rotations
        score -= 15; // Prefer less frequently rotated employees
        reasons.push(`High rotation frequency (${Math.round(rotationRatio * 100)}%)`);
      }

      // 3. Check minimum rest hours between shifts
      if (tracker.lastAssignmentDate) {
        const lastShiftEnd = await this.getLastShiftEndTime(employeeId, tracker.lastAssignmentDate, transaction);
        const currentShiftStart = await this.getShiftStartTime(instance, transaction);

        if (lastShiftEnd && currentShiftStart) {
          const restHours = moment(currentShiftStart).diff(moment(lastShiftEnd), 'hours');
          if (restHours < minRestHours) {
            eligible = false;
            reasons.push(`Rest hours violation (${restHours}h < ${minRestHours}h required)`);
            score -= 30;
          }
        }
      }

      // 4. Check overtime limits
      if (tracker.overtimeHours >= maxOvertimeHours) {
        eligible = false;
        reasons.push(`Overtime exceeded (${tracker.overtimeHours}h/${maxOvertimeHours}h)`);
        score -= 25;
      }

      // 5. Calculate workload score for ranking
      const workloadRatio = tracker.totalShifts / maxShiftsPerEmployee;
      score -= (workloadRatio * 20); // Reduce score based on current workload

      // ✅ ENHANCED: Return detailed workload information for debugging
      const result = {
        eligible,
        reasons,
        score: Math.max(score, 0),
        workloadDetails: {
          totalShifts: tracker.totalShifts,
          maxShifts: maxShiftsPerEmployee,
          consecutiveDays: tracker.consecutiveDays,
          overtimeHours: tracker.overtimeHours,
          lastAssignmentDate: tracker.lastAssignmentDate,
          // ✅ DEBUG INFO: Add constraint limits for comparison
          adjustedMaxShifts: adjustedMaxShifts,
          adjustedMaxConsecutive: adjustedMaxConsecutive
        }
      };

      // ✅ DEBUG LOGGING: Log constraint check details for failed cases
      if (!eligible) {
        console.log(`🔍 Employee ${employeeId} workload constraint failure:`);
        console.log(`   📊 Total shifts: ${tracker.totalShifts}/${adjustedMaxShifts} (limit: ${maxShiftsPerEmployee})`);
        console.log(`   📅 Consecutive days: ${tracker.consecutiveDays}/${adjustedMaxConsecutive}`);
        console.log(`   ⏰ Last assignment: ${tracker.lastAssignmentDate}`);
        console.log(`   ❌ Reasons: ${reasons.join(', ')}`);
      }

      return result;

    } catch (error) {
      console.error(`❌ Error checking workload constraints for employee ${employeeId}:`, error.message);
      return {
        eligible: true, // Default to eligible if check fails
        reasons: [`Workload check failed: ${error.message}`],
        score: 50
      };
    }
  }

  /**
   * Check for scheduling conflicts
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Shift instance
   * @param {Object} transaction - Database transaction
   * @returns {Object} Conflict check result
   */
  async checkSchedulingConflicts(employeeId, instance, transaction) {
    try {
      // Check if employee is already assigned to another shift on the same date
      const existingAssignment = await ShiftAssignment.findOne({
        where: {
          employeeId: employeeId,
          status: { [Op.in]: ['assigned', 'confirmed'] }
        },
        include: [
          {
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: {
              date: instance.date
            },
            include: [
              {
                model: RotaShift,
                as: 'rotaShift'
              }
            ]
          }
        ],
        transaction
      });

      if (existingAssignment) {
        // CRITICAL FIX: Check if it's the SAME shift instance (not a conflict)
        if (existingAssignment.shiftInstanceId === instance.id) {
          console.log(`✅ Employee ${employeeId} already assigned to SAME shift instance ${instance.id} - not a conflict`);
          return { hasConflict: false };
        }

        // Check if shifts overlap (different shifts on same date)
        const existingShift = existingAssignment.shiftInstance.rotaShift;
        const currentShift = instance.rotaShift;

        if (existingShift && currentShift) {
          const overlap = this.checkShiftTimeOverlap(existingShift, currentShift);
          if (overlap) {
            return {
              hasConflict: true,
              reason: `Already assigned to overlapping shift (${existingShift.name || 'Unknown'})`
            };
          }
        }

        // Different shifts on same date but no time overlap - allow
        console.log(`✅ Employee ${employeeId} assigned to different non-overlapping shift on ${instance.date} - no conflict`);
        return { hasConflict: false };
      }

      return { hasConflict: false };

    } catch (error) {
      console.error(`❌ Error checking scheduling conflicts for employee ${employeeId}:`, error.message);
      return {
        hasConflict: false, // Default to no conflict if check fails
        reason: `Conflict check failed: ${error.message}`
      };
    }
  }

  /**
   * Get last shift end time for an employee
   * @param {number} employeeId - Employee ID
   * @param {string} date - Date of last assignment
   * @param {Object} transaction - Database transaction
   * @returns {string|null} End time
   */
  async getLastShiftEndTime(employeeId, date, transaction) {
    try {
      const lastAssignment = await ShiftAssignment.findOne({
        where: {
          employeeId: employeeId,
          status: { [Op.in]: ['assigned', 'confirmed', 'completed'] }
        },
        include: [
          {
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: { date: date },
            include: [
              {
                model: RotaShift,
                as: 'rotaShift'
              }
            ]
          }
        ],
        order: [['createdAt', 'DESC']],
        transaction
      });

      if (lastAssignment && lastAssignment.shiftInstance.rotaShift) {
        const shift = lastAssignment.shiftInstance.rotaShift;
        return moment(`${date} ${shift.endTime}`).toISOString();
      }

      return null;
    } catch (error) {
      console.error(`❌ Error getting last shift end time:`, error.message);
      return null;
    }
  }

  /**
   * Get shift start time for an instance
   * @param {Object} instance - Shift instance
   * @param {Object} transaction - Database transaction
   * @returns {string|null} Start time
   */
  async getShiftStartTime(instance, transaction) {
    try {
      if (instance.rotaShift && instance.rotaShift.startTime) {
        return moment(`${instance.date} ${instance.rotaShift.startTime}`).toISOString();
      }

      // If rotaShift not loaded, fetch it
      const rotaShift = await RotaShift.findByPk(instance.rotaShiftId, { transaction });
      if (rotaShift && rotaShift.startTime) {
        return moment(`${instance.date} ${rotaShift.startTime}`).toISOString();
      }

      return null;
    } catch (error) {
      console.error(`❌ Error getting shift start time:`, error.message);
      return null;
    }
  }

  /**
   * ENHANCED: Process a single designation requirement with tracking
   * @param {Object} instance - Shift instance
   * @param {Object} designationReq - Designation requirement
   * @param {Object} employeesByDesignation - Employees grouped by designation
   * @param {Object} constraints - Assignment constraints
   * @param {Object} workloadTracker - Workload tracker
   * @param {Object} results - Results object to update
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @param {Set} instanceAssignedEmployees - Set to track already assigned employees for this instance (per designation)
   * @param {Set} dateAssignedEmployees - Set to track already assigned employees for this date (across all shifts)
   */
  async processDesignationRequirement(instance, designationReq, employeesByDesignation, constraints, workloadTracker, results, tenantContext, transaction, instanceAssignedEmployees = new Set(), dateAssignedEmployees = new Set(), allInstances = []) {
    const { assignmentStrategy = 'smart', allowPartialAssignment = true } = constraints;
    const designationId = designationReq.designationId;
    const requiredCount = designationReq.requiredCount;
    const sessionId = results.sessionId;

    console.log(`\n🎯 Processing designation ${designationId}: need ${requiredCount} employees for instance ${instance.id}`);
    console.log(`📋 Session: ${sessionId}`);
    console.log(`📊 Current tracking - Instance assigned: ${instanceAssignedEmployees.size}, Date assigned: ${dateAssignedEmployees.size}`);

    // ✅ ENHANCED: Initialize designation tracking with 15-day consistency support
    if (!results.byDesignation[designationId]) {
      results.byDesignation[designationId] = {
        required: 0,
        assigned: 0,
        coverage: 0,
        consistencyMaintained: 0, // ✅ NEW: Track 15-day consistency maintenance
        shiftChanges: 0, // ✅ NEW: Track shift type changes
        coverageComplete: false // ✅ NEW: Track complete coverage status
      };
    }
    results.byDesignation[designationId].required += requiredCount;

    // Get eligible employees for this designation
    const allEmployeesForDesignation = employeesByDesignation[designationId] || [];

    // DEBUG: Log designation filtering
    console.log(`🎯 Designation ${designationId} filtering:`);
    console.log(`   - Available designations in employeesByDesignation: ${Object.keys(employeesByDesignation).join(', ')}`);
    console.log(`   - Employees for designation ${designationId}: ${allEmployeesForDesignation.length}`);
    if (allEmployeesForDesignation.length > 0) {
      console.log(`   - Employee IDs: ${allEmployeesForDesignation.map(emp => `${emp.id}(${emp.name || 'Unknown'})`).join(', ')}`);
    }

    if (allEmployeesForDesignation.length === 0) {
      console.log(`❌ No employees found for designation ${designationId}`);
      console.log(`❌ Available employees in other designations:`);
      for (const [dId, empList] of Object.entries(employeesByDesignation)) {
        console.log(`   - Designation ${dId}: ${empList.length} employees`);
      }
      results.errors.push(`No employees available for designation ${designationId} on ${instance.date}`);
      results.failed += requiredCount;
      return;
    }

    // Filter employees based on constraints and availability
    const eligibleEmployees = await this.filterEligibleEmployeesForInstance(
      allEmployeesForDesignation,
      instance,
      constraints,
      workloadTracker,
      transaction
    );

    // ENHANCED: Database-backed conflict detection + in-memory tracking
    const availableEmployees = [];

    for (const emp of eligibleEmployees) {
      // Check in-memory tracking first (fast)
      const isInstanceAssigned = instanceAssignedEmployees.has(emp.id);
      const isDateAssigned = dateAssignedEmployees.has(emp.id);

      if (isInstanceAssigned) {
        console.log(`🚫 Employee ${emp.id} already assigned to this instance (in-memory)`);
        continue;
      }

      if (isDateAssigned) {
        console.log(`🚫 Employee ${emp.id} already assigned to another shift on this date (in-memory)`);
        continue;
      }

      // Check database tracking for conflicts (reliable) - but exclude current session pending trackers
      const conflictCheck = await this.checkAssignmentConflictsExcludingPending(sessionId, emp.id, instance.date, transaction, instance);
      if (conflictCheck.hasConflict) {
        console.log(`🚫 Employee ${emp.id} (${emp.name || 'Unknown'}) has database conflict: ${conflictCheck.reason}`);
        console.log(`   - Conflict types: ${conflictCheck.conflictTypes?.join(', ') || 'Unknown'}`);
        continue;
      }

      // Employee is available
      console.log(`✅ Employee ${emp.id} (${emp.name || 'Unknown'}) is available for designation ${designationId}`);
      availableEmployees.push(emp);
    }

    console.log(`📊 Designation ${designationId}: ${allEmployeesForDesignation.length} total, ${eligibleEmployees.length} eligible, ${availableEmployees.length} available`);
    console.log(`   - Excluded by in-memory tracking: ${eligibleEmployees.length - availableEmployees.length}`);

    if (availableEmployees.length === 0) {
      console.log(`❌ No available employees found for designation ${designationId} after conflict detection`);
      results.errors.push(`No available employees for designation ${designationId} on ${instance.date} (all eligible employees have conflicts)`);
      results.failed += requiredCount;
      return;
    }

    // Ensure we don't assign more than available
    const actualRequiredCount = Math.min(requiredCount, availableEmployees.length);
    if (actualRequiredCount < requiredCount) {
      console.log(`⚠️ Reducing required count from ${requiredCount} to ${actualRequiredCount} due to availability`);
    }

    // Apply assignment strategy to available employees
    const selectedEmployees = await this.applyAssignmentStrategy(
      availableEmployees,
      actualRequiredCount,
      assignmentStrategy,
      instance,
      constraints,
      workloadTracker,
      transaction,
      allInstances // ✅ CRITICAL: Pass all instances for priority calculation
    );

    console.log(`✅ Selected ${selectedEmployees.length} employees for designation ${designationId} (required: ${requiredCount})`);
    console.log(`   - Selected employee IDs: ${selectedEmployees.map(emp => `${emp.id}(${emp.name || 'Unknown'})`).join(', ')}`);

    // Validate no duplicate assignments
    const selectedEmployeeIds = selectedEmployees.map(emp => emp.id);
    const duplicates = selectedEmployeeIds.filter((id, index) => selectedEmployeeIds.indexOf(id) !== index);
    if (duplicates.length > 0) {
      console.error(`❌ Duplicate employee selections detected: ${duplicates.join(', ')}`);
      results.errors.push(`Duplicate employee selections for designation ${designationId}: ${duplicates.join(', ')}`);
      return;
    }

    // DEBUG: Check if any selected employee is already in tracking sets
    for (const emp of selectedEmployees) {
      if (instanceAssignedEmployees.has(emp.id)) {
        console.error(`🚨 CRITICAL: Employee ${emp.id} (${emp.name || 'Unknown'}) already in instanceAssignedEmployees but still selected!`);
      }
      if (dateAssignedEmployees.has(emp.id)) {
        console.error(`🚨 CRITICAL: Employee ${emp.id} (${emp.name || 'Unknown'}) already in dateAssignedEmployees but still selected!`);
      }
    }

    // ENHANCED: Create assignments with tracking
    let successfulAssignments = 0;
    for (const employee of selectedEmployees) {
      const startTime = Date.now();
      let tracker = null;

      try {
        // STEP 1: Final validation before assignment
        if (instanceAssignedEmployees.has(employee.id)) {
          console.error(`❌ Employee ${employee.id} already assigned to this instance - skipping`);
          continue;
        }
        if (dateAssignedEmployees.has(employee.id)) {
          console.error(`❌ Employee ${employee.id} already assigned to another shift on this date - skipping`);
          continue;
        }

        // STEP 2: Create assignment tracker (pending status)
        tracker = await this.createAssignmentTracker(
          sessionId,
          instance,
          employee,
          assignmentStrategy,
          tenantContext,
          transaction
        );

        console.log(`📋 Created tracker for employee ${employee.id} (${employee.name || 'Unknown'}) in instance ${instance.id}`);

        // STEP 3: Check for existing assignment in database (designation-specific)
        const existingAssignment = await ShiftAssignment.findOne({
          where: {
            employeeId: employee.id,
            shiftInstanceId: instance.id,
            designationId: designationId // ✅ CHECK DESIGNATION-SPECIFIC ASSIGNMENT
          },
          transaction
        });

        if (existingAssignment) {
          await this.updateAssignmentTracker(sessionId, employee.id, instance.id, 'failed', transaction);
          console.error(`❌ Employee ${employee.id} already assigned to instance ${instance.id} for designation ${designationId} - skipping`);
          continue;
        }

        // STEP 3.5: Log if employee has assignments to same instance for different designations
        const otherDesignationAssignments = await ShiftAssignment.findAll({
          where: {
            employeeId: employee.id,
            shiftInstanceId: instance.id,
            designationId: { [Op.ne]: designationId }
          },
          transaction
        });

        if (otherDesignationAssignments.length > 0) {
          console.log(`ℹ️ Employee ${employee.id} has ${otherDesignationAssignments.length} other designation assignments for same instance - allowing multiple roles`);
        }

        // STEP 4: Create actual shift assignment with designation
        const assignment = await ShiftAssignment.create({
          shiftInstanceId: instance.id,
          employeeId: employee.id,
          designationId: designationId, // ✅ ADD DESIGNATION ID
          status: 'assigned',
          assignmentType: 'auto_scheduled',
          assignedAt: new Date(),
          assignedBy: tenantContext.userId,
          createdById: tenantContext.userId,
          notes: `Auto-scheduled via ${assignmentStrategy} strategy for designation ${designationId} (${employee.designation?.name || 'Unknown'}) [Session: ${sessionId}]`
        }, { transaction });

        // STEP 5: Update tracker to success
        const processingTime = Date.now() - startTime;
        await this.updateAssignmentTracker(sessionId, employee.id, instance.id, 'assigned', transaction);

        // Update processing time in tracker
        await ShiftAssignmentTracker.update({
          processingTimeMs: processingTime
        }, {
          where: {
            sessionId,
            employeeId: employee.id,
            shiftInstanceId: instance.id
          },
          transaction
        });

        // STEP 6: Update results and tracking
        results.assignments.push(assignment);
        results.successful++;
        results.byDesignation[designationId].assigned++;
        results.byStrategy[assignmentStrategy]++;
        successfulAssignments++;

        // Update workload tracker (in-memory)
        this.updateWorkloadTracker(workloadTracker, employee.id, instance);

        // Update workload tracker (database)
        await this.updateEmployeeWorkloadTracker(employee.id, instance, tenantContext, transaction);

        // Update assigned count in junction table
        await this.updateInstanceDesignationAssignedCount(instance.id, designationId, 1, transaction);

        // CRITICAL: Add to tracking sets AFTER successful assignment
        instanceAssignedEmployees.add(employee.id);
        dateAssignedEmployees.add(employee.id);

        console.log(`✅ Assigned employee ${employee.id} (${employee.designation?.name || 'Unknown'}) to instance ${instance.id} on ${instance.date} [${processingTime}ms]`);
        console.log(`📊 Current tracking - Instance: ${instanceAssignedEmployees.size}, Date: ${dateAssignedEmployees.size}`);

      } catch (error) {
        // Update tracker to failed
        if (tracker) {
          await this.updateAssignmentTracker(sessionId, employee.id, instance.id, 'failed', transaction);
        }

        console.error(`❌ Failed to assign employee ${employee.id} to instance ${instance.id}:`, error.message);
        results.errors.push(`Failed to assign employee ${employee.id}: ${error.message}`);
        results.failed++;
      }
    }

    // ✅ ENHANCED: Detailed partial assignment analysis
    if (successfulAssignments < requiredCount) {
      const shortfall = requiredCount - successfulAssignments;
      console.log(`\n⚠️ PARTIAL ASSIGNMENT DETECTED:`);
      console.log(`   📊 Designation: ${designationId}`);
      console.log(`   📅 Date: ${instance.date}`);
      console.log(`   🎯 Required: ${requiredCount}`);
      console.log(`   ✅ Assigned: ${successfulAssignments}`);
      console.log(`   ❌ Shortfall: ${shortfall}`);

      // ✅ ANALYZE REASONS for partial assignment
      await this.analyzePartialAssignmentReasons(
        instance,
        designationId,
        requiredCount,
        successfulAssignments,
        shortfall,
        employeesByDesignation,
        constraints,
        workloadTracker,
        results,
        transaction
      );

      results.byDesignation[designationId].coverage = Math.round((successfulAssignments / requiredCount) * 100);

      if (!allowPartialAssignment) {
        results.errors.push(`Could not fill all ${requiredCount} positions for designation ${designationId} on ${instance.date} (only ${successfulAssignments} assigned)`);
        results.failed += shortfall;
      }
    } else {
      results.byDesignation[designationId].coverage = 100;
      console.log(`🎉 Successfully filled all ${requiredCount} positions for designation ${designationId}`);
    }

    console.log(`📊 Designation ${designationId} summary: ${successfulAssignments}/${requiredCount} assigned (${results.byDesignation[designationId].coverage}% coverage)`);
  }

  /**
   * ✅ ENHANCED: Record detailed conflict information to database
   * @param {Object} results - Results object
   * @param {string} conflictType - Type of conflict
   * @param {Object} conflictData - Detailed conflict data
   * @param {string} sessionId - Auto-schedule session ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async recordConflict(results, conflictType, conflictData, sessionId, tenantContext, transaction) {
    const conflictId = `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const conflict = {
      id: conflictId,
      type: conflictType,
      timestamp: new Date().toISOString(),
      ...conflictData
    };

    try {
      // ✅ SAVE TO DATABASE
      const ScheduleConflict = require('../../data/models').ScheduleConflict;

      const dbConflict = await ScheduleConflict.create({
        conflictId: conflictId,
        sessionId: sessionId,
        conflictType: conflictType,
        severity: conflictData.severity || 'medium',
        status: 'pending',

        // Context information
        scheduleId: conflictData.scheduleId,
        shiftInstanceId: conflictData.instance?.id,
        employeeId: conflictData.employeeId,
        designationId: conflictData.designationId,
        conflictDate: conflictData.date || conflictData.instance?.date,

        // Conflict details
        title: this.generateConflictTitle(conflictType, conflictData),
        description: conflictData.description,
        conflictData: {
          ...conflictData,
          constraintViolations: conflictData.constraintViolations,
          workloadDetails: conflictData.workloadDetails,
          existingAssignments: conflictData.existingAssignments
        },

        // Requirements vs availability
        requiredCount: conflictData.requiredCount,
        availableCount: conflictData.availableCount,
        assignedCount: conflictData.assignedCount,
        shortfall: conflictData.shortfall,

        // Impact assessment
        impactLevel: this.assessImpactLevel(conflictType, conflictData),
        affectedEmployees: conflictData.affectedEmployees || 1,

        // Multi-tenant
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      }, { transaction });

      console.log(`💾 Saved conflict to database: ${conflictType} - ${conflictId} (DB ID: ${dbConflict.id})`);

      // ✅ SAVE RECOMMENDATIONS TO DATABASE
      if (conflictData.resolutionOptions && conflictData.resolutionOptions.length > 0) {
        await this.saveConflictRecommendations(dbConflict.id, conflictData.resolutionOptions, conflictType, tenantContext, transaction);
      }

    } catch (error) {
      console.error(`❌ Error saving conflict to database:`, error.message);
      // Continue with in-memory tracking even if DB save fails
    }

    // Add to in-memory results for immediate use
    results.conflicts.details.push(conflict);
    results.conflicts.total++;
    results.conflicts.pending++;

    // Update type counters
    if (results.conflicts.byType[conflictType] !== undefined) {
      results.conflicts.byType[conflictType]++;
    }

    // Group by date
    const date = conflictData.date || conflictData.instance?.date;
    if (date) {
      if (!results.conflicts.byDate[date]) {
        results.conflicts.byDate[date] = [];
      }
      results.conflicts.byDate[date].push(conflict);
    }

    // Group by designation
    const designationId = conflictData.designationId;
    if (designationId) {
      if (!results.conflicts.byDesignation[designationId]) {
        results.conflicts.byDesignation[designationId] = [];
      }
      results.conflicts.byDesignation[designationId].push(conflict);
    }

    console.log(`📝 Recorded conflict: ${conflictType} - ${conflictId}`);
    return conflictId;
  }

  /**
   * ✅ ENHANCED: Generate actionable conflict resolution recommendations
   * @param {number} designationId - Designation ID
   * @param {string} date - Date
   * @param {number} availableEmployees - Available employees count
   * @param {number} requiredCount - Required count
   * @param {number} alreadyAssigned - Already assigned count
   * @param {number} constraintViolations - Constraint violations count
   * @param {number} availabilityIssues - Availability issues count
   * @returns {Array} Recommendations array
   */
  generateConflictRecommendations(designationId, date, availableEmployees, requiredCount, alreadyAssigned, constraintViolations, availabilityIssues) {
    const recommendations = [];

    // Employee pool recommendations
    if (availableEmployees < requiredCount) {
      recommendations.push({
        id: `rec_pool_${designationId}_${Date.now()}`,
        type: 'employee_pool',
        priority: 'high',
        title: 'Increase Employee Pool',
        description: `Hire ${requiredCount - availableEmployees} more employees for designation ${designationId}`,
        impact: 'Resolves fundamental shortage',
        effort: 'high',
        timeline: '2-4 weeks',
        actions: [
          'Post job openings for the designation',
          'Fast-track recruitment process',
          'Consider temporary/contract staff',
          'Cross-train existing employees'
        ]
      });
    }

    // Constraint relaxation recommendations
    if (constraintViolations > 0) {
      recommendations.push({
        id: `rec_constraints_${designationId}_${Date.now()}`,
        type: 'constraint_relaxation',
        priority: 'medium',
        title: 'Relax Assignment Constraints',
        description: 'Adjust constraints to allow more flexible assignments',
        impact: 'Immediate improvement in assignment success',
        effort: 'low',
        timeline: 'immediate',
        actions: [
          'Increase maxShiftsPerEmployee from 40 to 60',
          'Increase maxConsecutiveDays from 7 to 9',
          'Reduce minDaysBetweenRotations from 15 to 10',
          'Enable more frequent workload rebalancing'
        ]
      });
    }

    // Cross-training recommendations
    if (alreadyAssigned > 0) {
      recommendations.push({
        id: `rec_crosstrain_${designationId}_${Date.now()}`,
        type: 'cross_training',
        priority: 'medium',
        title: 'Implement Cross-Training',
        description: 'Train employees for multiple designations',
        impact: 'Increases flexibility and reduces conflicts',
        effort: 'medium',
        timeline: '1-2 weeks',
        actions: [
          'Identify employees suitable for cross-training',
          'Develop training programs',
          'Update employee skill profiles',
          'Implement multi-designation assignments'
        ]
      });
    }

    // Availability management recommendations
    if (availabilityIssues > 0) {
      recommendations.push({
        id: `rec_availability_${designationId}_${Date.now()}`,
        type: 'availability_management',
        priority: 'low',
        title: 'Improve Availability Management',
        description: 'Better manage employee availability and preferences',
        impact: 'Reduces availability conflicts',
        effort: 'medium',
        timeline: '1 week',
        actions: [
          'Implement availability preference system',
          'Better leave management integration',
          'Flexible shift timing options',
          'Advance notice for schedule changes'
        ]
      });
    }

    return recommendations;
  }

  /**
   * ✅ HELPER: Generate conflict title
   * @param {string} conflictType - Conflict type
   * @param {Object} conflictData - Conflict data
   * @returns {string} Conflict title
   */
  generateConflictTitle(conflictType, conflictData) {
    const titles = {
      employee_pool_exhaustion: `No employees available for designation ${conflictData.designationId}`,
      constraint_violation: `Employee ${conflictData.employeeId} violates constraints`,
      already_assigned: `Employee ${conflictData.employeeId} already assigned`,
      availability_conflict: `Availability conflict for employee ${conflictData.employeeId}`,
      designation_mismatch: `Designation mismatch for employee ${conflictData.employeeId}`,
      time_overlap: `Time overlap conflict for employee ${conflictData.employeeId}`,
      workload_limit: `Workload limit exceeded for employee ${conflictData.employeeId}`,
      partial_assignment: `Partial assignment for designation ${conflictData.designationId}`
    };

    return titles[conflictType] || `Scheduling conflict: ${conflictType}`;
  }

  /**
   * ✅ HELPER: Assess impact level
   * @param {string} conflictType - Conflict type
   * @param {Object} conflictData - Conflict data
   * @returns {string} Impact level
   */
  assessImpactLevel(conflictType, conflictData) {
    // Critical impact
    if (conflictType === 'employee_pool_exhaustion' && conflictData.availableCount === 0) {
      return 'severe';
    }

    // Significant impact
    if (conflictData.shortfall && conflictData.shortfall >= 3) {
      return 'significant';
    }

    // Moderate impact
    if (conflictType === 'constraint_violation' || conflictType === 'already_assigned') {
      return 'moderate';
    }

    // Minimal impact
    return 'minimal';
  }

  /**
   * ✅ ENHANCED: Save conflict recommendations to database
   * @param {number} conflictDbId - Database conflict ID
   * @param {Array} resolutionOptions - Resolution options
   * @param {string} conflictType - Conflict type
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async saveConflictRecommendations(conflictDbId, resolutionOptions, conflictType, tenantContext, transaction) {
    try {
      const ConflictRecommendation = require('../../data/models').ConflictRecommendation;

      for (let i = 0; i < resolutionOptions.length; i++) {
        const option = resolutionOptions[i];
        const recommendationId = `rec_${conflictType}_${Date.now()}_${i}`;

        await ConflictRecommendation.create({
          recommendationId: recommendationId,
          conflictId: conflictDbId,
          type: this.mapResolutionOptionToType(option),
          priority: this.assessRecommendationPriority(option, conflictType),
          title: option,
          description: `Recommended action: ${option}`,
          expectedImpact: this.getExpectedImpact(option),
          effortLevel: this.assessEffortLevel(option),
          estimatedTimeline: this.getEstimatedTimeline(option),
          actionSteps: this.getActionSteps(option),
          companyId: tenantContext.companyId
        }, { transaction });
      }

      console.log(`💾 Saved ${resolutionOptions.length} recommendations for conflict ${conflictDbId}`);
    } catch (error) {
      console.error(`❌ Error saving recommendations:`, error.message);
    }
  }

  /**
   * ✅ ENHANCED: Analyze reasons for partial assignment
   * @param {Object} instance - Shift instance
   * @param {number} designationId - Designation ID
   * @param {number} requiredCount - Required employee count
   * @param {number} successfulAssignments - Successful assignments
   * @param {number} shortfall - Assignment shortfall
   * @param {Object} employeesByDesignation - Employees grouped by designation
   * @param {Object} constraints - Assignment constraints
   * @param {Object} workloadTracker - Workload tracker
   * @param {Object} results - Results object
   * @param {Object} transaction - Database transaction
   */
  async analyzePartialAssignmentReasons(instance, designationId, requiredCount, successfulAssignments, shortfall, employeesByDesignation, constraints, workloadTracker, results, transaction) {
    console.log(`\n🔍 ANALYZING PARTIAL ASSIGNMENT REASONS:`);

    try {
      const allEmployeesForDesignation = employeesByDesignation[designationId] || [];
      console.log(`   📊 Total employees for designation ${designationId}: ${allEmployeesForDesignation.length}`);

      if (allEmployeesForDesignation.length === 0) {
        console.log(`   ❌ REASON 1: NO EMPLOYEES AVAILABLE for designation ${designationId}`);

        // ✅ RECORD CONFLICT: No employees available
        await this.recordConflict(results, 'employee_pool_exhaustion', {
          designationId,
          date: instance.date,
          instance: { id: instance.id, date: instance.date },
          severity: 'critical',
          description: `No employees available for designation ${designationId}`,
          requiredCount,
          availableCount: 0,
          shortfall,
          recommendation: `Hire employees for designation ${designationId} or reassign from other designations`,
          resolutionOptions: [
            'Hire new employees',
            'Cross-train existing employees',
            'Reduce shift requirements',
            'Use temporary/contract staff'
          ]
        }, results.sessionId, tenantContext, transaction);

        results.errors.push(`No employees available for designation ${designationId}`);
        return;
      }

      if (allEmployeesForDesignation.length < requiredCount) {
        console.log(`   ❌ REASON 2: INSUFFICIENT EMPLOYEE POOL (${allEmployeesForDesignation.length} < ${requiredCount})`);
        console.log(`   💡 SOLUTION: Hire more employees for designation ${designationId} or reduce requirements`);

        // ✅ RECORD CONFLICT: Insufficient employee pool
        this.recordConflict(results, 'employee_pool_exhaustion', {
          designationId,
          date: instance.date,
          instance: { id: instance.id, date: instance.date },
          severity: 'high',
          description: `Insufficient employee pool for designation ${designationId}`,
          requiredCount,
          availableCount: allEmployeesForDesignation.length,
          shortfall,
          recommendation: `Increase employee pool for designation ${designationId}`,
          resolutionOptions: [
            'Hire additional employees',
            'Cross-train from other designations',
            'Reduce shift requirements',
            'Implement flexible scheduling'
          ]
        });
      }

      // Check constraint violations
      let constraintViolations = 0;
      let alreadyAssigned = 0;
      let availabilityIssues = 0;

      for (const employee of allEmployeesForDesignation) {
        // ✅ FIXED: Check if already assigned on this date - ONLY in published schedules
        const existingAssignments = await ShiftAssignment.findAll({
          where: {
            employeeId: employee.id,
            companyId: instance.companyId
          },
          include: [{
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: { date: instance.date },
            include: [{
              model: RotaSchedule,
              as: 'schedule',
              attributes: ['id', 'name', 'status', 'scheduleType'],
              where: {
                // ✅ CRITICAL FIX: Only check published schedules
                status: { [Op.in]: ['published'] }
              }
            }]
          }],
          transaction
        });

        if (existingAssignments.length > 0) {
          alreadyAssigned++;

          // ✅ RECORD CONFLICT: Employee already assigned
          this.recordConflict(results, 'already_assigned', {
            employeeId: employee.id,
            employeeName: `${employee.firstName} ${employee.lastName}`,
            designationId,
            date: instance.date,
            instance: { id: instance.id, date: instance.date },
            severity: 'medium',
            description: `Employee ${employee.id} already assigned to another shift on ${instance.date}`,
            existingAssignments: existingAssignments.map(a => ({
              shiftInstanceId: a.shiftInstanceId,
              assignmentId: a.id
            })),
            recommendation: 'Consider cross-training or shift swapping',
            resolutionOptions: [
              'Cross-train employee for multiple designations',
              'Implement shift swapping system',
              'Adjust shift timing to avoid conflicts',
              'Use different employee'
            ]
          });

          continue;
        }

        // Check workload constraints
        const workloadCheck = await this.checkEnhancedWorkloadConstraints(
          employee.id,
          instance,
          workloadTracker,
          constraints,
          transaction
        );

        if (!workloadCheck.eligible) {
          constraintViolations++;
          console.log(`   ⚠️ Employee ${employee.id}: ${workloadCheck.reasons.join(', ')}`);

          // ✅ RECORD CONFLICT: Constraint violation
          this.recordConflict(results, 'constraint_violation', {
            employeeId: employee.id,
            employeeName: `${employee.firstName} ${employee.lastName}`,
            designationId,
            date: instance.date,
            instance: { id: instance.id, date: instance.date },
            severity: 'medium',
            description: `Employee ${employee.id} violates constraints: ${workloadCheck.reasons.join(', ')}`,
            constraintViolations: workloadCheck.reasons,
            workloadDetails: workloadCheck.workloadDetails,
            recommendation: 'Relax constraints or redistribute workload',
            resolutionOptions: [
              'Increase maxShiftsPerEmployee limit',
              'Reduce maxConsecutiveDays requirement',
              'Implement workload rebalancing',
              'Add rest periods between assignments'
            ]
          });
        }
      }

      console.log(`\n📊 DETAILED BREAKDOWN:`);
      console.log(`   👥 Total employees for designation: ${allEmployeesForDesignation.length}`);
      console.log(`   ✅ Successfully assigned: ${successfulAssignments}`);
      console.log(`   🔒 Already assigned elsewhere: ${alreadyAssigned}`);
      console.log(`   ⚠️ Constraint violations: ${constraintViolations}`);
      console.log(`   📅 Availability issues: ${availabilityIssues}`);
      console.log(`   ❌ Remaining shortfall: ${shortfall}`);

      // Provide specific recommendations
      // ✅ GENERATE ACTIONABLE RECOMMENDATIONS
      const recommendations = this.generateConflictRecommendations(
        designationId,
        instance.date,
        allEmployeesForDesignation.length,
        requiredCount,
        alreadyAssigned,
        constraintViolations,
        availabilityIssues
      );

      // Add recommendations to results
      results.conflicts.recommendations.push(...recommendations);

      console.log(`\n💡 RECOMMENDATIONS:`);
      recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec.title}: ${rec.description}`);
      });

    } catch (error) {
      console.error(`❌ Error analyzing partial assignment reasons:`, error.message);
    }
  }

  /**
   * Initialize enhanced workload tracker with database sync
   * @param {Array} employees - All employees
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Enhanced workload tracker
   */
  async initializeEnhancedWorkloadTracker(employees, tenantContext, transaction, scheduleId = null) {
    console.log(`📊 Initializing enhanced workload tracker for ${employees.length} employees`);

    const workloadTracker = {};

    // ✅ CRITICAL FIX: Load existing assignments from current schedule to preserve employee preferences
    let existingAssignments = [];
    if (scheduleId) {
      console.log(`🔍 Loading existing assignments from schedule ${scheduleId} to preserve employee preferences`);

      // Get existing shift assignments from current schedule
      existingAssignments = await ShiftAssignment.findAll({
        include: [
          {
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: { scheduleId },
            include: [
              {
                model: RotaShift,
                as: 'rotaShift',
                attributes: ['id', 'name', 'startTime', 'endTime']
              }
            ]
          }
        ],
        where: { status: ['assigned', 'confirmed'] },
        order: [['createdAt', 'ASC']], // Oldest first to maintain assignment sequence
        transaction
      });

      console.log(`📋 Found ${existingAssignments.length} existing assignments to preserve employee preferences`);
    } else {
      console.log(`🔄 Starting with fresh workload tracker (no schedule ID provided)`);
    }

    // Initialize tracker for each employee with EXISTING ASSIGNMENT DATA
    for (const employee of employees) {
      // ✅ ENHANCED: Initialize with base structure
      workloadTracker[employee.id] = {
        // Basic tracking
        totalShifts: 0,
        totalHours: 0,
        lastAssignmentDate: null,
        consecutiveDays: 0,
        balanceScore: 50,
        isOverworked: false,
        needsRest: false,

        // Enhanced tracking
        weekendShifts: 0,
        nightShifts: 0,
        overtimeHours: 0,
        rotationFrequency: 0,
        lastRotationDate: null,

        // Multi-period tracking
        weeklyShifts: {},
        monthlyShifts: {},
        assignmentHistory: [],

        // ✅ SHIFT CONSISTENCY: Track shift type assignments
        preferredShiftType: null,
        shiftTypeHistory: {},
        lastAssignedShiftType: null,
        shiftTypeConsistencyScore: 100,
        allowedShiftTypes: [],
        shiftChangeReasons: [],

        // ✅ EMPLOYEE SEQUENCING: Advanced assignment tracking
        assignmentSequence: {
          preferredShiftPosition: null,
          shiftPositionHistory: {},
          lastShiftPosition: null,
          sequenceConsistencyScore: 100,
          assignmentPattern: [],
          workingDaysStreak: 0,
          restDaysNeeded: 0,
          lastRestDate: null,
          monthlyWorkingDays: 0,
          yearlyWorkingDays: 0,
          overtimeHoursThisWeek: 0,
          overtimeHoursThisMonth: 0
        }
      };

      // ✅ CRITICAL: Populate with existing assignment data to preserve employee preferences
      const employeeAssignments = existingAssignments.filter(assignment => assignment.employeeId === employee.id);

      if (employeeAssignments.length > 0) {
        console.log(`📋 Loading ${employeeAssignments.length} existing assignments for employee ${employee.id}`);

        // Sort assignments by date to get proper sequence
        const sortedAssignments = employeeAssignments.sort((a, b) =>
          moment(a.shiftInstance.date).diff(moment(b.shiftInstance.date))
        );

        const tracker = workloadTracker[employee.id];

        // Update basic tracking
        tracker.totalShifts = employeeAssignments.length;

        // Get most recent assignment
        const latestAssignment = sortedAssignments[sortedAssignments.length - 1];
        tracker.lastAssignmentDate = latestAssignment.shiftInstance.date;
        tracker.lastAssignedShiftType = latestAssignment.shiftInstance.rotaShift?.name;

        // ✅ ENHANCED: Build comprehensive assignment history
        tracker.assignmentHistory = sortedAssignments.map(assignment => ({
          date: assignment.shiftInstance.date,
          shiftId: assignment.shiftInstance.rotaShiftId,
          shiftType: assignment.shiftInstance.rotaShift?.name || 'Unknown',
          shiftTypeName: assignment.shiftInstance.rotaShift?.name || 'Unknown',
          assignmentId: assignment.id
        }));

        // Build shift type history
        employeeAssignments.forEach(assignment => {
          const shiftType = assignment.shiftInstance.rotaShift?.name;
          if (shiftType) {
            tracker.shiftTypeHistory[shiftType] = (tracker.shiftTypeHistory[shiftType] || 0) + 1;
          }
        });

        // Set preferred shift type (most assigned)
        if (Object.keys(tracker.shiftTypeHistory).length > 0) {
          tracker.preferredShiftType = Object.keys(tracker.shiftTypeHistory).reduce((a, b) =>
            tracker.shiftTypeHistory[a] > tracker.shiftTypeHistory[b] ? a : b
          );
        }

        // ✅ CRITICAL: Calculate consecutive days properly
        if (sortedAssignments.length > 1) {
          let consecutiveDays = 1;
          for (let i = 1; i < sortedAssignments.length; i++) {
            const currentDate = moment(sortedAssignments[i].shiftInstance.date);
            const previousDate = moment(sortedAssignments[i-1].shiftInstance.date);
            const daysDiff = currentDate.diff(previousDate, 'days');

            if (daysDiff === 1) {
              consecutiveDays++;
            } else {
              consecutiveDays = 1; // Reset if gap exists
            }
          }
          tracker.consecutiveDays = consecutiveDays;
        } else {
          tracker.consecutiveDays = 1;
        }

        console.log(`✅ Employee ${employee.id} preferences loaded:`);
        console.log(`   - Preferred: ${tracker.preferredShiftType}`);
        console.log(`   - Last: ${tracker.lastAssignedShiftType}`);
        console.log(`   - Last Date: ${tracker.lastAssignmentDate}`);
        console.log(`   - Total: ${tracker.totalShifts}`);
        console.log(`   - Consecutive Days: ${tracker.consecutiveDays}`);
        console.log(`   - History: ${JSON.stringify(tracker.shiftTypeHistory)}`);
      } else {
        console.log(`📝 Employee ${employee.id}: No existing assignments, starting fresh`);
      }
    }

    console.log(`✅ Fresh workload tracker initialized (no previous schedule interference)`);
    return workloadTracker;
  }

  /**
   * Create assignment tracker record
   * @param {string} sessionId - Session ID
   * @param {Object} instance - Shift instance
   * @param {Object} employee - Employee
   * @param {string} strategy - Assignment strategy
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Tracker record
   */
  async createAssignmentTracker(sessionId, instance, employee, strategy, tenantContext, transaction) {
    try {
      const tracker = await ShiftAssignmentTracker.create({
        sessionId,
        scheduleId: instance.scheduleId,
        employeeId: employee.id,
        shiftInstanceId: instance.id,
        designationId: employee.designationId,
        assignmentDate: instance.date,
        shiftStartTime: instance.rotaShift?.startTime || null,
        shiftEndTime: instance.rotaShift?.endTime || null,
        status: 'pending',
        assignmentStrategy: strategy,
        assignmentPriority: instance.rotaShift?.priority || 5,
        hasConflicts: false,
        conflictDetails: null,
        createdById: tenantContext.userId,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      }, { transaction });

      console.log(`📋 Created assignment tracker for employee ${employee.id} in instance ${instance.id}`);
      return tracker;
    } catch (error) {
      console.error(`❌ Error creating assignment tracker:`, error.message);
      throw error;
    }
  }

  /**
   * Update assignment tracker status
   * @param {string} sessionId - Session ID
   * @param {number} employeeId - Employee ID
   * @param {number} instanceId - Instance ID
   * @param {string} status - New status
   * @param {Object} transaction - Database transaction
   */
  async updateAssignmentTracker(sessionId, employeeId, instanceId, status, transaction) {
    try {
      await ShiftAssignmentTracker.update({
        status,
        updatedAt: new Date()
      }, {
        where: {
          sessionId,
          employeeId,
          shiftInstanceId: instanceId
        },
        transaction
      });

      console.log(`📊 Updated assignment tracker: employee ${employeeId}, instance ${instanceId}, status: ${status}`);
    } catch (error) {
      console.error(`❌ Error updating assignment tracker:`, error.message);
    }
  }

  /**
   * Check assignment conflicts excluding pending trackers (for pre-assignment validation)
   * @param {string} sessionId - Session ID
   * @param {number} employeeId - Employee ID
   * @param {string} date - Assignment date
   * @param {Object} transaction - Database transaction
   * @param {Object} instance - Current shift instance (optional)
   * @returns {Object} Conflict check result
   */
  async checkAssignmentConflictsExcludingPending(sessionId, employeeId, date, transaction, instance = null) {
    try {
      const conflicts = [];

      // 1. Check only ASSIGNED session-based conflicts (exclude pending)
      const sessionConflicts = await ShiftAssignmentTracker.findAll({
        where: {
          sessionId,
          employeeId,
          assignmentDate: date,
          status: 'assigned' // Only check assigned, not pending
        },
        transaction
      });

      console.log(`🔍 Session conflict check (assigned only) for employee ${employeeId} on ${date}: ${sessionConflicts.length} conflicts found`);
      if (sessionConflicts.length > 0) {
        console.log(`   - Session conflicts: ${sessionConflicts.map(c => `Instance ${c.shiftInstanceId} (${c.status})`).join(', ')}`);
        conflicts.push({
          type: 'session_conflict',
          source: 'current_session',
          count: sessionConflicts.length,
          details: sessionConflicts
        });
      }

      // ✅ FIXED: Check existing shift assignments (database) - ONLY for published schedules
      const existingAssignments = await ShiftAssignment.findAll({
        where: {
          employeeId,
          status: { [Op.in]: ['assigned', 'confirmed'] }
        },
        include: [
          {
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: { date },
            required: true,
            include: [
              {
                model: RotaShift,
                as: 'rotaShift'
              },
              {
                model: RotaSchedule,
                as: 'schedule',
                attributes: ['id', 'name', 'status', 'scheduleType'],
                where: {
                  // ✅ CRITICAL FIX: Only check published schedules
                  status: { [Op.in]: ['published'] }
                }
              }
            ]
          }
        ],
        transaction
      });

      // CRITICAL FIX: Filter out assignments to same shift instance for DIFFERENT designations
      const conflictingAssignments = existingAssignments.filter(assignment => {
        // If we're checking against a specific instance, exclude same instance assignments for DIFFERENT designations
        if (instance && assignment.shiftInstanceId === instance.id) {
          // Check if this is for the same designation - if yes, it's a conflict
          // If different designation, it's allowed (employee can have multiple roles)
          console.log(`✅ Employee ${employeeId} already assigned to SAME shift instance ${instance.id} - checking designation specificity`);
          return false; // For now, allow same instance assignments (will be handled by designation-specific check)
        }
        return true;
      });

      if (conflictingAssignments.length > 0) {
        conflicts.push({
          type: 'existing_assignment',
          source: 'database',
          count: conflictingAssignments.length,
          details: conflictingAssignments
        });
      }

      // 3. Check workload constraints
      const workloadConflict = await this.checkWorkloadConstraints(employeeId, date, transaction);
      if (workloadConflict.hasConflict) {
        conflicts.push({
          type: 'workload_constraint',
          source: 'workload_tracker',
          details: workloadConflict
        });
      }

      // Return conflict summary
      if (conflicts.length > 0) {
        return {
          hasConflict: true,
          reason: `Employee has ${conflicts.length} conflict(s) on ${date}`,
          conflictTypes: conflicts.map(c => c.type),
          conflicts: conflicts,
          totalConflicts: conflicts.reduce((sum, c) => sum + (c.count || 1), 0)
        };
      }

      return { hasConflict: false };
    } catch (error) {
      console.error(`❌ Error checking assignment conflicts (excluding pending):`, error.message);
      return { hasConflict: false, error: error.message };
    }
  }

  /**
   * ENHANCED: Check for assignment conflicts using multiple sources
   * @param {string} sessionId - Session ID
   * @param {number} employeeId - Employee ID
   * @param {string} date - Assignment date
   * @param {Object} transaction - Database transaction
   * @returns {Object} Conflict check result
   */
  async checkAssignmentConflicts(sessionId, employeeId, date, transaction) {
    try {
      const conflicts = [];

      // 1. Check session-based conflicts (current auto-schedule session)
      const sessionConflicts = await ShiftAssignmentTracker.findAll({
        where: {
          sessionId,
          employeeId,
          assignmentDate: date,
          status: { [Op.in]: ['pending', 'assigned'] }
        },
        transaction
      });

      console.log(`🔍 Session conflict check for employee ${employeeId} on ${date}: ${sessionConflicts.length} conflicts found`);
      if (sessionConflicts.length > 0) {
        console.log(`   - Session conflicts: ${sessionConflicts.map(c => `Instance ${c.shiftInstanceId} (${c.status})`).join(', ')}`);
        conflicts.push({
          type: 'session_conflict',
          source: 'current_session',
          count: sessionConflicts.length,
          details: sessionConflicts
        });
      }

      // ✅ FIXED: Check existing shift assignments (database) - ONLY for published schedules
      const existingAssignments = await ShiftAssignment.findAll({
        where: {
          employeeId,
          status: { [Op.in]: ['assigned', 'confirmed'] }
        },
        include: [
          {
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: { date },
            required: true,
            include: [
              {
                model: RotaShift,
                as: 'rotaShift'
              },
              {
                model: RotaSchedule,
                as: 'schedule',
                attributes: ['id', 'name', 'status', 'scheduleType'],
                where: {
                  // ✅ CRITICAL FIX: Only check published schedules
                  // This prevents conflicts with other preview/draft schedules
                  status: { [Op.in]: ['published'] }
                }
              }
            ]
          }
        ],
        transaction
      });

      if (existingAssignments.length > 0) {
        conflicts.push({
          type: 'existing_assignment',
          source: 'database',
          count: existingAssignments.length,
          details: existingAssignments
        });
      }

      // 3. Check time overlap conflicts
      const timeConflicts = await this.checkTimeOverlapConflicts(employeeId, date, transaction);
      if (timeConflicts.length > 0) {
        conflicts.push({
          type: 'time_overlap',
          source: 'schedule_conflict',
          count: timeConflicts.length,
          details: timeConflicts
        });
      }

      // 4. Check workload constraints
      const workloadConflict = await this.checkWorkloadConstraints(employeeId, date, transaction);
      if (workloadConflict.hasConflict) {
        conflicts.push({
          type: 'workload_constraint',
          source: 'workload_tracker',
          details: workloadConflict
        });
      }

      // Return conflict summary
      if (conflicts.length > 0) {
        return {
          hasConflict: true,
          reason: `Employee has ${conflicts.length} conflict(s) on ${date}`,
          conflictTypes: conflicts.map(c => c.type),
          conflicts: conflicts,
          totalConflicts: conflicts.reduce((sum, c) => sum + (c.count || 1), 0)
        };
      }

      return { hasConflict: false };
    } catch (error) {
      console.error(`❌ Error checking assignment conflicts:`, error.message);
      return { hasConflict: false, error: error.message };
    }
  }

  /**
   * Check time overlap conflicts
   * @param {number} employeeId - Employee ID
   * @param {string} date - Assignment date
   * @param {Object} transaction - Database transaction
   * @returns {Array} Time overlap conflicts
   */
  async checkTimeOverlapConflicts(employeeId, date, transaction) {
    try {
      // ✅ FIXED: Get assignments for the employee on the given date - ONLY from published schedules
      const assignments = await ShiftAssignment.findAll({
        where: {
          employeeId,
          status: { [Op.in]: ['assigned', 'confirmed'] }
        },
        include: [
          {
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: { date },
            required: true,
            include: [
              {
                model: RotaShift,
                as: 'rotaShift'
              },
              {
                model: RotaSchedule,
                as: 'schedule',
                attributes: ['id', 'name', 'status', 'scheduleType'],
                where: {
                  // ✅ CRITICAL FIX: Only check published schedules
                  status: { [Op.in]: ['published'] }
                }
              }
            ]
          }
        ],
        transaction
      });

      const conflicts = [];
      for (let i = 0; i < assignments.length; i++) {
        for (let j = i + 1; j < assignments.length; j++) {
          const shift1 = assignments[i].shiftInstance.rotaShift;
          const shift2 = assignments[j].shiftInstance.rotaShift;

          if (shift1 && shift2 && this.checkShiftTimeOverlap(shift1, shift2)) {
            conflicts.push({
              assignment1: assignments[i],
              assignment2: assignments[j],
              overlap: true
            });
          }
        }
      }

      return conflicts;
    } catch (error) {
      console.error(`❌ Error checking time overlap conflicts:`, error.message);
      return [];
    }
  }

  /**
   * Check workload constraints
   * @param {number} employeeId - Employee ID
   * @param {string} date - Assignment date
   * @param {Object} transaction - Database transaction
   * @returns {Object} Workload constraint check result
   */
  async checkWorkloadConstraints(employeeId, date, transaction) {
    try {
      const weekStart = moment(date).startOf('week').toDate();
      const weekEnd = moment(date).endOf('week').toDate();

      const workloadTracker = await EmployeeWorkloadTracker.findOne({
        where: {
          employeeId,
          trackingPeriod: 'weekly',
          periodStartDate: weekStart,
          periodEndDate: weekEnd
        },
        transaction
      });

      if (!workloadTracker) {
        return { hasConflict: false };
      }

      const constraints = [];

      // Check if employee is overworked
      if (workloadTracker.isOverworked) {
        constraints.push('employee_overworked');
      }

      // Check if employee needs rest
      if (workloadTracker.needsRest) {
        constraints.push('employee_needs_rest');
      }

      // Check consecutive days limit
      if (workloadTracker.currentConsecutiveDays >= 7) {
        constraints.push('consecutive_days_limit');
      }

      // Check weekly hours limit (assuming 48 hours max)
      if (workloadTracker.totalHoursScheduled >= 48) {
        constraints.push('weekly_hours_limit');
      }

      return {
        hasConflict: constraints.length > 0,
        constraints: constraints,
        workloadData: workloadTracker
      };
    } catch (error) {
      console.error(`❌ Error checking workload constraints:`, error.message);
      return { hasConflict: false, error: error.message };
    }
  }

  /**
   * Update employee workload tracker in database
   * @param {number} employeeId - Employee ID
   * @param {Object} instance - Shift instance
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async updateEmployeeWorkloadTracker(employeeId, instance, tenantContext, transaction) {
    try {
      const currentDate = new Date();
      const weekStart = moment(currentDate).startOf('week').toDate();
      const weekEnd = moment(currentDate).endOf('week').toDate();

      // Find or create weekly workload tracker
      let workloadTracker = await EmployeeWorkloadTracker.findOne({
        where: {
          employeeId,
          trackingPeriod: 'weekly',
          periodStartDate: weekStart,
          periodEndDate: weekEnd,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        transaction
      });

      if (!workloadTracker) {
        // Create new workload tracker
        workloadTracker = await EmployeeWorkloadTracker.create({
          employeeId,
          designationId: instance.designationId || 1, // Default designation
          trackingPeriod: 'weekly',
          periodStartDate: weekStart,
          periodEndDate: weekEnd,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        }, { transaction });
      }

      // Calculate shift hours
      const shiftHours = this.calculateShiftHours(instance);
      const isWeekend = moment(instance.date).day() === 0 || moment(instance.date).day() === 6;
      const isNightShift = this.isNightShift(instance);

      // Update workload metrics
      await workloadTracker.update({
        totalShiftsAssigned: workloadTracker.totalShiftsAssigned + 1,
        totalHoursScheduled: parseFloat(workloadTracker.totalHoursScheduled) + shiftHours,
        lastWorkDate: instance.date,
        weekendShifts: workloadTracker.weekendShifts + (isWeekend ? 1 : 0),
        nightShifts: workloadTracker.nightShifts + (isNightShift ? 1 : 0),
        lastUpdated: new Date(),
        updatedBy: 'auto_schedule'
      }, { transaction });

      console.log(`📊 Updated workload tracker for employee ${employeeId}: +1 shift, +${shiftHours}h`);
    } catch (error) {
      console.error(`❌ Error updating employee workload tracker:`, error.message);
    }
  }

  /**
   * Calculate shift hours from instance
   * @param {Object} instance - Shift instance
   * @returns {number} Shift hours
   */
  calculateShiftHours(instance) {
    try {
      if (instance.rotaShift && instance.rotaShift.startTime && instance.rotaShift.endTime) {
        const start = moment(`2000-01-01 ${instance.rotaShift.startTime}`);
        const end = moment(`2000-01-01 ${instance.rotaShift.endTime}`);

        // Handle overnight shifts
        if (end.isBefore(start)) {
          end.add(1, 'day');
        }

        const hours = end.diff(start, 'hours', true);
        return Math.max(hours, 0);
      }
      return 8; // Default 8 hours
    } catch (error) {
      console.error(`❌ Error calculating shift hours:`, error.message);
      return 8;
    }
  }

  /**
   * Check if shift is night shift
   * @param {Object} instance - Shift instance
   * @returns {boolean} Is night shift
   */
  isNightShift(instance) {
    try {
      if (instance.rotaShift && instance.rotaShift.startTime) {
        const startHour = moment(`2000-01-01 ${instance.rotaShift.startTime}`).hour();
        return startHour >= 22 || startHour <= 6; // 10 PM to 6 AM
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Create auto-schedule session
   * @param {string} sessionId - Session ID
   * @param {number} scheduleId - Schedule ID
   * @param {Object} constraints - Assignment constraints
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Session record
   */
  async createAutoScheduleSession(sessionId, scheduleId, constraints, tenantContext, transaction) {
    try {
      const session = await AutoScheduleSession.create({
        sessionId,
        scheduleId,
        mode: constraints.mode || 'template',
        assignmentStrategy: constraints.assignmentStrategy || 'smart',
        constraints: constraints,
        startDate: constraints.startDate || new Date(),
        endDate: constraints.endDate || new Date(),
        status: 'processing',
        initiatedBy: tenantContext.userId,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      }, { transaction });

      console.log(`📋 Created auto-schedule session ${sessionId}`);
      return session;
    } catch (error) {
      console.error(`❌ Error creating auto-schedule session:`, error.message);
      throw error;
    }
  }

  /**
   * Update auto-schedule session
   * @param {string} sessionId - Session ID
   * @param {Object} updates - Updates to apply
   * @param {Object} transaction - Database transaction
   */
  async updateAutoScheduleSession(sessionId, updates, transaction) {
    try {
      await AutoScheduleSession.update(updates, {
        where: { sessionId },
        transaction
      });

      console.log(`📊 Updated auto-schedule session ${sessionId}`);
    } catch (error) {
      console.error(`❌ Error updating auto-schedule session:`, error.message);
    }
  }

  /**
   * Calculate overall coverage percentage
   * @param {Object} results - Assignment results
   * @returns {number} Overall coverage percentage
   */
  calculateOverallCoverage(results) {
    const totalRequired = results.successful + results.failed;
    if (totalRequired === 0) return 100;
    return Math.round((results.successful / totalRequired) * 100);
  }

  /**
   * Rollback auto-schedule session
   * @param {string} sessionId - Session ID
   * @param {string} reason - Rollback reason
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async rollbackAutoScheduleSession(sessionId, reason, tenantContext, transaction) {
    try {
      console.log(`🔄 Rolling back auto-schedule session ${sessionId}: ${reason}`);

      // Update all assignment trackers to rolled_back
      await ShiftAssignmentTracker.update({
        status: 'rolled_back'
      }, {
        where: {
          sessionId,
          status: { [Op.in]: ['pending', 'assigned'] }
        },
        transaction
      });

      // Delete all shift assignments created in this session
      const assignmentTrackers = await ShiftAssignmentTracker.findAll({
        where: { sessionId, status: 'rolled_back' },
        transaction
      });

      for (const tracker of assignmentTrackers) {
        await ShiftAssignment.destroy({
          where: {
            employeeId: tracker.employeeId,
            shiftInstanceId: tracker.shiftInstanceId
          },
          transaction
        });
      }

      // Update session status
      await AutoScheduleSession.update({
        status: 'rolled_back',
        rollbackReason: reason,
        rollbackAt: new Date(),
        rollbackBy: tenantContext.userId
      }, {
        where: { sessionId },
        transaction
      });

      console.log(`✅ Successfully rolled back session ${sessionId}`);
    } catch (error) {
      console.error(`❌ Error rolling back session ${sessionId}:`, error.message);
      throw error;
    }
  }

  /**
   * Update assigned count in instance designation requirement junction table
   * @param {number} instanceId - Instance ID
   * @param {number} designationId - Designation ID
   * @param {number} increment - Increment value (usually 1)
   * @param {Object} transaction - Database transaction
   */
  async updateInstanceDesignationAssignedCount(instanceId, designationId, increment, transaction) {
    try {
      const requirement = await RotaShiftInstanceDesignationRequirement.findOne({
        where: {
          shiftInstanceId: instanceId,
          designationId: designationId
        },
        transaction
      });

      if (requirement) {
        await requirement.update({
          assignedCount: (requirement.assignedCount || 0) + increment
        }, { transaction });
        console.log(`📊 Updated assigned count for designation ${designationId} in instance ${instanceId}: +${increment}`);
      } else {
        console.log(`⚠️ No designation requirement found for designation ${designationId} in instance ${instanceId}`);
      }
    } catch (error) {
      console.error(`❌ Error updating assigned count:`, error.message);
    }
  }

  /**
   * Check if two shifts have overlapping times
   * @param {Object} shift1 - First shift
   * @param {Object} shift2 - Second shift
   * @returns {boolean} Whether shifts overlap
   */
  checkShiftTimeOverlap(shift1, shift2) {
    try {
      if (!shift1.startTime || !shift1.endTime || !shift2.startTime || !shift2.endTime) {
        return false; // Can't determine overlap without times
      }

      const start1 = moment(shift1.startTime, 'HH:mm');
      const end1 = moment(shift1.endTime, 'HH:mm');
      const start2 = moment(shift2.startTime, 'HH:mm');
      const end2 = moment(shift2.endTime, 'HH:mm');

      // Handle overnight shifts
      if (end1.isBefore(start1)) end1.add(1, 'day');
      if (end2.isBefore(start2)) end2.add(1, 'day');

      // Check for overlap
      return start1.isBefore(end2) && start2.isBefore(end1);
    } catch (error) {
      console.error(`❌ Error checking shift overlap:`, error.message);
      return false;
    }
  }
}

module.exports = new AutoScheduleService();
