'use strict';

/**
 * Instance Forecasting Service - PRD Implementation
 * 
 * Integrates demand forecasting with shift instance generation:
 * - Forecast-driven instance creation
 * - Template override logic
 * - Confidence-based decisions
 * - Real-time forecast application
 */

const { 
  RotaShiftInstance,
  RotaShift,
  DemandForecast,
  sequelize
} = require('../../data/models');
const demandForecastService = require('./demandForecastService');
const { NotFoundError, ValidationError } = require('../../common/errors');
const { Op } = require('sequelize');
const moment = require('moment');

class InstanceForecastingService {

  /**
   * Generate instances with forecast integration
   * @param {Object} generationRequest - Instance generation request
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Generation results with forecast integration
   */
  async generateInstancesWithForecasting(generationRequest, tenantContext) {
    const {
      scheduleId,
      templateIds,
      startDate,
      endDate,
      enableForecastOverride = true,
      minConfidenceThreshold = 70,
      fallbackToTemplate = true
    } = generationRequest;

    const transaction = await sequelize.transaction();

    try {
      // 1. Get templates
      const templates = await RotaShift.findAll({
        where: {
          id: { [Op.in]: templateIds },
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId,
          isActive: true
        },
        transaction
      });

      if (templates.length === 0) {
        throw new ValidationError('No valid templates found');
      }

      // 2. Get relevant forecasts if enabled
      let forecastLookup = {};
      if (enableForecastOverride) {
        const departmentIds = [...new Set(templates.map(t => t.departmentId))];
        forecastLookup = await this.buildForecastLookup(
          departmentIds,
          startDate,
          endDate,
          minConfidenceThreshold,
          tenantContext
        );
      }

      // 3. Generate instances with forecast integration
      const instanceResults = await this.createInstancesWithForecastData(
        scheduleId,
        templates,
        startDate,
        endDate,
        forecastLookup,
        enableForecastOverride,
        fallbackToTemplate,
        tenantContext,
        transaction
      );

      await transaction.commit();

      return {
        instances: instanceResults.instances,
        summary: {
          totalInstances: instanceResults.instances.length,
          forecastOverrides: instanceResults.forecastOverrides,
          templateDefaults: instanceResults.templateDefaults,
          averageConfidence: instanceResults.averageConfidence,
          forecastCoverage: this.calculateForecastCoverage(instanceResults)
        },
        forecastMetadata: {
          enabledForecastOverride: enableForecastOverride,
          minConfidenceThreshold,
          totalForecastsUsed: Object.keys(forecastLookup).length
        }
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Update instance requirements based on latest forecasts
   * @param {Array} instanceIds - Instance IDs to update
   * @param {Object} updateOptions - Update options
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Update results
   */
  async updateInstancesWithLatestForecasts(instanceIds, updateOptions, tenantContext) {
    const {
      minConfidenceThreshold = 70,
      onlyIfHigherConfidence = true,
      preserveManualOverrides = true
    } = updateOptions;

    const transaction = await sequelize.transaction();

    try {
      // Get instances with their templates
      const instances = await RotaShiftInstance.findAll({
        where: {
          id: { [Op.in]: instanceIds }
        },
        include: [
          {
            model: RotaShift,
            as: 'rotaShift',
            where: {
              companyId: tenantContext.companyId,
              // businessUnitId: tenantContext.businessUnitId
            }
          }
        ],
        transaction
      });

      const updateResults = {
        updated: 0,
        skipped: 0,
        errors: [],
        details: []
      };

      for (const instance of instances) {
        try {
          // Skip manual overrides if preservation is enabled
          if (preserveManualOverrides && instance.overrideReason === 'manual_override') {
            updateResults.skipped++;
            updateResults.details.push({
              instanceId: instance.id,
              action: 'skipped',
              reason: 'manual_override_preserved'
            });
            continue;
          }

          // Find latest forecast for this instance
          const forecast = await this.findBestForecastForInstance(
            instance,
            minConfidenceThreshold,
            tenantContext
          );

          if (!forecast) {
            updateResults.skipped++;
            updateResults.details.push({
              instanceId: instance.id,
              action: 'skipped',
              reason: 'no_suitable_forecast'
            });
            continue;
          }

          // Check if forecast confidence is higher than current
          if (onlyIfHigherConfidence && instance.forecastData?.confidence >= forecast.confidence) {
            updateResults.skipped++;
            updateResults.details.push({
              instanceId: instance.id,
              action: 'skipped',
              reason: 'lower_confidence'
            });
            continue;
          }

          // Update instance with forecast data
          await instance.update({
            actualRequiredCount: forecast.predictedRequirement,
            overrideReason: 'forecast_prediction',
            forecastData: {
              forecastId: forecast.id,
              confidence: forecast.confidence,
              forecastType: forecast.forecastType,
              appliedAt: new Date(),
              previousRequirement: instance.actualRequiredCount
            },
            notes: `Updated with ${forecast.forecastType} forecast (${forecast.confidence}% confidence)`
          }, { transaction });

          updateResults.updated++;
          updateResults.details.push({
            instanceId: instance.id,
            action: 'updated',
            forecastId: forecast.id,
            previousRequirement: instance.actualRequiredCount,
            newRequirement: forecast.predictedRequirement,
            confidence: forecast.confidence
          });

        } catch (error) {
          updateResults.errors.push({
            instanceId: instance.id,
            error: error.message
          });
        }
      }

      await transaction.commit();
      return updateResults;

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get forecast recommendations for instances
   * @param {Array} instanceIds - Instance IDs
   * @param {Object} tenantContext - Tenant context
   * @returns {Array} Forecast recommendations
   */
  async getForecastRecommendations(instanceIds, tenantContext) {
    const instances = await RotaShiftInstance.findAll({
      where: {
        id: { [Op.in]: instanceIds }
      },
      include: [
        {
          model: RotaShift,
          as: 'rotaShift',
          where: {
            companyId: tenantContext.companyId,
          }
        }
      ]
    });

    const recommendations = [];

    for (const instance of instances) {
      const forecast = await this.findBestForecastForInstance(instance, 0, tenantContext);
      
      if (forecast) {
        const currentRequirement = instance.actualRequiredCount;
        const forecastRequirement = forecast.predictedRequirement;
        const variance = forecastRequirement - currentRequirement;
        
        recommendations.push({
          instanceId: instance.id,
          currentRequirement,
          forecastRequirement,
          variance,
          confidence: forecast.confidence,
          forecastType: forecast.forecastType,
          recommendation: this.generateRecommendationText(variance, forecast.confidence),
          shouldApply: Math.abs(variance) > 0 && forecast.confidence >= 70
        });
      } else {
        recommendations.push({
          instanceId: instance.id,
          currentRequirement: instance.actualRequiredCount,
          forecastRequirement: null,
          variance: 0,
          confidence: 0,
          forecastType: null,
          recommendation: 'No forecast available',
          shouldApply: false
        });
      }
    }

    return recommendations;
  }

  // Helper Methods

  /**
   * Build forecast lookup for quick access
   * @param {Array} departmentIds - Department IDs
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {number} minConfidence - Minimum confidence threshold
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Forecast lookup
   */
  async buildForecastLookup(departmentIds, startDate, endDate, minConfidence, tenantContext) {
    const forecasts = await demandForecastService.getActiveForecasts({
      departmentId: { [Op.in]: departmentIds },
      startDate,
      endDate,
      minConfidence
    }, tenantContext);

    const lookup = {};

    forecasts.forEach(forecast => {
      const key = this.generateForecastKey(
        forecast.departmentId,
        forecast.forecastDate,
        forecast.startTime,
        forecast.endTime
      );
      
      // Keep the highest confidence forecast for each key
      if (!lookup[key] || lookup[key].confidence < forecast.confidence) {
        lookup[key] = forecast;
      }
    });

    return lookup;
  }

  /**
   * Create instances with forecast data integration
   * @param {number} scheduleId - Schedule ID
   * @param {Array} templates - Template objects
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {Object} forecastLookup - Forecast lookup
   * @param {boolean} enableForecastOverride - Enable forecast override
   * @param {boolean} fallbackToTemplate - Fallback to template
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Creation results
   */
  async createInstancesWithForecastData(scheduleId, templates, startDate, endDate, forecastLookup, enableForecastOverride, fallbackToTemplate, tenantContext, transaction) {
    const results = {
      instances: [],
      forecastOverrides: 0,
      templateDefaults: 0,
      totalConfidence: 0,
      confidenceCount: 0
    };

    const start = moment(startDate);
    const end = moment(endDate);

    for (let date = start.clone(); date.isSameOrBefore(end); date.add(1, 'day')) {
      const currentDate = date.format('YYYY-MM-DD');

      for (const template of templates) {
        // Check if instance already exists
        const existingInstance = await RotaShiftInstance.findOne({
          where: {
            scheduleId,
            rotaShiftId: template.id,
            date: currentDate
          },
          transaction
        });

        if (existingInstance) {
          continue; // Skip existing instances
        }

        // Look for forecast data
        const forecastKey = this.generateForecastKey(
          template.departmentId,
          currentDate,
          template.startTime,
          template.endTime
        );

        let actualRequiredCount = template.baseRequiredCount;
        let overrideReason = 'template_default';
        let forecastData = null;
        let notes = 'Generated from template';

        // Apply forecast if available and enabled
        if (enableForecastOverride && forecastLookup[forecastKey]) {
          const forecast = forecastLookup[forecastKey];
          
          actualRequiredCount = forecast.predictedRequirement;
          overrideReason = 'forecast_prediction';
          forecastData = {
            forecastId: forecast.id,
            confidence: forecast.confidence,
            forecastType: forecast.forecastType,
            appliedAt: new Date()
          };
          notes = `Generated with ${forecast.forecastType} forecast (${forecast.confidence}% confidence)`;
          
          results.forecastOverrides++;
          results.totalConfidence += forecast.confidence;
          results.confidenceCount++;
        } else {
          results.templateDefaults++;
        }

        // Create instance
        const instance = await RotaShiftInstance.create({
          scheduleId,
          rotaShiftId: template.id,
          date: currentDate,
          actualRequiredCount,
          overrideReason,
          forecastData,
          notes,
          createdById: tenantContext.userId
        }, { transaction });

        results.instances.push(instance);
      }
    }

    // Calculate average confidence
    results.averageConfidence = results.confidenceCount > 0 ? 
      Math.round(results.totalConfidence / results.confidenceCount) : 0;

    return results;
  }

  /**
   * Find best forecast for instance
   * @param {Object} instance - Instance object
   * @param {number} minConfidence - Minimum confidence
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Best forecast
   */
  async findBestForecastForInstance(instance, minConfidence, tenantContext) {
    const forecasts = await DemandForecast.findAll({
      where: {
        companyId: tenantContext.companyId,
        departmentId: instance.rotaShift.departmentId,
        forecastDate: instance.date,
        startTime: instance.rotaShift.startTime,
        endTime: instance.rotaShift.endTime,
        status: 'active',
        confidence: { [Op.gte]: minConfidence }
      },
      order: [['confidence', 'DESC'], ['generatedAt', 'DESC']]
    });

    return forecasts.length > 0 ? forecasts[0] : null;
  }

  /**
   * Generate forecast lookup key
   * @param {number} departmentId - Department ID
   * @param {string} date - Date
   * @param {string} startTime - Start time
   * @param {string} endTime - End time
   * @returns {string} Lookup key
   */
  generateForecastKey(departmentId, date, startTime, endTime) {
    return `${departmentId}_${date}_${startTime}_${endTime}`;
  }

  /**
   * Calculate forecast coverage percentage
   * @param {Object} results - Instance results
   * @returns {number} Coverage percentage
   */
  calculateForecastCoverage(results) {
    const totalInstances = results.instances.length;
    if (totalInstances === 0) return 0;
    
    return Math.round((results.forecastOverrides / totalInstances) * 100);
  }

  /**
   * Generate recommendation text
   * @param {number} variance - Requirement variance
   * @param {number} confidence - Forecast confidence
   * @returns {string} Recommendation text
   */
  generateRecommendationText(variance, confidence) {
    if (variance === 0) {
      return 'Forecast matches current requirement';
    }
    
    const direction = variance > 0 ? 'increase' : 'decrease';
    const amount = Math.abs(variance);
    const confidenceText = confidence >= 80 ? 'high' : confidence >= 60 ? 'medium' : 'low';
    
    return `Recommend ${direction} by ${amount} staff (${confidenceText} confidence)`;
  }
}

module.exports = new InstanceForecastingService();
