'use strict';

/**
 * Rota Shift Instance Service - PRD Implementation
 * 
 * <PERSON>les shift instance business logic according to PRD specifications:
 * - Instance management (date-specific instances from templates)
 * - Requirement override functionality
 * - Employee availability checking
 * - Instance-level analytics and reporting
 */

const {
  RotaShiftInstance,
  RotaShift,
  ShiftAssignment,
  RotaSchedule,
  Employee,
  Department,
  Designation
} = require('../../data/models');
const { NotFoundError, ValidationError, ConflictError } = require('../../common/errors');
const { Op } = require('sequelize');
const sequelize = require('../../data/models').sequelize;
const moment = require('moment');

class RotaShiftInstanceService {

  /**
   * Get all shift instances with advanced filtering
   * @param {Object} filters - Filter options
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Instances with pagination
   */
  async getAllInstances(filters, tenantContext) {
    const {
      page = 1,
      limit = 10,
      scheduleId,
      templateId,
      date,
      startDate,
      endDate,
      departmentId,
      status,
      search,
      sortBy = 'date',
      sortOrder = 'ASC'
    } = filters;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // Apply filters
    if (scheduleId) whereClause.scheduleId = scheduleId;
    if (templateId) whereClause.rotaShiftId = templateId;
    if (date) whereClause.date = date;
    if (startDate && endDate) {
      whereClause.date = { [Op.between]: [startDate, endDate] };
    } else if (startDate) {
      whereClause.date = { [Op.gte]: startDate };
    } else if (endDate) {
      whereClause.date = { [Op.lte]: endDate };
    }

    const includeClause = [
      {
        model: RotaShift,
        as: 'rotaShift',
        include: [
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name'],
            where: departmentId ? { id: departmentId } : undefined
          },
          {
            model: Designation,
            as: 'designation',
            attributes: ['id', 'name'],
            through: { attributes: ['requiredCount'] },
            required: false
          }
        ]
      },
      {
        model: RotaSchedule,
        as: 'schedule',
        attributes: ['id', 'name', 'status'],
        where: {
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        }
      }
    ];

    // Add search functionality
    if (search) {
      includeClause[0].where = {
        [Op.or]: [
          { name: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } }
        ]
      };
    }

    const { count, rows: instances } = await RotaShiftInstance.findAndCountAll({
      where: whereClause,
      include: includeClause,
      order: [[sortBy, sortOrder]],
      limit,
      offset,
      distinct: true
    });

    return {
      instances,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
        hasNext: page < Math.ceil(count / limit),
        hasPrev: page > 1
      }
    };
  }

  /**
   * Get shift instance by ID with optional detailed information
   * @param {number} id - Instance ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} options - Additional options
   * @returns {Object} Instance details
   */
  async getInstanceById(id, tenantContext, options = {}) {
    const includeClause = [];

    // Include template information
    if (options.includeTemplate !== false) {
      includeClause.push({
        model: RotaShift,
        as: 'rotaShift',
        include: [
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name']
          },

        ]
      });
    }

    // Include schedule information
    if (options.includeSchedule) {
      includeClause.push({
        model: RotaSchedule,
        as: 'schedule',
        attributes: ['id', 'name', 'status', 'startDate', 'endDate']
      });
    }

    // Include assignments
    if (options.includeAssignments) {
      includeClause.push({
        model: ShiftAssignment,
        as: 'assignments',
        include: [
          {
            model: Employee,
            as: 'employee',
            attributes: ['id', 'firstName', 'lastName', 'contactEmail', 'employeeId']
          }
        ]
      });
    }

    const instance = await RotaShiftInstance.findOne({
      where: { id },
      include: includeClause
    });

    if (!instance) {
      throw new NotFoundError('Shift instance not found');
    }

    // Verify tenant access through schedule
    if (instance.schedule) {
      if (instance.schedule.companyId !== tenantContext.companyId ||
          instance.schedule.businessUnitId !== tenantContext.businessUnitId) {
        throw new NotFoundError('Shift instance not found');
      }
    }

    return instance;
  }

  /**
   * Create new shift instance
   * @param {Object} instanceData - Instance data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Created instance
   */
  async createInstance(instanceData, tenantContext) {
    // Validate instance data
    await this.validateInstanceData(instanceData, tenantContext);

    const instance = await RotaShiftInstance.create({
      ...instanceData,
      status: 'open'
    });

    // Return instance with associations
    return await this.getInstanceById(instance.id, tenantContext, {
      includeTemplate: true,
      includeSchedule: true
    });
  }

  /**
   * Update shift instance
   * @param {number} id - Instance ID
   * @param {Object} updateData - Update data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated instance
   */
  async updateInstance(id, updateData, tenantContext) {
    const instance = await this.getInstanceById(id, tenantContext);

    // Check if instance can be modified
    await this.validateInstanceModification(instance, tenantContext);

    // Validate update data
    await this.validateInstanceData(updateData, tenantContext, id);

    await instance.update(updateData);

    return await this.getInstanceById(id, tenantContext, {
      includeTemplate: true,
      includeSchedule: true
    });
  }

  /**
   * Delete shift instance
   * @param {number} id - Instance ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} options - Delete options
   */
  async deleteInstance(id, tenantContext, options = {}) {
    const transaction = await sequelize.transaction();

    try {
      const instance = await this.getInstanceById(id, tenantContext, {
        includeAssignments: true
      });

      // Check if instance can be deleted
      await this.validateInstanceModification(instance, tenantContext);

      // Handle assignments
      if (instance.assignments && instance.assignments.length > 0) {
        if (options.removeAssignments) {
          await ShiftAssignment.destroy({
            where: { shiftInstanceId: id },
            transaction
          });
        } else {
          throw new ValidationError('Cannot delete instance with existing assignments. Use removeAssignments=true to force deletion.');
        }
      }

      await instance.destroy({ transaction });
      await transaction.commit();

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Override instance required count
   * @param {number} id - Instance ID
   * @param {Object} overrideData - Override data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated instance
   */
  async overrideRequiredCount(id, overrideData, tenantContext) {
    const instance = await this.getInstanceById(id, tenantContext);

    // Check if instance can be modified
    await this.validateInstanceModification(instance, tenantContext);

    const { requiredCount, reason, overriddenBy, overriddenAt } = overrideData;

    if (!requiredCount || requiredCount < 0 || requiredCount > 50) {
      throw new ValidationError('Required count must be between 0 and 50');
    }

    if (!reason || reason.trim().length < 10) {
      throw new ValidationError('Override reason must be at least 10 characters');
    }

    await instance.update({
      actualRequiredCount: requiredCount,
      overrideReason: reason,
      overriddenBy,
      overriddenAt,
      updatedById: tenantContext.userId
    });

    return await this.getInstanceById(id, tenantContext, {
      includeTemplate: true,
      includeSchedule: true
    });
  }

  /**
   * Get available employees for instance
   * @param {number} id - Instance ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} options - Options
   * @returns {Array} Available employees
   */
  async getAvailableEmployees(id, tenantContext, options = {}) {
    const instance = await this.getInstanceById(id, tenantContext, {
      includeTemplate: true
    });

    const { sortBy = 'priority', limit = 50, includeUnavailable = false } = options;

    // Get employees matching designation and department
    const whereClause = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      status: 'active'
    };

    if (instance.rotaShift.designationId) {
      whereClause.designationId = instance.rotaShift.designationId;
    }

    if (instance.rotaShift.departmentId) {
      whereClause.departmentId = instance.rotaShift.departmentId;
    }

    // Build order clause based on sortBy parameter
    let orderClause;
    if (sortBy === 'availability_score' || sortBy === 'priority') {
      // For availability_score or priority, use firstName as fallback since these columns don't exist
      orderClause = [['firstName', 'ASC']];
    } else {
      // Use the provided sortBy field (firstName, lastName)
      orderClause = [[sortBy, 'ASC']];
    }

    const employees = await Employee.findAll({
      where: whereClause,
      attributes: ['id', 'firstName', 'lastName', 'contactEmail', 'employeeId'],
      order: orderClause,
      limit
    });

    // Check availability for each employee
    const availableEmployees = [];
    
    for (const employee of employees) {
      const availability = await this.checkEmployeeAvailability(employee.id, instance.date, tenantContext);
      
      if (availability.isAvailable || includeUnavailable) {
        // Calculate availability score (higher is better)
        let availabilityScore = 0;
        if (availability.isAvailable) {
          availabilityScore = 100; // Base score for being available
          // Reduce score based on existing assignments (simple logic)
          availabilityScore -= availability.existingAssignments * 10;
        }

        availableEmployees.push({
          ...employee.toJSON(),
          availability,
          availabilityScore
        });
      }
    }

    // Sort by availability score if requested
    if (sortBy === 'availability_score') {
      availableEmployees.sort((a, b) => b.availabilityScore - a.availabilityScore);
    }

    return availableEmployees;
  }

  /**
   * Get instance assignments
   * @param {number} id - Instance ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} options - Options
   * @returns {Array} Instance assignments
   */
  async getInstanceAssignments(id, tenantContext, options = {}) {
    const whereClause = { shiftInstanceId: id };

    if (options.status) {
      whereClause.status = options.status;
    }

    const includeClause = [];

    if (options.includeEmployee !== false) {
      includeClause.push({
        model: Employee,
        as: 'employee',
        attributes: ['id', 'firstName', 'lastName', 'email', 'employeeId']
      });
    }

    const assignments = await ShiftAssignment.findAll({
      where: whereClause,
      include: includeClause,
      order: [['assignedAt', 'ASC']]
    });

    return assignments;
  }

  /**
   * Get instance statistics
   * @param {number} id - Instance ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Instance statistics
   */
  async getInstanceStatistics(id, tenantContext) {
    const instance = await this.getInstanceById(id, tenantContext, {
      includeTemplate: true,
      includeAssignments: true
    });

    const requiredCount = instance.actualRequiredCount || instance.rotaShift?.baseRequiredCount || 0;
    const assignedCount = instance.assignments?.length || 0;
    const confirmedCount = instance.assignments?.filter(a => a.status === 'confirmed').length || 0;
    const completedCount = instance.assignments?.filter(a => a.status === 'completed').length || 0;
    const absentCount = instance.assignments?.filter(a => a.status === 'absent').length || 0;

    const staffingPercentage = requiredCount > 0 ? Math.round((assignedCount / requiredCount) * 100) : 0;
    const isFullyStaffed = assignedCount >= requiredCount;
    const isOverstaffed = assignedCount > requiredCount;

    return {
      requiredCount,
      assignedCount,
      confirmedCount,
      completedCount,
      noShowCount,
      staffingPercentage,
      isFullyStaffed,
      isOverstaffed,
      shortfall: Math.max(0, requiredCount - assignedCount),
      excess: Math.max(0, assignedCount - requiredCount)
    };
  }

  /**
   * Bulk create instances from templates
   * @param {Object} bulkData - Bulk creation data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Bulk creation results
   */
  async bulkCreateInstances(bulkData, tenantContext) {
    const { scheduleId, templateIds, dateRange, overrides, createdById } = bulkData;
    const { startDate, endDate } = dateRange;

    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      created: []
    };

    // Validate schedule exists
    const schedule = await RotaSchedule.findOne({
      where: {
        id: scheduleId,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      }
    });

    if (!schedule) {
      throw new NotFoundError('Schedule not found');
    }

    // Get templates
    const templates = await RotaShift.findAll({
      where: {
        id: { [Op.in]: templateIds },
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        isActive: true
      }
    });

    if (templates.length !== templateIds.length) {
      throw new ValidationError('Some templates not found or inactive');
    }

    // Generate instances for date range
    const start = moment(startDate);
    const end = moment(endDate);

    for (let date = start.clone(); date.isSameOrBefore(end); date.add(1, 'day')) {
      const currentDate = date.format('YYYY-MM-DD');

      for (const template of templates) {
        try {
          // Check if instance already exists
          const existingInstance = await RotaShiftInstance.findOne({
            where: {
              scheduleId,
              rotaShiftId: template.id,
              date: currentDate
            }
          });

          if (existingInstance) {
            results.failed++;
            results.errors.push({
              template: template.name,
              date: currentDate,
              error: 'Instance already exists'
            });
            continue;
          }

          // Apply overrides if any
          const instanceData = {
            scheduleId,
            date: currentDate,
            sourceType: 'template',
            sourceId: template.id,
            actualRequiredCount: overrides[template.id]?.requiredCount || template.baseRequiredCount,
            notes: overrides[template.id]?.notes || null,
            createdById
          };

          const instance = await RotaShiftInstance.create(instanceData);
          results.created.push(instance);
          results.successful++;

        } catch (error) {
          results.failed++;
          results.errors.push({
            template: template.name,
            date: currentDate,
            error: error.message
          });
        }
      }
    }

    return results;
  }

  /**
   * Bulk update instances
   * @param {Array} updates - Updates to apply
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Bulk update results
   */
  async bulkUpdateInstances(updates, tenantContext) {
    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      updated: []
    };

    for (let i = 0; i < updates.length; i++) {
      try {
        const { id, ...updateData } = updates[i];
        const instance = await this.updateInstance(id, updateData, tenantContext);
        results.updated.push(instance);
        results.successful++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          index: i,
          update: updates[i],
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Validate instance data
   * @param {Object} instanceData - Instance data to validate
   * @param {Object} tenantContext - Tenant context
   * @param {number} excludeId - ID to exclude from validation (for updates)
   */
  async validateInstanceData(instanceData, tenantContext, excludeId = null) {
    const { scheduleId, rotaShiftId, date, actualRequiredCount } = instanceData;

    // Required fields validation
    if (!scheduleId || !rotaShiftId || !date) {
      throw new ValidationError('Schedule ID, template ID, and date are required');
    }

    // Date validation
    if (!moment(date).isValid()) {
      throw new ValidationError('Invalid date format');
    }

    // Required count validation
    if (actualRequiredCount !== undefined) {
      if (actualRequiredCount < 0 || actualRequiredCount > 50) {
        throw new ValidationError('Required count must be between 0 and 50');
      }
    }

    // Validate schedule exists and belongs to tenant
    const schedule = await RotaSchedule.findOne({
      where: {
        id: scheduleId,
        companyId: tenantContext.companyId,
      }
    });

    if (!schedule) {
      throw new ValidationError('Invalid schedule ID');
    }

    // Validate template exists and belongs to tenant
    const template = await RotaShift.findOne({
      where: {
        id: rotaShiftId,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        isActive: true
      }
    });

    if (!template) {
      throw new ValidationError('Invalid template ID');
    }

    // Check for duplicate instances
    const whereClause = {
      scheduleId,
      rotaShiftId,
      date
    };

    if (excludeId) {
      whereClause.id = { [Op.ne]: excludeId };
    }

    const existingInstance = await RotaShiftInstance.findOne({ where: whereClause });
    if (existingInstance) {
      throw new ValidationError('Instance already exists for this template and date');
    }
  }

  /**
   * Validate instance modification permissions
   * @param {Object} instance - Instance object
   * @param {Object} tenantContext - Tenant context
   */
  async validateInstanceModification(instance, tenantContext) {
    // Check if schedule is published and locked
    if (instance.schedule && instance.schedule.status === 'published') {
      const isManager = tenantContext.userRole === 'manager' || tenantContext.userRole === 'admin';
      if (!isManager) {
        throw new ValidationError('Cannot modify instances in published schedule without manager permissions');
      }
    }

    // Check if schedule is archived
    if (instance.schedule && instance.schedule.status === 'archived') {
      throw new ValidationError('Cannot modify instances in archived schedule');
    }
  }

  /**
   * Check employee availability for specific date
   * @param {number} employeeId - Employee ID
   * @param {string} date - Date string
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Availability information
   */
  async checkEmployeeAvailability(employeeId, date, tenantContext) {
    // Check for existing assignments on the same date
    const existingAssignments = await ShiftAssignment.count({
      where: {
        employeeId,
        status: { [Op.notIn]: ['cancelled', 'absent'] }
      },
      include: [
        {
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: { date }
        }
      ]
    });

    const isAvailable = existingAssignments === 0;
    const conflicts = existingAssignments;

    return {
      isAvailable,
      conflicts,
      reason: isAvailable ? null : `Employee has ${conflicts} existing assignment(s) on this date`
    };
  }
}

module.exports = new RotaShiftInstanceService();
