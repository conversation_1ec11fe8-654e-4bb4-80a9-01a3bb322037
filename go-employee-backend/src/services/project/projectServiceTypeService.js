'use strict';

const {
  Project,
  ServiceType,
  ProjectServiceType,
  sequelize
} = require('../../data/models');
const { NotFoundError, BadRequestError } = require('../../common/errors');
const { Op } = require('sequelize');
const logger = require('../../common/logger');

class ProjectServiceTypeService {
  /**
   * Associate service types with a project
   * @param {number} projectId - Project ID
   * @param {Array<number>} serviceTypeIds - Array of service type IDs
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<boolean>} Success status
   */
  async associateServiceTypes(projectId, serviceTypeIds, tenantContext) {
    const transaction = await sequelize.transaction();
    
    try {
      // Check if project exists and belongs to the company
      const project = await Project.findOne({
        where: {
          id: projectId,
          companyId: tenantContext.companyId
        },
        transaction
      });

      if (!project) {
        await transaction.rollback();
        throw new NotFoundError('Project not found');
      }

      // Check if all service types exist and belong to the company
      const serviceTypes = await ServiceType.findAll({
        where: {
          id: { [Op.in]: serviceTypeIds },
          companyId: tenantContext.companyId
        },
        transaction
      });

      if (serviceTypes.length !== serviceTypeIds.length) {
        await transaction.rollback();
        throw new BadRequestError('One or more service types not found');
      }

      // Create associations
      const associations = serviceTypeIds.map(serviceTypeId => ({
        projectId,
        serviceTypeId,
        companyId: tenantContext.companyId,
        createdBy: tenantContext.employeeId,
        updatedBy: tenantContext.employeeId
      }));

      // Use bulkCreate with updateOnDuplicate to handle existing associations
      await ProjectServiceType.bulkCreate(associations, {
        updateOnDuplicate: ['updatedBy', 'updatedAt'],
        transaction
      });

      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      logger.error(`Error associating service types with project ${projectId}:`, error);
      throw error;
    }
  }

  /**
   * Remove service type association from a project
   * @param {number} projectId - Project ID
   * @param {number} serviceTypeId - Service type ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<boolean>} Success status
   */
  async removeServiceTypeAssociation(projectId, serviceTypeId, tenantContext) {
    const transaction = await sequelize.transaction();
    
    try {
      // Check if project exists and belongs to the company
      const project = await Project.findOne({
        where: {
          id: projectId,
          companyId: tenantContext.companyId
        },
        transaction
      });

      if (!project) {
        await transaction.rollback();
        throw new NotFoundError('Project not found');
      }

      // Check if association exists
      const association = await ProjectServiceType.findOne({
        where: {
          projectId,
          serviceTypeId
        },
        transaction
      });

      if (!association) {
        await transaction.rollback();
        throw new NotFoundError('Service type is not associated with this project');
      }

      // Remove association (soft delete)
      await association.destroy({ transaction });

      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      logger.error(`Error removing service type ${serviceTypeId} from project ${projectId}:`, error);
      throw error;
    }
  }

  /**
   * Get service types for a project
   * @param {number} projectId - Project ID
   * @param {Object} pagination - Pagination parameters
   * @param {Object} sorting - Sorting parameters
   * @param {Object} tenantContext - Tenant context
   * @returns {Promise<Object>} Service types and count
   */
  async getServiceTypesByProject(projectId, pagination, sorting, tenantContext) {
    // Check if project exists and belongs to the company
    const project = await Project.findOne({
      where: {
        id: projectId,
        companyId: tenantContext.companyId
      }
    });

    if (!project) {
      throw new NotFoundError('Project not found');
    }

    // Get service types associated with the project
    const { count, rows: serviceTypes } = await ServiceType.findAndCountAll({
      include: [
        {
          model: ProjectServiceType,
          as: 'projectServiceTypes',
          where: { projectId },
          required: true
        }
      ],
      where: {
        companyId: tenantContext.companyId
      },
      order: [[sorting.field, sorting.order]],
      limit: pagination.limit,
      offset: pagination.offset
    });

    return {
      serviceTypes,
      total: count
    };
  }
}

module.exports = new ProjectServiceTypeService();