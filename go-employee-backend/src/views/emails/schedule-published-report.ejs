<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📅 Schedule Published Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .schedule-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 4px solid #667eea;
        }
        .schedule-info h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .info-item {
            display: flex;
            align-items: center;
        }
        .info-label {
            font-weight: 600;
            color: #6c757d;
            margin-right: 8px;
            min-width: 120px;
        }
        .info-value {
            color: #495057;
            font-weight: 500;
        }
        .stats-section {
            margin: 25px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .stat-card h4 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
        }
        .stat-card p {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .attachment-info {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }
        .attachment-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .attachment-info p {
            margin: 5px 0;
            color: #424242;
        }
        .department-breakdown {
            margin: 25px 0;
        }
        .department-breakdown h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        .dept-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .dept-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 3px solid #28a745;
        }
        .dept-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        .dept-stats {
            font-size: 14px;
            color: #6c757d;
        }
        .action-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .action-section h3 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        .action-list {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }
        .action-list li {
            padding: 5px 0;
            color: #856404;
        }
        .action-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        .footer p {
            margin: 5px 0;
            color: #6c757d;
            font-size: 14px;
        }
        .warning-section {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .warning-section h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        .warning-list {
            margin: 0;
            padding-left: 20px;
            color: #856404;
        }
        @media (max-width: 600px) {
            .info-grid, .stats-grid, .dept-list {
                grid-template-columns: 1fr;
            }
            .container {
                margin: 10px;
            }
            .header, .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 Schedule Published Successfully</h1>
            <p>Complete Schedule Report for <strong><%= schedule.name %></strong></p>
            <p>Published on <%= new Date(schedule.publishedAt).toLocaleDateString('en-IN', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                timeZone: 'Asia/Kolkata'
            }) %></p>
        </div>

        <div class="content">
            <div class="schedule-info">
                <h3>📋 Schedule Information</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">📅 Schedule Name:</span>
                        <span class="info-value"><%= schedule.name %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">📆 Period:</span>
                        <span class="info-value"><%= schedule.startDate %> to <%= schedule.endDate %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">🏢 Business Unit:</span>
                        <span class="info-value"><%= businessUnit.name %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">✅ Status:</span>
                        <span class="info-value" style="color: #28a745; font-weight: 600;">Published</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">👤 Published By:</span>
                        <span class="info-value"><%= publishedBy.firstName %> <%= publishedBy.lastName %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">🕐 Published At:</span>
                        <span class="info-value"><%= new Date(schedule.publishedAt).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' }) %> IST</span>
                    </div>
                </div>
            </div>

            <div class="stats-section">
                <h3>📊 Schedule Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h4><%= statistics.totalShifts %></h4>
                        <p>Total Shifts</p>
                    </div>
                    <div class="stat-card success">
                        <h4><%= statistics.totalAssignments %></h4>
                        <p>Employee Assignments</p>
                    </div>
                    <div class="stat-card warning">
                        <h4><%= statistics.coveragePercentage %>%</h4>
                        <p>Coverage Percentage</p>
                    </div>
                    <div class="stat-card">
                        <h4><%= Object.keys(statistics.departmentBreakdown || {}).length %></h4>
                        <p>Departments</p>
                    </div>
                </div>
            </div>

            <% if (statistics.departmentBreakdown && Object.keys(statistics.departmentBreakdown).length > 0) { %>
            <div class="department-breakdown">
                <h3>🏢 Department-wise Breakdown</h3>
                <div class="dept-list">
                    <% Object.values(statistics.departmentBreakdown).forEach(function(dept) { %>
                    <div class="dept-item">
                        <div class="dept-name"><%= dept.name %></div>
                        <div class="dept-stats">
                            Shifts: <%= dept.shifts %> | Assignments: <%= dept.assignments %> | Required: <%= dept.required %>
                        </div>
                    </div>
                    <% }); %>
                </div>
            </div>
            <% } %>

            <% if (validationResults && validationResults.warnings && validationResults.warnings.length > 0) { %>
            <div class="warning-section">
                <h4>⚠️ Important Notices</h4>
                <ul class="warning-list">
                    <% validationResults.warnings.forEach(function(warning) { %>
                    <li><%= warning %></li>
                    <% }); %>
                </ul>
            </div>
            <% } %>

            <div class="attachment-info">
                <h3>📎 Excel Report Attached</h3>
                <p>📊 Complete schedule details with all employee assignments are attached as an Excel file.</p>
                <p>📋 The Excel file contains shift-wise employee assignments, timings, and department information.</p>
                <p>💾 File: <strong><%= excelFileName %></strong></p>
            </div>

            <div class="action-section">
                <h3>📋 Next Steps</h3>
                <ul class="action-list">
                    <li>Review the attached Excel report for complete schedule details</li>
                    <li>All assigned employees have been notified via email</li>
                    <li>Shift assignments are now locked to prevent unauthorized changes</li>
                    <li>Monitor attendance and coverage during the schedule period</li>
                    <li>Contact HR for any schedule modifications or concerns</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p><strong>📧 This is an automated notification from HRMS System</strong></p>
            <p>🕐 Generated at: <%= new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' }) %> IST</p>
            <p>🏢 <%= businessUnit.company.name %> - Human Resource Management System</p>
        </div>
    </div>
</body>
</html>
