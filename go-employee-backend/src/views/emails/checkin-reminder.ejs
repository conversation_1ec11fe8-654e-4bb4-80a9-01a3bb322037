<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check-in Reminder</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #ffc107;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #ffc107;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 16px;
        }
        .alert-box {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 25px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .alert-box h2 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .alert-box p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .info-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 4px solid #ffc107;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            align-items: center;
        }
        .info-row:last-child {
            margin-bottom: 0;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
            display: flex;
            align-items: center;
        }
        .info-value {
            color: #007bff;
            font-weight: 600;
            text-align: right;
        }
        .shift-info {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 25px 0;
        }
        .shift-info h3 {
            margin: 0 0 10px 0;
            font-size: 20px;
        }
        .shift-info p {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        .action-section {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
        }
        .action-section h3 {
            color: #1976d2;
            margin: 0 0 15px 0;
        }
        .action-section ul {
            margin: 0;
            padding-left: 20px;
        }
        .action-section li {
            margin-bottom: 8px;
            color: #424242;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
        }
        .footer p {
            margin: 5px 0;
            font-size: 14px;
        }
        .contact-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .contact-info strong {
            color: #856404;
        }
        @media (max-width: 600px) {
            .info-row {
                flex-direction: column;
                align-items: flex-start;
            }
            .info-row .info-value {
                margin-top: 5px;
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ Check-in Reminder</h1>
            <p>Daily Attendance Notification</p>
        </div>

        <div class="alert-box">
            <h2>🚨 Missing Check-in Detected</h2>
            <% if (typeof consecutiveDays !== 'undefined' && consecutiveDays > 1) { %>
                <p>You haven't checked in for <strong><%= consecutiveDays %> consecutive days</strong></p>
            <% } else { %>
                <p>We noticed you haven't checked in today</p>
            <% } %>
        </div>

        <div class="info-section">
            <div class="info-row">
                <span class="info-label">👤 Employee Name:</span>
                <span class="info-value"><%= employee.name %></span>
            </div>
            <div class="info-row">
                <span class="info-label">🆔 Employee Code:</span>
                <span class="info-value"><%= employee.employeeCode || 'N/A' %></span>
            </div>
            <div class="info-row">
                <span class="info-label">📅 Date:</span>
                <span class="info-value"><%= employee.date %></span>
            </div>
            <div class="info-row">
                <span class="info-label">⏰ Current Time:</span>
                <span class="info-value"><%= new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' }) %> IST</span>
            </div>
        </div>

        <div class="shift-info">
            <h3>📋 Your Shift Schedule</h3>
            <p><%= employee.shift %></p>
        </div>

        <div class="action-section">
            <h3>🎯 What You Need To Do:</h3>
            <ul>
                <li><strong>Check-in immediately</strong> if you're already at work</li>
                <li><strong>Contact your supervisor</strong> if you're running late</li>
                <li><strong>Apply for leave</strong> if you're unable to come to work</li>
                <li><strong>Update your attendance</strong> through the HRMS system</li>
            </ul>
        </div>

        <div class="contact-info">
            <strong>📞 Need Help?</strong><br>
            If you have already checked in or this is an error, please contact HR Or Reporting Manager immediately.<br>
            <!-- <strong>HR Department:</strong> <EMAIL> | <strong>Phone:</strong> +91-XXXX-XXXXXX -->
        </div>

        <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: center;">
            <strong>⚠️ Important Notice</strong><br>
            <% if (typeof consecutiveDays !== 'undefined' && consecutiveDays > 1) { %>
                You have missed check-in for <strong><%= consecutiveDays %> consecutive days</strong>. Regular attendance is mandatory as per company policy.
            <% } else { %>
                Regular attendance is mandatory as per company policy. Repeated missed check-ins may result in disciplinary action.
            <% } %>
        </div>

        <div class="footer">
            <p><strong>📧 This is an automated reminder from the HRMS system</strong></p>
            <!-- <p>⏰ Sent daily at 12:00 PM IST for missing check-ins</p> -->
            <p>🔄 Please ensure timely check-in to avoid future reminders</p>
            <p style="margin-top: 15px; font-size: 12px; color: #999;">
                This email was sent because our system detected that you haven't checked in today.<br>
                If you believe this is an error, please contact the HR department immediately.
            </p>
        </div>
    </div>
</body> 
</html>
