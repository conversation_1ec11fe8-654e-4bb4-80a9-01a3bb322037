<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Checkout Notification</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        
        .email-container {
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .info-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            flex: 1;
        }
        
        .info-value {
            font-weight: 500;
            color: #2c3e50;
            flex: 1;
            text-align: right;
        }
        
        .attendance-details {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .attendance-details h3 {
            margin-top: 0;
            color: #1976d2;
            font-size: 18px;
        }
        
        .time-badge {
            display: inline-block;
            background-color: #667eea;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .work-hours {
            background-color: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            margin: 20px 0;
        }
        
        .work-hours h3 {
            margin: 0 0 10px 0;
            color: #2e7d32;
            font-size: 20px;
        }
        
        .work-hours .hours {
            font-size: 24px;
            font-weight: bold;
            color: #1b5e20;
        }
        
        .alert-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .alert-box .alert-icon {
            font-size: 20px;
            margin-right: 10px;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }
        
        .footer p {
            margin: 5px 0;
        }
        
        .company-logo {
            max-width: 150px;
            margin-bottom: 10px;
        }
        
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .info-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .info-value {
                text-align: left;
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>🕐 Auto-Checkout Notification</h1>
            <p>Your attendance has been automatically processed</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                Hello <strong><%= employee.firstName %> <%= employee.lastName %></strong>,
            </div>
            
            <p>Your attendance has been automatically checked out by the system as you didn't manually check out within the grace period.</p>
            
            <div class="info-section">
                <div class="info-row">
                    <span class="info-label">👤 Employee ID:</span>
                    <span class="info-value"><%= employee.employeeId %></span>
                </div>
                <div class="info-row">
                    <span class="info-label">🏢 Company:</span>
                    <span class="info-value"><%= company.name %></span>
                </div>
                <div class="info-row">
                    <span class="info-label">🏬 Business Unit:</span>
                    <span class="info-value"><%= businessUnit.name %></span>
                </div>
                <div class="info-row">
                    <span class="info-label">📅 Date:</span>
                    <span class="info-value"><%= attendance.date %></span>
                </div>
            </div>
            
            <div class="attendance-details">
                <h3>📋 Attendance Details</h3>
                <div class="info-row">
                    <span class="info-label">⏰ Shift Timing:</span>
                    <span class="info-value"><%= shiftType.name %> (<%= shiftType.startTime %> - <%= shiftType.endTime %>)</span>
                </div>
                <div class="info-row">
                    <span class="info-label">🔓 Check-in Time:</span>
                    <span class="info-value"><span class="time-badge"><%= attendance.clockInTime %></span></span>
                </div>
                <div class="info-row">
                    <span class="info-label">🔒 Auto Check-out Time:</span>
                    <span class="info-value"><span class="time-badge"><%= attendance.clockOutTime %></span></span>
                </div>
                <div class="info-row">
                    <span class="info-label">💼 Work Mode:</span>
                    <span class="info-value"><%= attendance.modeOfWork || 'WFO' %></span>
                </div>
            </div>
            
            <div class="work-hours">
                <h3>⏱️ Total Work Hours</h3>
                <div class="hours"><%= attendance.workHours %> hours</div>
                <% if (attendance.overtimeHours && attendance.overtimeHours > 0) { %>
                <p style="margin: 10px 0 0 0; color: #ff9800;">
                    <strong>Overtime:</strong> <%= attendance.overtimeHours %> hours
                </p>
                <% } %>
            </div>
            
            <div class="alert-box">
                <span class="alert-icon">⚠️</span>
                <strong>Auto-Checkout Applied:</strong> 
                This checkout was automatically processed because you didn't manually check out after your shift ended. 
                The checkout time has been set to 2 hours before your shift end time as per company policy.
            </div>
            
            <p style="margin-top: 30px; color: #495057;">
                <strong>📝 Note:</strong> To avoid auto-checkout in the future, please remember to manually check out before the grace period expires.
            </p>
            
            <p style="color: #6c757d; font-size: 14px; margin-top: 20px;">
                If you have any questions about this attendance record, please contact your HR department.
            </p>
        </div>
        
        <div class="footer">
            <p><strong>📧 This is an automated notification </strong></p>
            <p>🕐 Auto-checkout processed at: <%= new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' }) %> IST</p>
            <p>🔄 Please ensure timely manual checkout to avoid future auto-checkouts</p>
            <p style="margin-top: 15px; font-size: 12px; color: #999;">
                This email was sent because our system automatically checked you out.<br>
                If you believe this is an error, please contact the HR department immediately.
            </p>
        </div>
    </div>
</body>
</html>
