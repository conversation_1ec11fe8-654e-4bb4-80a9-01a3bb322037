<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Not Check-in Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 16px;
        }
        .info-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .info-row:last-child {
            margin-bottom: 0;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        .info-value {
            color: #007bff;
            font-weight: 600;
        }
        .summary-stats {
            display: flex;
            justify-content: space-around;
            margin: 25px 0;
            text-align: center;
        }
        .stat-box {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 20px;
            border-radius: 10px;
            flex: 1;
            margin: 0 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        .table-container {
            overflow-x: auto;
            margin: 25px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        th {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
        }
        td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #e3f2fd;
        }
        .status-badge {
            background-color: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            display: inline-block;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
        }
        .footer p {
            margin: 5px 0;
            font-size: 14px;
        }
        .attachment-note {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .attachment-note strong {
            color: #0c5460;
        }
        @media (max-width: 600px) {
            .summary-stats {
                flex-direction: column;
            }
            .stat-box {
                margin: 10px 0;
            }
            .info-row {
                flex-direction: column;
            }
            .info-row .info-value {
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Daily Not Check-in Report</h1>
            <p>Automated Employee Attendance Monitoring</p>
        </div>

        <div class="info-section">
            <div class="info-row">
                <span class="info-label">🏢 Business Unit:</span>
                <span class="info-value"><%= businessUnit.name %></span>
            </div>
            <div class="info-row">
                <span class="info-label">📅 Report Date:</span>
                <span class="info-value"><%= date %></span>
            </div>
            <div class="info-row">
                <span class="info-label">⏰ Generated At:</span>
                <span class="info-value"><%= new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' }) %> IST</span>
            </div>
        </div>

        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-number"><%= employees.length %></div>
                <div class="stat-label">Employees Not Checked In</div>
            </div>
        </div>

        <% if (employees.length > 0) { %>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>👤 Employee Code</th>
                        <th>📝 Name</th>
                        <th>📧 Email</th>
                        <th>💼 Designation</th>
                        <th>🏢 Department</th>
                        <th>⏰ Shift</th>
                        <th>🚨 Status</th>
                    </tr>
                </thead>
                <tbody>
                    <% employees.forEach(function(employee) { %>
                    <tr>
                        <td><%= employee.employeeCode || 'N/A' %></td>
                        <td><strong><%= employee.name %></strong></td>
                        <td><%= employee.email || 'N/A' %></td>
                        <td><%= employee.designation || 'N/A' %></td>
                        <td><%= employee.department || 'N/A' %></td>
                        <td><%= employee.shift %></td>
                        <td><span class="status-badge"><%= employee.status %></span></td>
                    </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>

        <div class="attachment-note">
            <strong>📎 Excel Report Attached</strong><br>
            Please find the detailed Excel report attached to this email for further analysis and record keeping.
        </div>
        <% } else { %>
        <div style="text-align: center; padding: 40px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724;">
            <h3>🎉 Great News!</h3>
            <p>All employees in <strong><%= businessUnit.name %></strong> have checked in today.</p>
            <p>No attendance issues to report for <%= date %>.</p>
        </div>
        <% } %>

        <div class="footer">
            <p><strong>📧 This is an automated report generated </strong></p>
            <p>⏰ Report generated daily </p>
            <p>🔄 Next report will be sent tomorrow at the same time</p>
            <p style="margin-top: 15px; font-size: 12px; color: #999;">
                If you have any questions about this report, please contact the IT department.
            </p>
        </div>
    </div>
</body>
</html>
