<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timesheet Auto-Approval Summary</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 16px;
        }
        .summary-stats {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            min-width: 150px;
            margin: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .stat-card.success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
        .stat-card.error {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
        }
        .stat-card h3 {
            margin: 0;
            font-size: 32px;
            font-weight: bold;
        }
        .stat-card p {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .section {
            margin: 30px 0;
        }
        .section h2 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .table-container {
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #eee;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        @media (max-width: 600px) {
            .summary-stats {
                flex-direction: column;
            }
            .stat-card {
                margin: 5px 0;
            }
            table {
                font-size: 14px;
            }
            th, td {
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Timesheet Auto-Approval Summary</h1>
            <p>Daily Report for <strong><%= date %></strong></p>
            <p>Generated on <%= generatedAt %></p>
        </div>

        <div class="summary-stats">
            <div class="stat-card">
                <h3><%= totalProcessed %></h3>
                <p>Total Processed</p>
            </div>
            <div class="stat-card success">
                <h3><%= totalApproved %></h3>
                <p>Auto-Approved</p>
            </div>
            <div class="stat-card error">
                <h3><%= totalErrors %></h3>
                <p>Errors</p>
            </div>
        </div>

        <% if (totalProcessed === 0) { %>
            <div class="alert alert-info">
                <strong>ℹ️ No Action Required</strong><br>
                No pending timesheets were found for <%= date %>. All timesheets were either already approved or no timesheets were submitted.
            </div>
        <% } %>

        <% if (totalApproved > 0) { %>
            <div class="section">
                <h2>✅ Auto-Approved Timesheets (<%= totalApproved %>)</h2>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Timesheet ID</th>
                                <th>Employee ID</th>
                                <th>Employee Name</th>
                                <th>Project</th>
                                <th>Task</th>
                                <th>Duration</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% approvedTimesheets.forEach(function(timesheet) { %>
                                <tr>
                                    <td><%= timesheet.id %></td>
                                    <td><%= timesheet.employeeId %></td>
                                    <td><%= timesheet.employeeName %></td>
                                    <td><%= timesheet.projectName || 'N/A' %></td>
                                    <td><%= timesheet.taskName || 'N/A' %></td>
                                    <td><%= timesheet.duration %></td>
                                    <td><span class="status-badge status-approved">Approved</span></td>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        <% } %>

        <% if (totalErrors > 0) { %>
            <div class="section">
                <h2>❌ Processing Errors (<%= totalErrors %>)</h2>
                <div class="alert alert-warning">
                    <strong>⚠️ Attention Required</strong><br>
                    Some timesheets could not be auto-approved. Please review and manually process these timesheets to avoid project calculation impacts.
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Timesheet ID</th>
                                <th>Employee ID</th>
                                <th>Error</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% errors.forEach(function(error) { %>
                                <tr>
                                    <td><%= error.timesheetId %></td>
                                    <td><%= error.employeeId || 'N/A' %></td>
                                    <td><%= error.error %></td>
                                    <td><span class="status-badge status-error">Failed</span></td>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        <% } %>

        <div class="section">
            <h2>📋 Process Details</h2>
            <div class="alert alert-info">
                <strong>🤖 Automated Process Information:</strong><br>
                • This cron job runs daily at 2:00 AM UTC (7:30 AM IST)<br>
                • It processes timesheets from the previous day (<%= date %>)<br>
                • Approval checking is bypassed to ensure project calculations are not impacted<br>
                • All approved timesheets update task and project actual hours automatically<br>
                • Employees receive email notifications for auto-approved timesheets<br>
                • Manual review is recommended for any failed approvals
            </div>
        </div>

        <% if (totalApproved > 0) { %>
            <div class="section">
                <h2>📈 Impact Summary</h2>
                <ul>
                    <li><strong><%= totalApproved %></strong> timesheets were automatically approved</li>
                    <li>Task actual hours have been updated for all approved timesheets</li>
                    <li>Project actual hours have been updated for all approved timesheets</li>
                    <li>Employees have been notified via email about their approved timesheets</li>
                    <li>Project calculations and billing cycles remain unimpacted</li>
                </ul>
            </div>
        <% } %>

        <div class="footer">
            <p>
                <strong>HRMS - Timesheet Auto-Approval System</strong><br>
                This is an automated email. Please do not reply to this message.<br>
                For support, contact your system administrator.
            </p>
        </div>
    </div>
</body>
</html>
