'use strict';

/**
 * Rota Shift Template Routes - PRD Implementation
 * 
 * Complete API endpoints for shift template management according to PRD:
 * - Template CRUD operations
 * - Template library features (search, categories, duplicate)
 * - Bulk operations
 * - Usage analytics
 */

const express = require('express');
const rotaShiftController = require('../controllers/rotaShift.controller');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission } = require('../middlewares/authorization.middleware');
const { validate, validateQuery } = require('../middlewares/validation.middleware');
const rotaShiftValidator = require('../validators/rotaShift.validator');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// ==================== TEMPLATE LIBRARY FEATURES ====================

/**
 * @route GET /api/v1/rota-shifts/categories
 * @desc Get template categories with counts
 * @access Private
 */
router.get(
  '/categories',
  // hasPermission('rota_shifts:read'),
  rotaShiftController.getTemplateCategories
);

/**
 * @route GET /api/v1/rota-shifts/search
 * @desc Search templates with advanced filters
 * @access Private
 */
router.get(
  '/search',
  validateQuery(rotaShiftValidator.searchTemplates),
  // hasPermission('rota_shifts:read'),
  rotaShiftController.searchTemplates
);

// ==================== BULK OPERATIONS ====================

/**
 * @route POST /api/v1/rota-shifts/bulk-create
 * @desc Bulk create shift templates
 * @access Private
 */
router.post(
  '/bulk-create',
  validate(rotaShiftValidator.bulkCreateTemplates),
  // hasPermission('rota_shifts:create'),
  rotaShiftController.bulkCreateTemplates
);

/**
 * @route PUT /api/v1/rota-shifts/bulk-update
 * @desc Bulk update shift templates
 * @access Private
 */
router.put(
  '/bulk-update',
  validate(rotaShiftValidator.bulkUpdateTemplates),
  // hasPermission('rota_shifts:update'),
  rotaShiftController.bulkUpdateTemplates
);

// ==================== CORE CRUD OPERATIONS ====================

/**
 * @route GET /api/v1/rota-shifts
 * @desc Get all shift templates with filtering and pagination
 * @access Private
 */
router.get(
  '/',
  validateQuery(rotaShiftValidator.getAllTemplates),
  // hasPermission('rota_shifts:read'),
  rotaShiftController.getAllShiftTemplates
);

/**
 * @route POST /api/v1/rota-shifts
 * @desc Create new shift template
 * @access Private
 */
router.post(
  '/',
  validate(rotaShiftValidator.createTemplate),
  // hasPermission('rota_shifts:create'),
  rotaShiftController.createShiftTemplate
);

/**
 * @route GET /api/v1/rota-shifts/:id
 * @desc Get shift template by ID with optional usage analytics
 * @access Private
 */
router.get(
  '/:id',
  validateQuery(rotaShiftValidator.getTemplateById),
  // hasPermission('rota_shifts:read'),
  rotaShiftController.getShiftTemplateById
);

/**
 * @route PUT /api/v1/rota-shifts/:id
 * @desc Update shift template
 * @access Private
 */
router.put(
  '/:id',
  validate(rotaShiftValidator.updateTemplate),
  // hasPermission('rota_shifts:update'),
  rotaShiftController.updateShiftTemplate
);

/**
 * @route DELETE /api/v1/rota-shifts/:id
 * @desc Delete shift template (soft delete by default)
 * @access Private
 */
router.delete(
  '/:id',
  validateQuery(rotaShiftValidator.deleteTemplate),
  // hasPermission('rota_shifts:delete'),
  rotaShiftController.deleteShiftTemplate
);

// ==================== TEMPLATE SPECIFIC OPERATIONS ====================

/**
 * @route POST /api/v1/rota-shifts/:id/duplicate
 * @desc Duplicate shift template with modifications
 * @access Private
 */
router.post(
  '/:id/duplicate',
  validate(rotaShiftValidator.duplicateTemplate),
  // hasPermission('rota_shifts:create'),
  rotaShiftController.duplicateShiftTemplate
);

/**
 * @route GET /api/v1/rota-shifts/:id/usage
 * @desc Get template usage analytics
 * @access Private
 */
router.get(
  '/:id/usage',
  validateQuery(rotaShiftValidator.getTemplateUsage),
  // hasPermission('rota_shifts:read'),
  rotaShiftController.getTemplateUsage
);

module.exports = router;
