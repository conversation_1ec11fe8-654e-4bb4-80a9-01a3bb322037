'use strict';

/**
 * Rota Schedule Routes - PRD Implementation
 * 
 * Complete API endpoints for schedule management according to PRD:
 * - Schedule lifecycle management (draft → published → archived)
 * - Auto-schedule generation integration
 * - Schedule workflow operations
 * - Statistics and permissions
 */

const express = require('express');
const rotaScheduleController = require('../controllers/rotaSchedule.controller');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission } = require('../middlewares/authorization.middleware');
const { validate, validateQuery } = require('../middlewares/validation.middleware');
const rotaScheduleValidator = require('../validators/rotaSchedule.validator');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// ==================== CORE CRUD OPERATIONS ====================

/**
 * @route GET /api/v1/rota-schedules
 * @desc Get all schedules with filtering and pagination
 * @access Private
 */
router.get(
  '/',
  validateQuery(rotaScheduleValidator.getAllSchedules),
  // hasPermission('rota_schedules:read'), // ✅ Basic permission check
  rotaScheduleController.getAllSchedules
);

/**
 * @route POST /api/v1/rota-schedules
 * @desc Create new schedule
 * @access Private
 */
router.post(
  '/',
  validate(rotaScheduleValidator.createSchedule),
  // hasPermission('rota_schedules:create'), // ✅ Basic permission check
  rotaScheduleController.createSchedule
);

/**
 * @route GET /api/v1/rota-schedules/:id
 * @desc Get schedule by ID with optional detailed information
 * @access Private
 */
router.get(
  '/:id',
  validateQuery(rotaScheduleValidator.getScheduleById),
  // hasPermission('rota_schedules:read'),
  rotaScheduleController.getScheduleById
);

/**
 * @route PUT /api/v1/rota-schedules/:id
 * @desc Update schedule
 * @access Private
 */
router.put(
  '/:id',
  validate(rotaScheduleValidator.updateSchedule),
  // hasPermission('rota_schedules:update'),
  rotaScheduleController.updateSchedule
);

/**
 * @route DELETE /api/v1/rota-schedules/:id
 * @desc Delete schedule (soft delete by default)
 * @access Private
 */
router.delete(
  '/:id',
  validateQuery(rotaScheduleValidator.deleteSchedule),
  // hasPermission('rota_schedules:delete'),
  rotaScheduleController.deleteSchedule
);

// ==================== ENHANCED SCHEDULE MANAGEMENT (HYBRID APPROACH) ====================

/**
 * @route POST /api/v1/rota-schedules/:id/bulk-assign
 * @desc Bulk assign employees to schedule shifts (Enhanced for scale)
 * @access Private
 */
router.post(
  '/:id/bulk-assign',
  validate(rotaScheduleValidator.bulkAssignEmployees),
  // hasPermission('rota_schedules:assign_employees'),
  rotaScheduleController.bulkAssignEmployees
);

/**
 * @route PUT /api/v1/rota-schedules/shift-instances/:instanceId/requirements
 * @desc Update shift instance requirements (Schedule-specific modifications)
 * @access Private
 */
router.put(
  '/shift-instances/:instanceId/requirements',
  validate(rotaScheduleValidator.updateShiftInstanceRequirements),
  // hasPermission('rota_schedules:update'),
  rotaScheduleController.updateShiftInstanceRequirements
);

// ==================== SCHEDULE WORKFLOW OPERATIONS ====================

/**
 * @route POST /api/v1/rota-schedules/:id/auto-generate
 * @desc Generate auto-schedule from existing schedule with forecasting
 * @access Private
 */
router.post(
  '/:id/auto-generate',
  validate(rotaScheduleValidator.generateAutoSchedule),
  // hasPermission('rota_schedules:generate'),
  rotaScheduleController.generateAutoSchedule
);

/**
 * @route POST /api/v1/rota-schedules/auto-generate
 * @desc Generate new schedule from shift template (dual mode)
 * @access Private
 */
router.post(
  '/auto-generate',
  validate(rotaScheduleValidator.generateAutoSchedule),
  // hasPermission('rota_schedules:generate'),
  rotaScheduleController.generateAutoSchedule
);

/**
 * ✅ ENHANCED: @route POST /api/v1/rota-schedules/:id/approve-preview
 * @desc Move auto-generated schedule from preview to draft
 * @access Private
 */
router.post(
  '/:id/approve-preview',
  validate(rotaScheduleValidator.approvePreview),
  // hasPermission('rota_schedules:approve'),
  rotaScheduleController.approvePreview
);

/**
 * @route POST /api/v1/rota-schedules/:id/publish
 * @desc Publish schedule (makes it active and locks assignments)
 * @access Private
 */
router.post(
  '/:id/publish',
  validate(rotaScheduleValidator.publishSchedule),
  // hasPermission('rota_schedules:publish'),
  rotaScheduleController.publishSchedule
);

/**
 * @route POST /api/v1/rota-schedules/:id/archive
 * @desc Archive schedule (soft delete with reason)
 * @access Private
 */
router.post(
  '/:id/archive',
  validate(rotaScheduleValidator.archiveSchedule),
  // hasPermission('rota_schedules:archive'),
  rotaScheduleController.archiveSchedule
);

/**
 * @route POST /api/v1/rota-schedules/:id/clone
 * @desc Clone schedule with optional assignments
 * @access Private
 */
router.post(
  '/:id/clone',
  validate(rotaScheduleValidator.cloneSchedule),
  // hasPermission('rota_schedules:create'),
  rotaScheduleController.cloneSchedule
);

// ==================== SCHEDULE ACTIONS ====================

/**
 * @route POST /api/v1/rota-schedules/:id/generate-instances
 * @desc Generate shift instances for schedule
 * @access Private
 */
router.post(
  '/:id/generate-instances',
  validate(rotaScheduleValidator.generateInstances),
  // hasPermission('rota_schedules:update'),
  rotaScheduleController.generateInstances
);

/**
 * @route POST /api/v1/rota-schedules/:id/auto-assign
 * @desc Auto-assign employees to schedule
 * @access Private
 */
router.post(
  '/:id/auto-assign',
  validate(rotaScheduleValidator.autoAssignEmployees),
  // hasPermission('rota_schedules:update'),
  rotaScheduleController.autoAssignEmployees
);

/**
 * @route POST /api/v1/rota-schedules/:id/validate
 * @desc Validate schedule for conflicts and coverage
 * @access Private
 */
router.post(
  '/:id/validate',
  validate(rotaScheduleValidator.validateSchedule),
  // hasPermission('rota_schedules:read'),
  rotaScheduleController.validateSchedule
);

/**
 * @route POST /api/v1/rota-schedules/:id/cancel
 * @desc Cancel schedule
 * @access Private
 */
router.post(
  '/:id/cancel',
  validate(rotaScheduleValidator.cancelSchedule),
  // hasPermission('rota_schedules:update'),
  rotaScheduleController.cancelSchedule
);

// ==================== SCHEDULE ANALYTICS ====================

/**
 * @route GET /api/v1/rota-schedules/:id/statistics
 * @desc Get schedule statistics (staffing, coverage, etc.)
 * @access Private
 */
router.get(
  '/:id/statistics',
  // hasPermission('rota_schedules:read'),
  rotaScheduleController.getScheduleStatistics
);

/**
 * @route GET /api/v1/rota-schedules/:id/conflicts
 * @desc Get schedule conflicts report
 * @access Private
 */
router.get(
  '/:id/conflicts',
  validateQuery(rotaScheduleValidator.getScheduleConflicts),
  // hasPermission('rota_schedules:read'),
  rotaScheduleController.getScheduleConflicts
);

/**
 * @route GET /api/v1/rota-schedules/:id/coverage
 * @desc Get schedule coverage analysis
 * @access Private
 */
router.get(
  '/:id/coverage',
  validateQuery(rotaScheduleValidator.getScheduleCoverage),
  // hasPermission('rota_schedules:read'),
  rotaScheduleController.getScheduleCoverage
);

// ==================== SCHEDULE INFORMATION ====================



module.exports = router;
