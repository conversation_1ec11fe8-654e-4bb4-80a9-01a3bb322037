'use strict';

/**
 * Shift Swap Routes - PRD Implementation
 * 
 * Complete API endpoints for shift swap management according to PRD:
 * - Shift swap request workflow (request → approve → execute)
 * - Swap validation and conflict detection
 * - Approval workflow management
 * - Swap discovery and analytics
 */

const express = require('express');
const shiftSwapController = require('../controllers/shiftSwap.controller');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission } = require('../middlewares/authorization.middleware');
const { validate, validateQuery } = require('../middlewares/validation.middleware');
const shiftSwapValidator = require('../validators/shiftSwap.validator');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// ==================== NEW APIS ====================

/**
 * @route GET /api/v1/shift-swaps/employee/:employeeId/shift/:date
 * @desc Get employee's assigned shift for a specific date
 * @access Private
 */
router.get(
  '/employee/:employeeId/shift/:date',
  // hasPermission('shift_swaps:read'),
  shiftSwapController.getEmployeeAssignedShift
);

/**
 * @route GET /api/v1/shift-swaps/available-employees/:date
 * @desc Get available employees for shift swap
 * @access Private
 */
router.get(
  '/available-employees/:date',
  // hasPermission('shift_swaps:read'),
  shiftSwapController.getAvailableEmployeesForSwap
);

/**
 * @route GET /api/v1/shift-swaps/scheduled-shifts/:date
 * @desc Get all scheduled shifts for a specific date
 * @access Private
 */
router.get(
  '/scheduled-shifts/:date',
  validateQuery(shiftSwapValidator.getScheduledShiftsByDate),
  // hasPermission('shift_swaps:read'),
  shiftSwapController.getScheduledShiftsByDate
);

// ==================== SWAP DISCOVERY & VALIDATION ====================

/**
 * @route POST /api/v1/shift-swaps/validate
 * @desc Validate swap request before creation
 * @access Private
 */
router.post(
  '/validate',
  validate(shiftSwapValidator.validateSwapRequest),
  // hasPermission('shift_swaps:validate'),
  shiftSwapController.validateSwapRequest
);

/**
 * @route GET /api/v1/shift-swaps/statistics
 * @desc Get swap statistics and analytics
 * @access Private
 */
router.get(
  '/statistics',
  validateQuery(shiftSwapValidator.getSwapStatistics),
  // hasPermission('shift_swaps:read'),
  shiftSwapController.getSwapStatistics
);

/**
 * @route GET /api/v1/shift-swaps/pending-approvals
 * @desc Get pending approvals for current user
 * @access Private
 */
router.get(
  '/pending-approvals',
  validateQuery(shiftSwapValidator.getPendingApprovals),
  // hasPermission('shift_swaps:approve'),
  shiftSwapController.getPendingApprovals
);

/**
 * @route POST /api/v1/shift-swaps/request
 * @desc Create swap request (alternative endpoint)
 * @access Private
 */
router.post(
  '/request',
  validate(shiftSwapValidator.createSwapRequest),
  // hasPermission('shift_swaps:create'),
  shiftSwapController.createSwapRequestAlt
);

/**
 * @route GET /api/v1/shift-swaps/analytics
 * @desc Get swap analytics
 * @access Private
 */
router.get(
  '/analytics',
  validateQuery(shiftSwapValidator.getSwapAnalytics),
  // hasPermission('shift_swaps:read'),
  shiftSwapController.getSwapAnalytics
);

/**
 * @route POST /api/v1/shift-swaps/bulk-approve
 * @desc Bulk approve swap requests
 * @access Private
 */
router.post(
  '/bulk-approve',
  validate(shiftSwapValidator.bulkApproveSwaps),
  // hasPermission('shift_swaps:approve'),
  shiftSwapController.bulkApproveSwaps
);

// ==================== CORE CRUD OPERATIONS ====================

/**
 * @route GET /api/v1/shift-swaps
 * @desc Get all shift swap requests with filtering and pagination
 * @access Private
 */
router.get(
  '/',
  validateQuery(shiftSwapValidator.getAllSwapRequests),
  // hasPermission('shift_swaps:read'),
  shiftSwapController.getAllSwapRequests
);

/**
 * @route POST /api/v1/shift-swaps
 * @desc Create new shift swap request
 * @access Private
 */
router.post(
  '/',
  validate(shiftSwapValidator.createSwapRequest),
  // hasPermission('shift_swaps:create'),
  shiftSwapController.createSwapRequest
);

/**
 * @route GET /api/v1/shift-swaps/:id
 * @desc Get swap request by ID with detailed information
 * @access Private
 */
router.get(
  '/:id',
  
  validateQuery(shiftSwapValidator.getSwapRequestById),
  // hasPermission('shift_swaps:read'),
  shiftSwapController.getSwapRequestById
);

/**
 * @route PUT /api/v1/shift-swaps/:id
 * @desc Update swap request
 * @access Private
 */
router.put(
  '/:id',
  
  validate(shiftSwapValidator.updateSwapRequest),
  // hasPermission('shift_swaps:update'),
  shiftSwapController.updateSwapRequest
);

// ==================== SWAP WORKFLOW OPERATIONS ====================

/**
 * @route POST /api/v1/shift-swaps/:id/cancel
 * @desc Cancel swap request
 * @access Private
 */
router.post(
  '/:id/cancel',
  
  validate(shiftSwapValidator.cancelSwapRequest),
  // hasPermission('shift_swaps:cancel'),
  shiftSwapController.cancelSwapRequest
);

/**
 * @route POST /api/v1/shift-swaps/:id/approve
 * @desc Approve swap request
 * @access Private
 */
router.post(
  '/:id/approve',
  
  validate(shiftSwapValidator.approveSwapRequest),
  // hasPermission('shift_swaps:approve'),
  shiftSwapController.approveSwapRequest
);

/**
 * @route POST /api/v1/shift-swaps/:id/reject
 * @desc Reject swap request
 * @access Private
 */
router.post(
  '/:id/reject',
  
  validate(shiftSwapValidator.rejectSwapRequest),
  // hasPermission('shift_swaps:reject'),
  shiftSwapController.rejectSwapRequest
);

/**
 * @route POST /api/v1/shift-swaps/:id/respond
 * @desc Respond to swap request (employee response)
 * @access Private
 */
router.post(
  '/:id/respond',
  
  validate(shiftSwapValidator.respondToSwapRequest),
  // hasPermission('shift_swaps:respond'),
  shiftSwapController.respondToSwapRequest
);

/**
 * @route POST /api/v1/shift-swaps/:id/execute
 * @desc Execute approved swap
 * @access Private
 */
router.post(
  '/:id/execute',
  
  validate(shiftSwapValidator.executeSwap),
  // hasPermission('shift_swaps:execute'),
  shiftSwapController.executeSwap
);

/**
 * @route GET /api/v1/shift-swaps/:id/eligible-employees
 * @desc Get eligible employees for swap
 * @access Private
 */
router.get(
  '/:id/eligible-employees',
  
  validateQuery(shiftSwapValidator.getEligibleEmployees),
  // hasPermission('shift_swaps:read'),
  shiftSwapController.getEligibleEmployees
);

// ==================== EMPLOYEE SWAP DISCOVERY ====================

/**
 * @route GET /api/v1/employees/:employeeId/available-swaps
 * @desc Get available swaps for employee
 * @access Private
 */
router.get(
  '/employees/:employeeId/available-swaps',
  
  validateQuery(shiftSwapValidator.getAvailableSwaps),
  // hasPermission('shift_swaps:read'),
  shiftSwapController.getAvailableSwaps
);

module.exports = router;
