const express = require('express');
const router = express.Router();

// Import controllers
const shiftTemplateController = require('../controllers/shiftTemplate.controller');

// Import validators
const shiftTemplateValidator = require('../validators/shiftTemplate.validator');

// Import middleware
const authenticate  = require('../middlewares/authentication.middleware');
const { hasPermission } = require('../middlewares/authorization.middleware');
const { validate, validateParams } = require('../middlewares/validation.middleware');

// For the new routes, we'll use the correct middleware names
const authorize = hasPermission;
const validateRequest = validate;

// Apply authentication and tenant context to all routes
router.use(authenticate);
router.get('/available-shifts',
  // hasPermission('rota:read'),
  shiftTemplateController.getAvailableShifts
);

router.post('/validate',
  // hasPermission('rota:create'),
  validate(shiftTemplateValidator.createTemplate, 'body'),
  shiftTemplateController.validateTemplate
);

router.post('/',
  // hasPermission('rota:create'),
  validate(shiftTemplateValidator.createTemplate, 'body'),
  shiftTemplateController.createTemplate
);

router.get('/',
  hasPermission('rota:read'),
  validate(shiftTemplateValidator.getAllTemplates, 'query'),
  shiftTemplateController.getAllTemplates
);

router.get('/:id',
  // hasPermission('rota:read'),
  // validate(shiftTemplateValidator.getTemplateById, 'params'),
  shiftTemplateController.getTemplateById
);

router.put('/:id',
  // hasPermission('rota:update'),
  // validate(shiftTemplateValidator.getTemplateById, 'params'),
  validate(shiftTemplateValidator.updateTemplate, 'body'),
  shiftTemplateController.updateTemplate
);

router.delete('/:id',
  // hasPermission('rota:delete'),
  validate(shiftTemplateValidator.deleteTemplate, 'params'),
  shiftTemplateController.deleteTemplate
);


router.post('/:id/clone',
  // hasPermission('rota:create'),
  validate(shiftTemplateValidator.getTemplateById, 'params'),
  validate(shiftTemplateValidator.cloneTemplate, 'body'),
  shiftTemplateController.cloneTemplate
);

router.put('/:id/set-default',
  // hasPermission('rota:update'),
  validate(shiftTemplateValidator.setAsDefault, 'params'),
  shiftTemplateController.setAsDefault
);

router.get('/:id/usage-stats',
  // hasPermission('rota:read'),
  validate(shiftTemplateValidator.getTemplateById, 'params'),
  shiftTemplateController.getUsageStats
);

router.post('/:id/generate-instances',
  authorize('rota:create'),
  validateParams(shiftTemplateValidator.generateInstancesParams),
  validate(shiftTemplateValidator.generateInstancesBody),
  shiftTemplateController.generateInstances
);

module.exports = router;
