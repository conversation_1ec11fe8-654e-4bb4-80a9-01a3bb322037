'use strict';

/**
 * Shift Assignment Routes - PRD Implementation
 * 
 * Complete API endpoints for employee assignment management according to PRD:
 * - Assignment CRUD operations
 * - Assignment status management (confirm, complete, no-show)
 * - Bulk operations
 * - Employee-to-instance assignment workflow
 */

const express = require('express');
const shiftAssignmentController = require('../controllers/shiftAssignment.controller');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission } = require('../middlewares/authorization.middleware');
const { validate, validateQuery } = require('../middlewares/validation.middleware');
const shiftAssignmentValidator = require('../validators/shiftAssignment.validator');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// ==================== BULK OPERATIONS ====================

/**
 * @route POST /api/v1/shift-assignments/bulk-assign
 * @desc Bulk assign employees to shifts
 * @access Private
 */
router.post(
  '/bulk-assign',
  validate(shiftAssignmentValidator.bulkAssignEmployees),
  // hasPermission('shift_assignments:create'),
  shiftAssignmentController.bulkAssignEmployees
);

/**
 * @route PUT /api/v1/shift-assignments/bulk-update
 * @desc Bulk update assignments
 * @access Private
 */
router.put(
  '/bulk-update',
  validate(shiftAssignmentValidator.bulkUpdateAssignments),
  // hasPermission('shift_assignments:update'),
  shiftAssignmentController.bulkUpdateAssignments
);

/**
 * @route POST /api/v1/shift-assignments/bulk-confirm
 * @desc Bulk confirm assignments
 * @access Private
 */
router.post(
  '/bulk-confirm',
  validate(shiftAssignmentValidator.bulkConfirmAssignments),
  // hasPermission('shift_assignments:confirm'),
  shiftAssignmentController.bulkConfirmAssignments
);

// ==================== CORE CRUD OPERATIONS ====================

/**
 * @route GET /api/v1/shift-assignments
 * @desc Get all shift assignments with filtering and pagination
 * @access Private
 */
router.get(
  '/',
  validateQuery(shiftAssignmentValidator.getAllAssignments),
  // hasPermission('shift_assignments:read'),
  shiftAssignmentController.getAllAssignments
);

/**
 * @route GET /api/v1/shift-assignments/:id
 * @desc Get assignment by ID with detailed information
 * @access Private
 */
router.get(
  '/:id',
  
  validateQuery(shiftAssignmentValidator.getAssignmentById),
  // hasPermission('shift_assignments:read'),
  shiftAssignmentController.getAssignmentById
);

/**
 * @route PUT /api/v1/shift-assignments/:id
 * @desc Update assignment
 * @access Private
 */
router.put(
  '/:id',
  
  validate(shiftAssignmentValidator.updateAssignment),
  // hasPermission('shift_assignments:update'),
  shiftAssignmentController.updateAssignment
);

/**
 * @route DELETE /api/v1/shift-assignments/:id
 * @desc Remove assignment
 * @access Private
 */
router.delete(
  '/:id',
  
  validate(shiftAssignmentValidator.removeAssignment),
  // hasPermission('shift_assignments:delete'),
  shiftAssignmentController.removeAssignment
);

// ==================== ASSIGNMENT STATUS MANAGEMENT ====================

/**
 * @route POST /api/v1/shift-assignments/:id/confirm
 * @desc Confirm assignment (employee confirmation)
 * @access Private
 */
router.post(
  '/:id/confirm',
  
  validate(shiftAssignmentValidator.confirmAssignment),
  // hasPermission('shift_assignments:confirm'),
  shiftAssignmentController.confirmAssignment
);

/**
 * @route POST /api/v1/shift-assignments/:id/complete
 * @desc Mark assignment as completed
 * @access Private
 */
router.post(
  '/:id/complete',
  
  validate(shiftAssignmentValidator.completeAssignment),
  // hasPermission('shift_assignments:complete'),
  shiftAssignmentController.completeAssignment
);

/**
 * @route POST /api/v1/shift-assignments/:id/no-show
 * @desc Mark assignment as no-show
 * @access Private
 */
router.post(
  '/:id/no-show',
  
  validate(shiftAssignmentValidator.markNoShow),
  // hasPermission('shift_assignments:no_show'),
  shiftAssignmentController.markNoShow
);

// ==================== EMPLOYEE-TO-INSTANCE ASSIGNMENT ====================

/**
 * @route POST /api/v1/shift-instances/:instanceId/assign
 * @desc Assign employee to shift instance
 * @access Private
 */
router.post(
  '/instances/:instanceId/assign',

  validate(shiftAssignmentValidator.assignEmployeeToShift),
  // hasPermission('shift_assignments:create'),
  shiftAssignmentController.assignEmployeeToShift
);

// ==================== SCHEDULE-TIME EMPLOYEE ASSIGNMENT ====================

/**
 * @route POST /api/v1/shift-assignments/schedule/:scheduleId/assign
 * @desc Assign employees to schedule shifts during schedule creation
 * @access Private
 */
router.post(
  '/schedule/:scheduleId/assign',

  validate(shiftAssignmentValidator.assignEmployeesToSchedule),
  // hasPermission('shift_assignments:create'),
  shiftAssignmentController.assignEmployeesToSchedule
);

/**
 * @route POST /api/v1/shift-assignments/bulk-schedule-assign
 * @desc Bulk assign employees during schedule creation
 * @access Private
 */
router.post(
  '/bulk-schedule-assign',
  validate(shiftAssignmentValidator.bulkScheduleAssign),
  // hasPermission('shift_assignments:create'),
  shiftAssignmentController.bulkScheduleAssign
);

module.exports = router;
