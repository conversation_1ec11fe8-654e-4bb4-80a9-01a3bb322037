'use strict';

const express = require('express');
const authenticate = require('../../middlewares/authentication.middleware');
const { validate } = require('../../middlewares/validation.middleware');
const mobileBankDetailController = require('../../controllers/mobile/bankDetail.controller');
const employeeValidator = require('../../validators/employee.validator');

const router = express.Router();

// Protected routes (authentication required)
router.use(authenticate);
router.get('/bank-details', mobileBankDetailController.getBankDetails);
router.post(
  '/bank-details',
  validate(employeeValidator.addBankDetails),
  mobileBankDetailController.addBankDetails
);
router.put(
  '/bank-details',
  validate(employeeValidator.updateBankDetails),
  mobileBankDetailController.updateBankDetails
);
router.delete('/bank-details', mobileBankDetailController.deleteBankDetails);

module.exports = router;
