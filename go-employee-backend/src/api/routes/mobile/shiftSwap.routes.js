'use strict';

/**
 * Mobile Shift Swap Routes - PRD Implementation
 * 
 * Mobile-optimized API endpoints for shift swap management according to PRD:
 * - Mobile-optimized swap discovery
 * - Simplified swap request workflow
 * - Push notification integration
 * - Offline-friendly data structure
 */

const express = require('express');
const mobileShiftSwapController = require('../../controllers/mobile/shiftSwap.controller');
const authenticate = require('../../middlewares/authentication.middleware');
const { validate, validateQuery, validateParams } = require('../../middlewares/validation.middleware');
const mobileShiftSwapValidator = require('../../validators/mobile/shiftSwap.validator');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// ==================== MY SWAP REQUESTS ====================

/**
 * @route GET /api/v1/mobile/my-swap-requests
 * @desc Get my swap requests (sent and received)
 * @access Private (Employee)
 */
router.get(
  '/my-swap-requests',
  validateQuery(mobileShiftSwapValidator.getMySwapRequests),
  mobileShiftSwapController.getMySwapRequests
);

/**
 * @route POST /api/v1/mobile/swap-requests
 * @desc Create swap request
 * @access Private (Employee)
 */
router.post(
  '/swap-requests',
  validate(mobileShiftSwapValidator.createSwapRequest),
  mobileShiftSwapController.createSwapRequest
);

/**
 * @route GET /api/v1/mobile/swap-requests/:id
 * @desc Get swap request details
 * @access Private (Employee)
 */
router.get(
  '/swap-requests/:id',
  validateParams(mobileShiftSwapValidator.idParam),
  mobileShiftSwapController.getSwapRequestDetails
);

// ==================== SWAP WORKFLOW ====================

/**
 * @route POST /api/v1/mobile/swap-requests/:id/respond
 * @desc Respond to swap request (accept/decline)
 * @access Private (Employee)
 */
router.post(
  '/swap-requests/:id/respond',
  validateParams(mobileShiftSwapValidator.idParam),
  validate(mobileShiftSwapValidator.respondToSwapRequest),
  mobileShiftSwapController.respondToSwapRequest
);

/**
 * @route POST /api/v1/mobile/swap-requests/:id/cancel
 * @desc Cancel my swap request
 * @access Private (Employee)
 */
router.post(
  '/swap-requests/:id/cancel',
  validateParams(mobileShiftSwapValidator.idParam),
  validate(mobileShiftSwapValidator.cancelSwapRequest),
  mobileShiftSwapController.cancelSwapRequest
);

// ==================== SWAP DISCOVERY ====================

/**
 * @route GET /api/v1/mobile/available-swaps
 * @desc Get available shifts for swapping
 * @access Private (Employee)
 */
router.get(
  '/available-swaps',
  validateQuery(mobileShiftSwapValidator.getAvailableSwaps),
  mobileShiftSwapController.getAvailableSwaps
);

/**
 * @route GET /api/v1/mobile/my-swappable-shifts
 * @desc Get my swappable shifts
 * @access Private (Employee)
 */
router.get(
  '/my-swappable-shifts',
  validateQuery(mobileShiftSwapValidator.getMySwappableShifts),
  mobileShiftSwapController.getMySwappableShifts
);

module.exports = router;
