'use strict';

/**
 * Mobile Schedule Routes - PRD Implementation
 * 
 * Mobile-optimized API endpoints for schedule management according to PRD:
 * - Employee schedule viewing (my shifts)
 * - Mobile-optimized responses
 * - Offline-friendly data structure
 * - Push notification integration
 */

const express = require('express');
const mobileScheduleController = require('../../controllers/mobile/schedule.controller');
const authenticate = require('../../middlewares/authentication.middleware');
const { validate, validateQuery, validateParams } = require('../../middlewares/validation.middleware');
const mobileScheduleValidator = require('../../validators/mobile/schedule.validator');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// ==================== MY SCHEDULE ====================

/**
 * @route GET /api/v1/mobile/my-schedule
 * @desc Get employee's schedule (my shifts)
 * @access Private (Employee)
 */
router.get(
  '/my-schedule',
  validateQuery(mobileScheduleValidator.getMySchedule),
  mobileScheduleController.getMySchedule
);

/**
 * @route GET /api/v1/mobile/upcoming-shifts
 * @desc Get upcoming shifts (next 7 days)
 * @access Private (Employee)
 */
router.get(
  '/upcoming-shifts',
  validateQuery(mobileScheduleValidator.getUpcomingShifts),
  mobileScheduleController.getUpcomingShifts
);

/**
 * @route GET /api/v1/mobile/calendar
 * @desc Get shift calendar view
 * @access Private (Employee)
 */
router.get(
  '/calendar',
  validateQuery(mobileScheduleValidator.getCalendarView),
  mobileScheduleController.getCalendarView
);

// ==================== SHIFT MANAGEMENT ====================

/**
 * @route GET /api/v1/mobile/shifts/:assignmentId
 * @desc Get specific shift details
 * @access Private (Employee)
 */
router.get(
  '/shifts/:assignmentId',
  validateParams(mobileScheduleValidator.assignmentIdParam),
  mobileScheduleController.getShiftDetails
);

/**
 * @route POST /api/v1/mobile/shifts/:assignmentId/confirm
 * @desc Confirm shift assignment
 * @access Private (Employee)
 */
router.post(
  '/shifts/:assignmentId/confirm',
  validateParams(mobileScheduleValidator.assignmentIdParam),
  validate(mobileScheduleValidator.confirmShift),
  mobileScheduleController.confirmShift
);

// ==================== NOTIFICATIONS ====================

/**
 * @route GET /api/v1/mobile/notifications
 * @desc Get shift notifications
 * @access Private (Employee)
 */
router.get(
  '/notifications',
  validateQuery(mobileScheduleValidator.getShiftNotifications),
  mobileScheduleController.getShiftNotifications
);

module.exports = router;
