'use strict';

const express = require('express');
const authenticate = require('../../middlewares/authentication.middleware');
const { validate, validateParams } = require('../../middlewares/validation.middleware');
const mobileEducationController = require('../../controllers/mobile/education.controller');
const mobileEducationValidator = require('../../validators/mobile/education.validator');

const router = express.Router();

// Protected routes (authentication required)
router.use(authenticate);
// Get current user's education records
router.get('/', mobileEducationController.getEducation);

// Get single education record by ID
router.get(
  '/:educationId',
  validateParams(mobileEducationValidator.getEducationById),
  mobileEducationController.getEducationById
);

// Add new education record
router.post(
  '/',
  validate(mobileEducationValidator.addEducation),
  mobileEducationController.addEducation
);

// Update education record
router.put(
  '/:educationId',
  validate(mobileEducationValidator.updateEducation),
  mobileEducationController.updateEducation
);

// Delete education record
router.delete(
  '/:educationId',
  validateParams(mobileEducationValidator.deleteEducation),
  mobileEducationController.deleteEducation
);

module.exports = router;
