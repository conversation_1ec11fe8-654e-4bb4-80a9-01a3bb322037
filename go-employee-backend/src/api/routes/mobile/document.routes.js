'use strict';

const express = require('express');
const documentController = require('../../controllers/mobile/document.controller');
const authenticate = require('../../middlewares/authentication.middleware');
const { validate, validateQuery } = require('../../middlewares/validation.middleware');
const documentValidator = require('../../validators/mobile/document.validator');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

/**
 * Mobile Document Management Routes
 * Following the mobile API specification requirements
 */

// GET /v1/mobile/documents - Get all general documents
router.get(
  '/documents',
  validateQuery(documentValidator.getDocuments),
  documentController.getGeneralDocuments
);

// POST /v1/mobile/documents - Create general document
router.post(
  '/documents',
  validate(documentValidator.createDocument),
  documentController.createGeneralDocument
);

// GET /v1/mobile/documents/{documentId} - Get specific general document
router.get(
  '/documents/:documentId',
  documentController.getGeneralDocumentById
);

// PUT /v1/mobile/documents/{documentId} - Update general document
router.put(
  '/documents/:documentId',
  validate(documentValidator.updateDocument),
  documentController.updateGeneralDocument
);

// PATCH /v1/mobile/documents/{documentId} - Partial update general document
router.patch(
  '/documents/:documentId',
  validate(documentValidator.updateDocument),
  documentController.updateGeneralDocument
);

// DELETE /v1/mobile/documents/{documentId} - Delete general document
router.delete(
  '/documents/:documentId',
  documentController.deleteGeneralDocument
);

// GET /v1/mobile/documents/{documentId}/download - Download general document
router.get(
  '/documents/:documentId/download',
  documentController.downloadGeneralDocument
);

// PUT /v1/mobile/documents/{documentId}/verify - Verify general document
router.put(
  '/documents/:documentId/verify',
  documentController.verifyGeneralDocument
);

// GET /v1/mobile/document-types - Get document types
router.get(
  '/document-types',
  validateQuery(documentValidator.getDocumentTypes),
  documentController.getDocumentTypes
);

// POST /v1/mobile/document-types - Create document type
router.post(
  '/document-types',
  validate(documentValidator.createDocumentType),
  documentController.createDocumentType
);

// GET /v1/mobile/document-types/{id} - Get document type by ID
router.get(
  '/document-types/:id',
  documentController.getDocumentTypeById
);

// PUT /v1/mobile/document-types/{id} - Update document type
router.put(
  '/document-types/:id',
  validate(documentValidator.updateDocumentType),
  documentController.updateDocumentType
);

module.exports = router;
