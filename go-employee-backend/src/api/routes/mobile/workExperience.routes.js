'use strict';

const express = require('express');
const mobileWorkExperienceController = require('../../controllers/mobile/workExperience.controller');
const { validate, validateParams } = require('../../middlewares/validation.middleware');
const mobileWorkExperienceValidator = require('../../validators/mobile/workExperience.validator');
const authenticate = require('../../middlewares/authentication.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

/**
 * @route GET /api/v1/mobile/work-experience
 * @desc Get current user's work experience records for mobile
 * @access Private
 */
router.get(
  '/',
  mobileWorkExperienceController.getWorkExperience
);

/**
 * @route GET /api/v1/mobile/work-experience/:experienceId
 * @desc Get single work experience record by ID for mobile
 * @access Private
 */
router.get(
  '/:experienceId',
  validateParams(mobileWorkExperienceValidator.getWorkExperienceById),
  mobileWorkExperienceController.getWorkExperienceById
);

/**
 * @route POST /api/v1/mobile/work-experience
 * @desc Add new work experience record for mobile
 * @access Private
 */
router.post(
  '/',
  validate(mobileWorkExperienceValidator.addWorkExperience),
  mobileWorkExperienceController.addWorkExperience
);

/**
 * @route PUT /api/v1/mobile/work-experience/:experienceId
 * @desc Update work experience record for mobile
 * @access Private
 */
router.put(
  '/:experienceId',
  validate(mobileWorkExperienceValidator.updateWorkExperience),
  mobileWorkExperienceController.updateWorkExperience
);

/**
 * @route DELETE /api/v1/mobile/work-experience/:experienceId
 * @desc Delete work experience record for mobile
 * @access Private
 */
router.delete(
  '/:experienceId',
  validateParams(mobileWorkExperienceValidator.deleteWorkExperience),
  mobileWorkExperienceController.deleteWorkExperience
);

module.exports = router;
