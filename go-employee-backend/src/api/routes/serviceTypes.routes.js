'use strict';

const express = require('express');
const projectController = require('../controllers/project.controller');
const { validate, validateQuery } = require('../middlewares/validation.middleware');
const expenseValidator = require('../validators/expense.validator');
const projectValidator = require('../validators/project.validator');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission } = require('../middlewares/authorization.middleware');

const router = express.Router();

// ===== SERVICE TYPE ROUTES =====
router.use(authenticate);


// Create new service type
router.post(
  '/',
  validate(projectValidator.createServiceType),
  hasPermission('project:create'),
  projectController.createServiceType
);

// Get service type by ID
router.get(
  '/:id',
  hasPermission('project:read'),
  projectController.getServiceTypeById
);

// Get all service types
// Get all service types
router.get(
  '/',
  validateQuery(projectValidator.generalPagination),
  hasPermission('project:read'),
  projectController.getServiceTypes
);

// Update service type
router.put(
  '/:id',
  validate(projectValidator.updateServiceType),
  hasPermission('project:update'),
  projectController.updateServiceType
);

// Delete service type
router.delete(
  '/:id',
  hasPermission('project:delete'),
  projectController.deleteServiceType
);

// Get service types by project
// router.get(
//   '/:projectId/service-types',
//   validateQuery(projectValidator.generalPagination),
//   hasPermission('project:read'),
//   projectController.getServiceTypesByProject
// );

// Associate service types with a project
router.post(
  '/:projectId/service-types',
  validate(projectValidator.associateServiceTypes),
  hasPermission('project:update'),
  projectController.associateServiceTypes
);

// Remove service type association from a project
router.delete(
  '/:projectId/service-types/:serviceTypeId',
  hasPermission('project:update'),
  projectController.removeServiceTypeAssociation
);

module.exports = router;