'use strict';

/**
 * Rota Shift Instance Routes - PRD Implementation
 * 
 * Complete API endpoints for shift instance management according to PRD:
 * - Instance CRUD operations
 * - Requirement overrides
 * - Assignment management
 * - Instance analytics
 */

const express = require('express');
const rotaShiftInstanceController = require('../controllers/rotaShiftInstance.controller');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission } = require('../middlewares/authorization.middleware');
const { validate, validateQuery } = require('../middlewares/validation.middleware');
const rotaShiftInstanceValidator = require('../validators/rotaShiftInstance.validator');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// ==================== CORE INSTANCE OPERATIONS ====================

/**
 * @route GET /api/v1/rota-shift-instances
 * @desc Get all shift instances with filtering
 * @access Private
 */
router.get(
  '/',
  validateQuery(rotaShiftInstanceValidator.getAllInstances),
  // hasPermission('rota_shift_instances:read'),
  rotaShiftInstanceController.getAllInstances
);

/**
 * @route GET /api/v1/rota-shift-instances/:id
 * @desc Get shift instance by ID
 * @access Private
 */
router.get(
  '/:id',
  validateQuery(rotaShiftInstanceValidator.getInstanceById),
  // hasPermission('rota_shift_instances:read'),
  rotaShiftInstanceController.getInstanceById
);

/**
 * @route PUT /api/v1/rota-shift-instances/:id
 * @desc Update shift instance
 * @access Private
 */
router.put(
  '/:id',
  validate(rotaShiftInstanceValidator.updateInstance),
  // hasPermission('rota_shift_instances:update'),
  rotaShiftInstanceController.updateInstance
);

/**
 * @route POST /api/v1/rota-shift-instances/:id/override-count
 * @desc Override instance requirement count
 * @access Private
 */
router.post(
  '/:id/override-count',
  validate(rotaShiftInstanceValidator.overrideRequirementCount),
  // hasPermission('rota_shift_instances:update'),
  rotaShiftInstanceController.overrideRequirementCount
);

/**
 * @route GET /api/v1/rota-shift-instances/:id/coverage
 * @desc Get instance coverage status
 * @access Private
 */
router.get(
  '/:id/coverage',
  // hasPermission('rota_shift_instances:read'),
  rotaShiftInstanceController.getInstanceCoverage
);

// ==================== ASSIGNMENT OPERATIONS ====================

/**
 * @route GET /api/v1/shift-instances/:id/assignments
 * @desc Get instance assignments
 * @access Private
 */
router.get(
  '/:id/assignments',
  validateQuery(rotaShiftInstanceValidator.getInstanceAssignments),
  // hasPermission('shift_assignments:read'),
  rotaShiftInstanceController.getInstanceAssignments
);

/**
 * @route POST /api/v1/shift-instances/:id/assign
 * @desc Assign employee to instance
 * @access Private
 */
router.post(
  '/:id/assign',
  validate(rotaShiftInstanceValidator.assignEmployeeToInstance),
  // hasPermission('shift_assignments:create'),
  rotaShiftInstanceController.assignEmployeeToInstance
);

/**
 * @route GET /api/v1/shift-instances/:id/available-employees
 * @desc Get available employees for instance
 * @access Private
 */
router.get(
  '/:id/available-employees',
  validateQuery(rotaShiftInstanceValidator.getAvailableEmployees),
  // hasPermission('shift_assignments:read'),
  rotaShiftInstanceController.getAvailableEmployees
);

module.exports = router;
