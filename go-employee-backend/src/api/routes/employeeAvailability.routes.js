'use strict';

/**
 * Employee Availability Routes - PRD Implementation
 * 
 * Complete API endpoints for employee availability management according to PRD:
 * - Employee availability CRUD operations
 * - Availability overrides and exceptions
 * - Consent management for shift assignments
 * - Bulk availability operations
 */

const express = require('express');
const employeeAvailabilityController = require('../controllers/employeeAvailability.controller');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission } = require('../middlewares/authorization.middleware');
const { validate, validateQuery } = require('../middlewares/validation.middleware');
const employeeAvailabilityValidator = require('../validators/employeeAvailability.validator');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// ==================== BULK OPERATIONS ====================

/**
 * @route POST /api/v1/availability/bulk-check
 * @desc Bulk availability check for multiple employees/dates
 * @access Private
 */
router.post(
  '/bulk-check',
  validate(employeeAvailabilityValidator.bulkAvailabilityCheck),
  // hasPermission('employee_availability:read'),
  employeeAvailabilityController.bulkAvailabilityCheck
);

// ==================== EMPLOYEE AVAILABILITY MANAGEMENT ====================

/**
 * @route GET /api/v1/employees/:employeeId/availability
 * @desc Get employee availability with optional date range
 * @access Private
 */
router.get(
  '/employees/:employeeId/availability',
  
  validateQuery(employeeAvailabilityValidator.getEmployeeAvailability),
  // hasPermission('employee_availability:read'),
  employeeAvailabilityController.getEmployeeAvailability
);

/**
 * @route PUT /api/v1/employees/:employeeId/availability
 * @desc Update employee availability
 * @access Private
 */
router.put(
  '/employees/:employeeId/availability',
  
  validate(employeeAvailabilityValidator.updateEmployeeAvailability),
  // hasPermission('employee_availability:update'),
  employeeAvailabilityController.updateEmployeeAvailability
);

// ==================== AVAILABILITY OVERRIDES ====================

/**
 * @route POST /api/v1/employees/:employeeId/availability/override
 * @desc Add availability override for employee
 * @access Private
 */
router.post(
  '/employees/:employeeId/availability/override',
  
  validate(employeeAvailabilityValidator.addAvailabilityOverride),
  // hasPermission('employee_availability:override'),
  employeeAvailabilityController.addAvailabilityOverride
);

/**
 * @route DELETE /api/v1/employees/:employeeId/availability/override/:overrideId
 * @desc Remove availability override
 * @access Private
 */
router.delete(
  '/employees/:employeeId/availability/override/:overrideId',
  
  validate(employeeAvailabilityValidator.removeAvailabilityOverride),
  // hasPermission('employee_availability:override'),
  employeeAvailabilityController.removeAvailabilityOverride
);

// ==================== AVAILABILITY CHECKING ====================

/**
 * @route GET /api/v1/employees/:employeeId/availability/check
 * @desc Check employee availability for specific date/time
 * @access Private
 */
router.get(
  '/employees/:employeeId/availability/check',
  
  validateQuery(employeeAvailabilityValidator.checkEmployeeAvailability),
  // hasPermission('employee_availability:read'),
  employeeAvailabilityController.checkEmployeeAvailability
);

/**
 * @route GET /api/v1/shift-instances/:instanceId/available-employees
 * @desc Get available employees for shift instance
 * @access Private
 */
router.get(
  '/shift-instances/:instanceId/available-employees',
  
  validateQuery(employeeAvailabilityValidator.getAvailableEmployeesForInstance),
  // hasPermission('employee_availability:read'),
  employeeAvailabilityController.getAvailableEmployeesForInstance
);

// ==================== CONSENT MANAGEMENT ====================

/**
 * @route GET /api/v1/employees/:employeeId/availability/consents
 * @desc Get employee consents for shift assignments
 * @access Private
 */
router.get(
  '/employees/:employeeId/availability/consents',
  
  // hasPermission('employee_availability:read'),
  employeeAvailabilityController.getEmployeeConsents
);

/**
 * @route PUT /api/v1/employees/:employeeId/availability/consents
 * @desc Update employee consents for shift assignments
 * @access Private
 */
router.put(
  '/employees/:employeeId/availability/consents',
  
  validate(employeeAvailabilityValidator.updateEmployeeConsents),
  // hasPermission('employee_availability:update'),
  employeeAvailabilityController.updateEmployeeConsents
);

/**
 * @route GET /api/v1/employees/consents/summary
 * @desc Get consent summary for all employees
 * @access Private
 */
router.get(
  '/employees/consents/summary',
  validateQuery(employeeAvailabilityValidator.getConsentSummary),
  // hasPermission('employee_availability:read'),
  employeeAvailabilityController.getConsentSummary
);

module.exports = router;
