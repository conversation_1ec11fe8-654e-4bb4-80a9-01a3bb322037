'use strict';

const express = require('express');
const policyController = require('../controllers/policy.controller');
const { validate, validateQuery } = require('../middlewares/validation.middleware');
const policyValidator = require('../validators/policy.validator');
const policyCategoryValidator = require('../validators/policyCategory.validator');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission, hasRole } = require('../middlewares/authorization.middleware');
const { uploadSingleFile } = require('../middlewares/upload.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// 1. Policy CRUD Operations

// GET /v1/policies - Retrieve all policies with pagination, filtering, and search
router.get(
  '/',
  validateQuery(policyValidator.getAllPolicies),
  // hasPermission('policy:read'),
  policyController.getAllPolicies
);

// GET /v1/policies/:id - Retrieve a specific policy by ID
router.get(
  '/:id',
  // hasPermission('policy:read'),
  policyController.getPolicyById
);

// POST /v1/policies - Create a new policy
router.post(
  '/',
  validate(policyValidator.createPolicy),
  // hasPermission('policy:create'),
  policyController.createPolicy
);

// PUT /v1/policies/:id - Update an existing policy
router.put(
  '/:id',
  validate(policyValidator.updatePolicy),
  // hasPermission('policy:update'),
  policyController.updatePolicy
);

// DELETE /v1/policies/:id - Delete a policy
router.delete(
  '/:id',
  // hasRole(['hr_manager', 'company_admin', 'admin']),
  // hasPermission('policy:delete'),
  policyController.deletePolicy
);

// 2. Policy Status Management

// PATCH /v1/policies/:id/status - Update policy status
router.patch(
  '/approve-reject/:id',
  validate(policyValidator.approveRejectPolicy),
  // hasPermission('policy:update'),
  policyController.approveRejectPolicy
);

// 3. Policy Attachments

// POST /v1/policies/:id/attachments - Upload attachment to policy
router.post(
  '/:id/attachments',
  hasPermission('policy:update'),
  uploadSingleFile('file'),
  policyController.uploadPolicyAttachment
);

// GET /v1/policies/:id/attachments - Get all attachments for a policy
router.get(
  '/:id/attachments',
  hasPermission('policy:read'),
  policyController.getPolicyAttachments
);

// DELETE /v1/policies/:id/attachments/:attachmentId - Delete policy attachment
router.delete(
  '/:id/attachments/:attachmentId',
  hasPermission('policy:update'),
  policyController.deletePolicyAttachment
);

// 4. Policy Acknowledgments

// GET /v1/policies/:id/acknowledgments - Get acknowledgments for a policy
router.get(
  '/:id/acknowledgments',
  validateQuery(policyValidator.getPolicyAcknowledgments),
  hasPermission('policy:read'),
  policyController.getPolicyAcknowledgments
);

// POST /v1/policies/:id/acknowledge - Acknowledge a policy
router.post(
  '/:id/acknowledge',
  validate(policyValidator.acknowledgePolicySchema),
  policyController.acknowledgePolicy
);

// GET /v1/policies/acknowledgments/employee/:employeeId - Get employee's policy acknowledgments
router.get(
  '/acknowledgments/employee/:employeeId',
  validateQuery(policyValidator.getEmployeePolicyAcknowledgments),
  hasPermission('policy:read'),
  policyController.getEmployeePolicyAcknowledgments
);

// 5. Policy Versions

// GET /v1/policies/:id/versions - Get all versions of a policy
router.get(
  '/:id/versions',
  hasPermission('policy:read'),
  policyController.getPolicyVersions
);

// GET /v1/policies/:id/versions/:versionId - Get specific version of a policy
router.get(
  '/:id/versions/:versionId',
  hasPermission('policy:read'),
  policyController.getPolicyVersionById
);

// POST /v1/policies/:id/versions - Create new version of a policy
router.post(
  '/:id/versions',
  validate(policyValidator.createPolicyVersion),
  hasPermission('policy:create'),
  policyController.createPolicyVersion
);

// 6. Policy Categories

// GET /v1/policy-categories - Get all policy categories
router.get(
  '/categories',
  validateQuery(policyCategoryValidator.getAllPolicyCategories),
  hasPermission('policy:read'),
  policyController.getAllPolicyCategories
);

// 7. Policy Analytics

// GET /v1/policies/analytics/acknowledgment - Get policy acknowledgment analytics
router.get(
  '/analytics/acknowledgment',
  validateQuery(policyValidator.getPolicyAcknowledgmentAnalytics),
  hasPermission('policy:read'),
  policyController.getPolicyAcknowledgmentAnalytics
);

// GET /v1/policies/analytics/category - Get policy category analytics
router.get(
  '/analytics/category',
  validateQuery(policyValidator.getPolicyCategoryAnalytics),
  hasPermission('policy:read'),
  policyController.getPolicyCategoryAnalytics
);

// 8. Policy Bulk Operations

// POST /v1/policies/bulk/delete - Bulk delete policies
router.post(
  '/bulk/delete',
  validate(policyValidator.bulkDeletePolicies),
  hasRole(['hr_manager', 'company_admin', 'admin']),
  hasPermission('policy:delete'),
  policyController.bulkDeletePolicies
);

// POST /v1/policies/bulk/status - Bulk update policy status
router.post(
  '/bulk/status',
  validate(policyValidator.bulkUpdatePolicyStatus),
  hasPermission('policy:update'),
  policyController.bulkUpdatePolicyStatus
);

module.exports = router;
