'use strict';

const express = require('express');
const policyCategoryController = require('../controllers/policyCategory.controller');
const { validate, validateQuery } = require('../middlewares/validation.middleware');
const policyCategoryValidator = require('../validators/policyCategory.validator');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission, hasRole } = require('../middlewares/authorization.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// GET /v1/policy-categories - Get all policy categories
router.get(
  '/',
  validateQuery(policyCategoryValidator.getAllPolicyCategories),
  // hasPermission('policy:read'),
  policyCategoryController.getAllPolicyCategories
);

// POST /v1/policy-categories - Create a new policy category
router.post(
  '/',
  validate(policyCategoryValidator.createPolicyCategory),
  // hasRole(['hr_manager', 'company_admin', 'admin']),
  // hasPermission('policy:create'),
  policyCategoryController.createPolicyCategory
);

// GET /v1/policy-categories/:id - Get policy category by ID
router.get(
  '/:id',
  // hasPermission('policy:read'),
  policyCategoryController.getPolicyCategoryById
);

// PUT /v1/policy-categories/:id - Update policy category
router.put(
  '/:id',
  validate(policyCategoryValidator.updatePolicyCategory),
  // hasRole(['hr_manager', 'company_admin', 'admin']),
  // hasPermission('policy:update'),
  policyCategoryController.updatePolicyCategory
);

// DELETE /v1/policy-categories/:id - Delete policy category
router.delete(
  '/:id',
  // hasRole(['hr_manager', 'company_admin', 'admin']),
  // hasPermission('policy:delete'),
  policyCategoryController.deletePolicyCategory
);

// GET /v1/policy-categories/:id/policies - Get policies by category
router.get(
  '/:id/policies',
  validateQuery(policyCategoryValidator.getPoliciesByCategory),
  hasPermission('policy:read'),
  policyCategoryController.getPoliciesByCategory
);

module.exports = router;