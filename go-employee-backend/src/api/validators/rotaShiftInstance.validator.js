'use strict';

const Joi = require('joi');

// Get all instances
const getAllInstances = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  scheduleId: Joi.number().integer().positive().optional(),
  departmentId: Joi.number().integer().positive().optional(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  status: Joi.string().valid('open', 'assigned', 'confirmed', 'completed', 'cancelled').optional(),
  sortBy: Joi.string().valid('date', 'createdAt', 'updatedAt').default('date'),
  sortOrder: Joi.string().valid('ASC', 'DESC').default('ASC')
}).custom((value, helpers) => {
  if (value.startDate && value.endDate) {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
  }
  return value;
}).messages({
  'custom.endDateAfterStart': 'End date must be after start date'
});

// Create instance
const createInstance = Joi.object({
  scheduleId: Joi.number().integer().positive().required(),
  rotaShiftId: Joi.number().integer().positive().required(),
  date: Joi.date().iso().required(),
  actualRequiredCount: Joi.number().integer().min(0).optional(),
  overrideReason: Joi.string().valid(
    'template_default',
    'forecast_prediction',
    'manual_override',
    'emergency_requirement',
    'holiday_adjustment',
    'business_rule_override'
  ).default('template_default'),
  forecastData: Joi.object().optional(),
  notes: Joi.string().max(1000).optional()
});

// Get instance by ID
const getInstanceById = Joi.object({
  includeAssignments: Joi.boolean().default(true)
});

// Update instance
const updateInstance = Joi.object({
  actualRequiredCount: Joi.number().integer().min(0).optional(),
  status: Joi.string().valid('open', 'assigned', 'confirmed', 'completed', 'cancelled').optional(),
  notes: Joi.string().max(1000).optional(),
  overrideReason: Joi.string().valid(
    'template_default',
    'forecast_prediction',
    'manual_override',
    'emergency_requirement',
    'holiday_adjustment',
    'business_rule_override'
  ).optional(),
  forecastData: Joi.object().optional()
}).min(1);

// Delete instance
const deleteInstance = Joi.object({
  removeAssignments: Joi.boolean().default(false)
});

// Override requirement count
const overrideRequirementCount = Joi.object({
  newRequiredCount: Joi.number().integer().min(0).required(),
  reason: Joi.string().valid(
    'forecast_update',
    'emergency_requirement',
    'skill_shortage',
    'business_demand',
    'other'
  ).required(),
  notes: Joi.string().max(500).optional()
});

// Get instance assignments
const getInstanceAssignments = Joi.object({
  status: Joi.string().valid('assigned', 'confirmed', 'completed', 'cancelled', 'absent').optional(),
  includeEmployee: Joi.boolean().default(true)
});

// Assign employee to instance
const assignEmployeeToInstance = Joi.object({
  employeeId: Joi.number().integer().positive().required(),
  assignmentType: Joi.string().valid(
    'manual',
    'auto_assigned',
    'swap_assigned',
    'emergency_assigned'
  ).default('manual'),
  notes: Joi.string().max(500).optional()
});

// Get available employees
const getAvailableEmployees = Joi.object({
  departmentId: Joi.number().integer().positive().optional(),
  designationId: Joi.number().integer().positive().optional(),
  skillIds: Joi.string().optional(),
  limit: Joi.number().integer().min(1).max(100).default(20)
});

module.exports = {
  getAllInstances,
  createInstance,
  getInstanceById,
  updateInstance,
  deleteInstance,
  overrideRequirementCount,
  getInstanceAssignments,
  assignEmployeeToInstance,
  getAvailableEmployees
};
