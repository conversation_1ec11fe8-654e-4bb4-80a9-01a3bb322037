'use strict';

/**
 * Shift Swap Validators - PRD Implementation
 * 
 * Comprehensive validation schemas for shift swap management
 */

const Joi = require('joi');

// Removed complex param structures

// Enhanced Create swap request - Based on user requirements
const createSwapRequest = Joi.object({
  // ✅ REQUESTER INFORMATION - Auto-set from tenant context in controller
  requesterId: Joi.number().integer().positive().optional()
    .description('Employee ID who is requesting the swap (auto-set from context)'),

  // ✅ CURRENT SHIFT TO SWAP - Enhanced validation (moved from service)
  currentShiftAssignmentId: Joi.number().integer().positive().required()
    .messages({
      'any.required': 'Current shift assignment ID is required',
      'number.positive': 'Current shift assignment ID must be a positive number',
      'number.base': 'Current shift assignment ID must be a valid number'
    })
    .description('Assignment ID of the shift they want to swap away'),

  // ✅ SWAP DATE - Enhanced validation (moved from service)
  swapDate: Joi.date().iso().min('now').required()
    .messages({
      'any.required': 'Swap date is required',
      'date.min': 'Swap date cannot be in the past',
      'date.base': 'Swap date must be a valid date',
      'date.format': 'Swap date must be in ISO format (YYYY-MM-DD)'
    })
    .description('Date when the swap should happen'),

  // ✅ DESIRED SHIFT - Default to null if not provided
  desiredShiftId: Joi.number().integer().positive().optional().allow(null).default(null)
    .description('Specific shift they want (if they have preference)'),

  // ✅ TARGET EMPLOYEE - Default to null if not provided
  targetEmployeeId: Joi.number().integer().positive().optional().allow(null).default(null)
    .description('Specific employee to swap with (if they have someone in mind)'),

  // ✅ FLEXIBLE SWAP OPTIONS - Defaults handled by Joi
  swapWithAnyAvailableEmployee: Joi.boolean().default(false)
    .description('Accept swap with any available employee'),

  swapWithAnyAvailableShift: Joi.boolean().default(false)
    .description('Accept any available shift (flexible about timing)'),

  // ✅ REASON & URGENCY - Enhanced validation (moved from service)
  reasonForSwap: Joi.string().valid(
    'personal_emergency',
    'medical_appointment',
    'family_commitment',
    'education_training',
    'transportation_issue',
    'work_life_balance',
    'other'
  ).required()
    .messages({
      'any.required': 'Reason for swap is required',
      'any.only': 'Invalid reason for swap. Must be one of: personal_emergency, medical_appointment, family_commitment, education_training, transportation_issue, work_life_balance, other'
    })
    .description('Category of reason for swap'),

  reasonDescription: Joi.string().min(10).max(500).required()
    .messages({
      'any.required': 'Reason description is required',
      'string.min': 'Reason description must be at least 10 characters long',
      'string.max': 'Reason description cannot exceed 500 characters',
      'string.empty': 'Reason description cannot be empty'
    })
    .description('Detailed explanation of why swap is needed'),

  urgency: Joi.string().valid('low', 'medium', 'high', 'critical').default('medium')
    .messages({
      'any.only': 'Urgency must be one of: low, medium, high, critical'
    })
    .description('Urgency level of the swap request'),

  // ✅ ADDITIONAL INFORMATION - Defaults handled by Joi
  notes: Joi.string().max(1000).optional().allow(null).default(null)
    .description('Additional notes or special requirements'),

  isRecurring: Joi.boolean().default(false)
    .description('Whether this is a recurring swap request'),

  recurringPattern: Joi.when('isRecurring', {
    is: true,
    then: Joi.object({
      frequency: Joi.string().valid('weekly', 'bi-weekly', 'monthly').required(),
      endDate: Joi.date().iso().min(Joi.ref('swapDate')).required(),
      specificDays: Joi.array().items(Joi.number().integer().min(0).max(6)).optional()
        .description('Days of week (0=Sunday, 6=Saturday)')
    }).required(),
    otherwise: Joi.forbidden()
  }),

  // ✅ NOTIFICATION PREFERENCES - Complete defaults handled by Joi
  notificationPreferences: Joi.object({
    emailNotification: Joi.boolean().default(true),
    smsNotification: Joi.boolean().default(false),
    pushNotification: Joi.boolean().default(true)
  }).optional().default({
    emailNotification: true,
    smsNotification: false,
    pushNotification: true
  })

}).custom((value, helpers) => {
  // At least one swap option must be specified
  const hasSpecificTarget = value.targetEmployeeId || value.desiredShiftId;
  const hasFlexibleOption = value.swapWithAnyAvailableEmployee || value.swapWithAnyAvailableShift;

  if (!hasSpecificTarget && !hasFlexibleOption) {
    return helpers.error('custom.noSwapOptionSpecified');
  }


  // Validation for recurring pattern
  if (value.isRecurring && value.recurringPattern) {
    const swapDate = new Date(value.swapDate);
    const endDate = new Date(value.recurringPattern.endDate);
    const daysDiff = Math.ceil((endDate - swapDate) / (1000 * 60 * 60 * 24));

    if (daysDiff > 365) {
      return helpers.error('custom.recurringTooLong');
    }
  }

  return value;
}).messages({
  'custom.noSwapOptionSpecified': 'Must specify either a target employee/shift OR enable flexible swap options',
 'custom.recurringTooLong': 'Recurring pattern cannot exceed 1 year'
})


// Update swap request - Enhanced with new fields
const updateSwapRequest = Joi.object({
  // SWAP DATE (can be updated if not yet processed)
  swapDate: Joi.date().iso().min('now').optional()
    .description('Updated date when the swap should happen'),

  // DESIRED SHIFT (can change preference)
  desiredShiftId: Joi.number().integer().positive().optional()
    .description('Updated desired shift preference'),

  // TARGET EMPLOYEE (can change target)
  targetEmployeeId: Joi.number().integer().positive().optional()
    .description('Updated target employee'),

  // FLEXIBLE SWAP OPTIONS (can toggle these)
  swapWithAnyAvailableEmployee: Joi.boolean().optional()
    .description('Updated preference for any available employee'),

  swapWithAnyAvailableShift: Joi.boolean().optional()
    .description('Updated preference for any available shift'),

  // REASON UPDATES
  reasonForSwap: Joi.string().valid(
    'personal_emergency', 'medical_appointment', 'family_commitment',
    'education_training', 'transportation_issue', 'work_life_balance', 'other'
  ).optional().description('Updated reason category'),

  reasonDescription: Joi.string().min(10).max(500).optional()
    .description('Updated detailed reason'),

  // URGENCY (can be updated)
  urgency: Joi.string().valid('low', 'medium', 'high', 'critical').optional()
    .description('Updated urgency level'),

  // ADDITIONAL NOTES
  notes: Joi.string().max(1000).optional()
    .description('Additional notes or updates'),

  // RECURRING PATTERN UPDATES
  isRecurring: Joi.boolean().optional(),
  recurringPattern: Joi.when('isRecurring', {
    is: true,
    then: Joi.object({
      frequency: Joi.string().valid('weekly', 'bi-weekly', 'monthly').required(),
      endDate: Joi.date().iso().required(),
      specificDays: Joi.array().items(Joi.number().integer().min(0).max(6)).optional()
    }).required(),
    otherwise: Joi.forbidden()
  }),

  // NOTIFICATION PREFERENCES
  notificationPreferences: Joi.object({
    emailNotification: Joi.boolean().optional(),
    smsNotification: Joi.boolean().optional(),
    pushNotification: Joi.boolean().optional()
  }).optional()

}).min(1).custom((value, helpers) => {
  // Same validation logic as create
  if (value.targetEmployeeId && value.swapWithAnyAvailableEmployee) {
    return helpers.error('custom.conflictingEmployeeOptions');
  }

  if (value.desiredShiftId && value.swapWithAnyAvailableShift) {
    return helpers.error('custom.conflictingShiftOptions');
  }

  // If updating recurring pattern, validate dates
  if (value.isRecurring && value.recurringPattern && value.swapDate) {
    const swapDate = new Date(value.swapDate);
    const endDate = new Date(value.recurringPattern.endDate);
    const daysDiff = Math.ceil((endDate - swapDate) / (1000 * 60 * 60 * 24));

    if (daysDiff > 365) {
      return helpers.error('custom.recurringTooLong');
    }
  }

  return value;
}).messages({
  'custom.conflictingEmployeeOptions': 'Cannot specify both target employee and swap with any available employee',
  'custom.conflictingShiftOptions': 'Cannot specify both desired shift and swap with any available shift',
  'custom.recurringTooLong': 'Recurring pattern cannot exceed 1 year'
})

// Get all swap requests
const getAllSwapRequests = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'executed', 'cancelled').optional(),
    requesterId: Joi.number().integer().positive().optional(),
    targetId: Joi.number().integer().positive().optional(),
    approverId: Joi.number().integer().positive().optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
    departmentId: Joi.number().integer().positive().optional(),
    search: Joi.string().max(100).optional(),
    sortBy: Joi.string().valid('createdAt', 'updatedAt', 'urgency', 'status').default('createdAt'),
    sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC')
  }).custom((value, helpers) => {
    if (value.startDate && value.endDate) {
      if (new Date(value.endDate) <= new Date(value.startDate)) {
        return helpers.error('custom.endDateAfterStart');
      }
    }
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date'
  })

// Get swap request by ID
const getSwapRequestById =  Joi.object({
    includeHistory: Joi.boolean().default(false),
    includeValidation: Joi.boolean().default(true)
  })


// Cancel swap request
const cancelSwapRequest = Joi.object({
    reason: Joi.string().min(10).max(500).required()
  })


// Approve swap request - Enhanced with auto-execution
const approveSwapRequest = Joi.object({
  notes: Joi.string().max(500).optional()
    .description('Approval notes or comments'),

  executeImmediately: Joi.boolean().default(true)
    .description('Auto-execute swap upon approval (default: true)'),

  executionNotes: Joi.string().max(500).optional()
    .description('Additional notes for execution process')
})

// Reject swap request
const rejectSwapRequest =  Joi.object({
    reason: Joi.string().valid(
      'policy_violation',
      'skill_mismatch',
      'scheduling_conflict',
      'insufficient_coverage',
      'other'
    ).required(),
    notes: Joi.string().max(500).optional()
  })


// Execute swap
const executeSwap = Joi.object({
    notes: Joi.string().max(500).optional()
  })


// Get available swaps
const getAvailableSwaps = Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
    departmentId: Joi.number().integer().positive().optional(),
    limit: Joi.number().integer().min(1).max(50).default(20)
  }).custom((value, helpers) => {
    if (value.startDate && value.endDate) {
      if (new Date(value.endDate) <= new Date(value.startDate)) {
        return helpers.error('custom.endDateAfterStart');
      }
      
      // Limit date range to 90 days
      const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
      if (daysDiff > 90) {
        return helpers.error('custom.maxDateRange');
      }
    }
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date',
    'custom.maxDateRange': 'Date range cannot exceed 90 days'
  })


// Validate swap request
const validateSwapRequest = Joi.object({
    requesterAssignmentId: Joi.number().integer().positive().required(),
    targetAssignmentId: Joi.number().integer().positive().required()
  })


// Get swap statistics
const getSwapStatistics = Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
    departmentId: Joi.number().integer().positive().optional(),
    period: Joi.string().pattern(/^\d+d$/).default('30d') // Format: "30d", "7d", etc.
  }).custom((value, helpers) => {
    if (value.startDate && value.endDate) {
      if (new Date(value.endDate) <= new Date(value.startDate)) {
        return helpers.error('custom.endDateAfterStart');
      }
    }
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date'
  })


// Get pending approvals
const getPendingApprovals =  Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(10),
    urgency: Joi.string().valid('low', 'medium', 'high').optional(),
    departmentId: Joi.number().integer().positive().optional()
  })

// Respond to swap request
const respondToSwapRequest = Joi.object({
    response: Joi.string().valid('accept', 'decline').required(),
    notes: Joi.string().max(500).optional()
  })

// Get eligible employees
const getEligibleEmployees = Joi.object({
    limit: Joi.number().integer().min(1).max(100).default(20),
    includeUnavailable: Joi.boolean().default(false)
  })


// Get swap analytics
const getSwapAnalytics = Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
    departmentId: Joi.number().integer().positive().optional(),
    period: Joi.string().pattern(/^\d+d$/).default('30d')
  }).custom((value, helpers) => {
    if (value.startDate && value.endDate) {
      if (new Date(value.endDate) <= new Date(value.startDate)) {
        return helpers.error('custom.endDateAfterStart');
      }
    }
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date'
  })


// Bulk approve swaps
const bulkApproveSwaps =  Joi.object({
    swapIds: Joi.array().items(Joi.number().integer().positive()).min(1).max(50).required(),
    notes: Joi.string().max(500).optional(),
    executeImmediately: Joi.boolean().default(false)
  })

// Get scheduled shifts by date - SIMPLE VERSION
const getScheduledShiftsByDate = Joi.object({
  departmentId: Joi.number().integer().positive().optional(),
  designationId: Joi.number().integer().positive().optional()
})


module.exports = {
  createSwapRequest,
  updateSwapRequest,
  getAllSwapRequests,
  getSwapRequestById,
  cancelSwapRequest,
  approveSwapRequest,
  rejectSwapRequest,
  executeSwap,

  // Additional Endpoints
  respondToSwapRequest,
  getEligibleEmployees,
  getSwapAnalytics,

  // Discovery & Validation
  getAvailableSwaps,
  validateSwapRequest,

  // Analytics & Management
  getSwapStatistics,
  getPendingApprovals,
  bulkApproveSwaps,

  // Shift Discovery
  getScheduledShiftsByDate
};
