'use strict';

const Joi = require('joi');

// Assign employee to shift
const assignEmployeeToShift = Joi.object({
  employeeId: Joi.number().integer().positive().required(),
  notes: Joi.string().max(500).optional(),
  assignmentType: Joi.string().valid(
    'manual_assigned',
    'auto_assigned',
    'bulk_assigned',
    'swap_assigned',
    'overtime_assigned'
  ).default('manual_assigned')
});

// Update assignment
const updateAssignment = Joi.object({
  status: Joi.string().valid('assigned', 'confirmed', 'completed', 'cancelled', 'absent').optional(),
  notes: Joi.string().max(500).optional(),
  actualHours: Joi.number().min(0).max(24).optional(),
  assignmentType: Joi.string().valid(
    'manual_assigned',
    'auto_assigned',
    'bulk_assigned',
    'swap_assigned',
    'overtime_assigned'
  ).optional()
}).min(1);

// Remove assignment
const removeAssignment = Joi.object({
  reason: Joi.string().min(10).max(500).required()
});

// Get all assignments with filtering
const getAllAssignments = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  employeeId: Joi.number().integer().positive().optional(),
  scheduleId: Joi.number().integer().positive().optional(),
  instanceId: Joi.number().integer().positive().optional(),
  status: Joi.string().valid('assigned', 'confirmed', 'completed', 'cancelled', 'absent').optional(),
  assignmentType: Joi.string().valid(
    'manual_assigned',
    'auto_assigned',
    'bulk_assigned',
    'swap_assigned',
    'overtime_assigned'
  ).optional(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  departmentId: Joi.number().integer().positive().optional(),
  search: Joi.string().max(100).optional(),
  sortBy: Joi.string().valid('assignedAt', 'confirmedAt', 'completedAt', 'status').default('assignedAt'),
  sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC')
}).custom((value, helpers) => {
  // Validate date range if both provided
  if (value.startDate && value.endDate) {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
  }
  return value;
}).messages({
  'custom.endDateAfterStart': 'End date must be after start date'
});

// Get assignment by ID
const getAssignmentById = {
  query: Joi.object({
    includeEmployee: Joi.boolean().default(true),
    includeInstance: Joi.boolean().default(true),
    includeHistory: Joi.boolean().default(false)
  })
};

// Confirm assignment
const confirmAssignment = {
  body: Joi.object({
    notes: Joi.string().max(500).optional()
  })
};

// Complete assignment
const completeAssignment = {
  body: Joi.object({
    notes: Joi.string().max(500).optional(),
    actualHours: Joi.number().min(0).max(24).optional()
  })
};

// Mark no-show
const markNoShow = {
  body: Joi.object({
    reason: Joi.string().valid(
      'no_call_no_show',
      'called_in_sick',
      'emergency',
      'transportation_issue',
      'other'
    ).required(),
    notes: Joi.string().max(500).optional()
  })
};

// Bulk assign employees
const bulkAssignEmployees = {
  body: Joi.object({
    assignments: Joi.array().items(
      Joi.object({
        shiftInstanceId: Joi.number().integer().positive().required(),
        employeeId: Joi.number().integer().positive().required(),
        notes: Joi.string().max(500).optional(),
        assignmentType: Joi.string().valid(
          'manual_assigned', 
          'auto_assigned', 
          'bulk_assigned', 
          'swap_assigned',
          'overtime_assigned'
        ).default('bulk_assigned')
      })
    ).min(1).max(100).required() // Max 100 assignments in one bulk operation
  })
};

// Bulk update assignments
const bulkUpdateAssignments = {
  body: Joi.object({
    updates: Joi.array().items(
      Joi.object({
        id: Joi.number().integer().positive().required(),
        status: Joi.string().valid('assigned', 'confirmed', 'completed', 'cancelled', 'absent').optional(),
        notes: Joi.string().max(500).optional(),
        actualHours: Joi.number().min(0).max(24).optional(),
        assignmentType: Joi.string().valid(
          'manual_assigned', 
          'auto_assigned', 
          'bulk_assigned', 
          'swap_assigned',
          'overtime_assigned'
        ).optional()
      }).min(2) // At least ID + one field to update
    ).min(1).max(100).required() // Max 100 updates in one bulk operation
  })
};

// Bulk confirm assignments
const bulkConfirmAssignments = {
  body: Joi.object({
    assignmentIds: Joi.array().items(Joi.number().integer().positive()).min(1).max(100).required(),
    notes: Joi.string().max(500).optional()
  })
};

// Assign employees to schedule during creation
const assignEmployeesToSchedule = Joi.object({
  assignments: Joi.array().items(
    Joi.object({
      rotaShiftId: Joi.number().integer().positive().required(),
      date: Joi.date().iso().required(),
      employeeId: Joi.number().integer().positive().required(),
      designationId: Joi.number().integer().positive().optional(),
      notes: Joi.string().max(500).optional(),
      assignmentType: Joi.string().valid(
        'manual_assigned',
        'auto_assigned',
        'bulk_assigned',
        'swap_assigned',
        'overtime_assigned'
      ).default('manual_assigned')
    })
  ).min(1).max(50).required()
});

// Bulk schedule assign
const bulkScheduleAssign = Joi.object({
  scheduleId: Joi.number().integer().positive().required(),
  assignments: Joi.array().items(
    Joi.object({
      shiftInstanceId: Joi.number().integer().positive().required(),
      employeeId: Joi.number().integer().positive().required(),
      designationId: Joi.number().integer().positive().optional(),
      notes: Joi.string().max(500).optional(),
      assignmentType: Joi.string().valid(
        'manual_assigned',
        'auto_assigned',
        'bulk_assigned',
        'swap_assigned',
        'overtime_assigned'
      ).default('manual_assigned')
    })
  ).min(1).max(100).required()
});

module.exports = {
  assignEmployeeToShift,
  updateAssignment,
  removeAssignment,
  getAllAssignments,
  getAssignmentById,
  confirmAssignment,
  completeAssignment,
  markNoShow,
  bulkAssignEmployees,
  bulkUpdateAssignments,
  bulkConfirmAssignments,
  assignEmployeesToSchedule,
  bulkScheduleAssign
};
