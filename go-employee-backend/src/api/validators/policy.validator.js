'use strict';

const Joi = require('joi');

// Policy CRUD Operations
const getAllPolicies = Joi.object({
  page: Joi.number().integer().min(1),
  limit: Joi.number().integer().min(1),
  title: Joi.string(),
  status: Joi.string().valid('draft', 'active', 'inactive', 'archived'),
  policy_category_id: Joi.number().integer(),
  sort_by: Joi.string(),
  sort_order: Joi.string().valid('asc', 'desc')
  // business_unit_id: Joi.array().items(Joi.number().integer())
});

const createPolicy = Joi.object({
  title: Joi.string().required(),
  description: Joi.string().required(),
  content: Joi.string().required(),
  policy_category_id: Joi.number().integer().required(),
  effective_date: Joi.date(),
  expiry_date: Joi.date().greater(Joi.ref('effective_date')),
  requires_acknowledgment: Joi.boolean().default(false),
  recurrence: Joi.string().allow('', null),
  business_unit_id: Joi.array().items(Joi.number().integer()),
  target_department_ids: Joi.array().items(Joi.number().integer()),
  approver_ids: Joi.array().items(Joi.number().integer()),
});

const updatePolicy = Joi.object({
  title: Joi.string(),
  description: Joi.string(),
  content: Joi.string(),
  policy_category_id: Joi.number().integer(),
  effective_date: Joi.date(),
  expiry_date: Joi.date().greater(Joi.ref('effective_date')),
  requires_acknowledgment: Joi.boolean(),
  business_unit_id: Joi.array().items(Joi.number().integer()),
  target_department_ids: Joi.array().items(Joi.number().integer()),
  approver_ids: Joi.array().items(Joi.number().integer()),
});

// Policy Status Management
const approveRejectPolicy = Joi.object({
  decision: Joi.string().valid('approve', 'reject').required()
});

// Policy Acknowledgments
const getPolicyAcknowledgments = Joi.object({
  page: Joi.number().integer().min(1),
  size: Joi.number().integer().min(1),
  acknowledged: Joi.boolean(),
  start_date: Joi.date(),
  end_date: Joi.date().greater(Joi.ref('start_date'))
});

const acknowledgePolicySchema = Joi.object({
  employee_id: Joi.number().integer().required(),
  acknowledged: Joi.boolean().required()
});

const getEmployeePolicyAcknowledgments = Joi.object({
  page: Joi.number().integer().min(1),
  size: Joi.number().integer().min(1),
  acknowledged: Joi.boolean(),
  start_date: Joi.date(),
  end_date: Joi.date().greater(Joi.ref('start_date'))
});

// Policy Versions
const createPolicyVersion = Joi.object({
  content: Joi.string().required(),
  change_summary: Joi.string().required(),
  created_by: Joi.number().integer().required()
});

// Policy Categories
const getAllPolicyCategories = Joi.object({
  page: Joi.number().integer().min(1),
  size: Joi.number().integer().min(1),
  name: Joi.string()
});

// Policy Analytics
const getPolicyAcknowledgmentAnalytics = Joi.object({
  policy_id: Joi.number().integer(),
  start_date: Joi.date(),
  end_date: Joi.date().greater(Joi.ref('start_date'))
});

const getPolicyCategoryAnalytics = Joi.object({
  start_date: Joi.date(),
  end_date: Joi.date().greater(Joi.ref('start_date'))
});

// Policy Bulk Operations
const bulkDeletePolicies = Joi.object({
  policy_ids: Joi.array().items(Joi.number().integer()).min(1).required()
});

const bulkUpdatePolicyStatus = Joi.object({
  policy_ids: Joi.array().items(Joi.number().integer()).min(1).required(),
  status: Joi.string().valid('draft', 'active', 'inactive', 'archived').required(),
  updated_by: Joi.number().integer().required()
});

module.exports = {
  getAllPolicies,
  createPolicy,
  updatePolicy,
  approveRejectPolicy,
  getPolicyAcknowledgments,
  acknowledgePolicySchema,
  getEmployeePolicyAcknowledgments,
  createPolicyVersion,
  getAllPolicyCategories,
  getPolicyAcknowledgmentAnalytics,
  getPolicyCategoryAnalytics,
  bulkDeletePolicies,
  bulkUpdatePolicyStatus
};
