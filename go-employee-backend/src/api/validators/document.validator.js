'use strict';

const Joi = require('joi');

// Get documents validation schema
const getDocuments = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.base': 'Page must be a number',
    'number.min': 'Page must be at least 1'
  }),
  limit: Joi.number().integer().min(1).max(100).default(10).messages({
    'number.base': 'Limit must be a number',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit cannot exceed 100'
  }),
  documentType: Joi.string().messages({
    'string.base': 'Document type must be a string'
  }),
  documentTypeId: Joi.number().integer().messages({
    'number.base': 'Document type ID must be a number'
  }),
  sortBy: Joi.string().valid('title', 'documentType', 'createdAt', 'updatedAt').default('createdAt').messages({
    'any.only': 'Sort by must be one of: title, documentType, createdAt, updatedAt'
  }),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
    'any.only': 'Sort order must be either asc or desc'
  })
});

// Create document validation schema
const createDocument = Joi.object({
  documentType: Joi.string().required().messages({
    'string.empty': 'Document type is required',
    'any.required': 'Document type is required'
  }),
  documentTypeId: Joi.number().integer().allow(null).messages({
    'number.base': 'Document type ID must be a number'
  }),
  title: Joi.string().required().max(255).messages({
    'string.empty': 'Title is required',
    'any.required': 'Title is required',
    'string.max': 'Title cannot exceed 255 characters'
  }),
  description: Joi.string().allow('', null).max(1000).messages({
    'string.max': 'Description cannot exceed 1000 characters'
  }),
  fileUrl: Joi.string().max(2048).required().messages({
    'string.uri': 'File URL must be a valid URL',
    'string.max': 'File URL cannot exceed 2048 characters',
    'string.empty': 'File URL is required',
    'any.required': 'File URL is required'
  }),
  fileSize: Joi.number().integer().min(1).required().messages({
    'number.base': 'File size must be a number',
    'number.min': 'File size must be at least 1 byte',
    'any.required': 'File size is required'
  }),
  mimeType: Joi.string().required().messages({
    'string.empty': 'MIME type is required',
    'any.required': 'MIME type is required'
  }),
  expiryDate: Joi.date().allow(null).messages({
    'date.base': 'Expiry date must be a valid date'
  }),
  isPublic: Joi.boolean().default(false).messages({
    'boolean.base': 'Is public must be a boolean'
  }),
  tags: Joi.array().items(Joi.string()).default([]).messages({
    'array.base': 'Tags must be an array of strings'
  }),
  businessUnitId: Joi.number().integer().allow(null).messages({
    'number.base': 'Business unit ID must be a number'
  })
});

// Update document validation schema
const updateDocument = Joi.object({
  documentType: Joi.string().messages({
    'string.empty': 'Document type cannot be empty'
  }),
  documentTypeId: Joi.number().integer().allow(null).messages({
    'number.base': 'Document type ID must be a number'
  }),
  title: Joi.string().max(255).messages({
    'string.empty': 'Title cannot be empty',
    'string.max': 'Title cannot exceed 255 characters'
  }),
  description: Joi.string().allow('', null).max(1000).messages({
    'string.max': 'Description cannot exceed 1000 characters'
  }),
  fileUrl: Joi.string().max(2048).messages({
    'string.uri': 'File URL must be a valid URL',
    'string.max': 'File URL cannot exceed 2048 characters',
    'string.empty': 'File URL cannot be empty'
  }),
  fileSize: Joi.number().integer().min(1).messages({
    'number.base': 'File size must be a number',
    'number.min': 'File size must be at least 1 byte'
  }),
  mimeType: Joi.string().messages({
    'string.empty': 'MIME type cannot be empty'
  }),
  expiryDate: Joi.date().allow(null).messages({
    'date.base': 'Expiry date must be a valid date'
  }),
  isPublic: Joi.boolean().messages({
    'boolean.base': 'Is public must be a boolean'
  }),
  tags: Joi.array().items(Joi.string()).messages({
    'array.base': 'Tags must be an array of strings'
  }),
  businessUnitId: Joi.number().integer().allow(null).messages({
    'number.base': 'Business unit ID must be a number'
  })
}).min(1).messages({
  'object.min': 'At least one field must be provided for update'
});

module.exports = {
  getDocuments,
  createDocument,
  updateDocument
};
