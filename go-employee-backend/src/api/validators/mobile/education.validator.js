'use strict';

const Joi = require('joi');

// Get single education record validation schema
const getEducationById = Joi.object({
  educationId: Joi.number().integer().positive().required().messages({
    'number.base': 'Education ID must be a number',
    'number.integer': 'Education ID must be an integer',
    'number.positive': 'Education ID must be positive',
    'any.required': 'Education ID is required'
  })
});

// Add education validation schema
const addEducation = Joi.object({
  institution: Joi.string().trim().min(2).max(255).required().messages({
    'string.empty': 'Institution name is required',
    'string.min': 'Institution name must be at least 2 characters long',
    'string.max': 'Institution name cannot exceed 255 characters',
    'any.required': 'Institution name is required'
  }),
  university: Joi.string().trim().max(255).allow('', null).messages({
    'string.max': 'University name cannot exceed 255 characters'
  }),
  degree: Joi.string().trim().min(2).max(255).required().messages({
    'string.empty': 'Degree is required',
    'string.min': 'Degree must be at least 2 characters long',
    'string.max': 'Degree cannot exceed 255 characters',
    'any.required': 'Degree is required'
  }),
  fieldOfStudy: Joi.string().trim().min(2).max(255).required().messages({
    'string.empty': 'Field of study is required',
    'string.min': 'Field of study must be at least 2 characters long',
    'string.max': 'Field of study cannot exceed 255 characters',
    'any.required': 'Field of study is required'
  }),
  startDate: Joi.date().max('now').required().messages({
    'date.base': 'Start date must be a valid date',
    'date.max': 'Start date cannot be in the future',
    'any.required': 'Start date is required'
  }),
  endDate: Joi.date().greater(Joi.ref('startDate')).max('now').allow(null).messages({
    'date.base': 'End date must be a valid date',
    'date.greater': 'End date must be after start date',
    'date.max': 'End date cannot be in the future'
  }),
  grade: Joi.string().trim().max(50).allow('', null).messages({
    'string.max': 'Grade cannot exceed 50 characters'
  }),
  activities: Joi.string().trim().max(1000).allow('', null).messages({
    'string.max': 'Activities cannot exceed 1000 characters'
  }),
  description: Joi.string().trim().max(1000).allow('', null).messages({
    'string.max': 'Description cannot exceed 1000 characters'
  }),
  skill: Joi.array().items(Joi.string().trim().min(1).max(100)).max(20).allow(null).messages({
    'array.base': 'Skills must be an array',
    'array.max': 'Cannot have more than 20 skills',
    'string.min': 'Each skill must be at least 1 character long',
    'string.max': 'Each skill cannot exceed 100 characters'
  }),
  documentId: Joi.number().integer().positive().allow(null).messages({
    'number.base': 'Document ID must be a number',
    'number.integer': 'Document ID must be an integer',
    'number.positive': 'Document ID must be positive'
  })
}).custom((value, helpers) => {
  // Custom validation to ensure end date is after start date if both are provided
  const { startDate, endDate } = value;
  
  if (startDate && endDate && new Date(endDate) <= new Date(startDate)) {
    return helpers.error('custom.endDateAfterStart');
  }
  
  return value;
}).messages({
  'custom.endDateAfterStart': 'End date must be after start date'
});

// Update education validation schema
const updateEducation = Joi.object({
  educationId: Joi.number().integer().positive().optional().messages({
    'number.base': 'Education ID must be a number',
    'number.integer': 'Education ID must be an integer',
    'number.positive': 'Education ID must be positive',
    'any.required': 'Education ID is optional'
  }),
  institution: Joi.string().trim().min(2).max(255).messages({
    'string.min': 'Institution name must be at least 2 characters long',
    'string.max': 'Institution name cannot exceed 255 characters'
  }),
  university: Joi.string().trim().max(255).allow('', null).messages({
    'string.max': 'University name cannot exceed 255 characters'
  }),
  degree: Joi.string().trim().min(2).max(255).messages({
    'string.min': 'Degree must be at least 2 characters long',
    'string.max': 'Degree cannot exceed 255 characters'
  }),
  fieldOfStudy: Joi.string().trim().min(2).max(255).messages({
    'string.min': 'Field of study must be at least 2 characters long',
    'string.max': 'Field of study cannot exceed 255 characters'
  }),
  startDate: Joi.date().max('now').messages({
    'date.base': 'Start date must be a valid date',
    'date.max': 'Start date cannot be in the future'
  }),
  endDate: Joi.date().greater(Joi.ref('startDate')).max('now').allow(null).messages({
    'date.base': 'End date must be a valid date',
    'date.greater': 'End date must be after start date',
    'date.max': 'End date cannot be in the future'
  }),
  grade: Joi.string().trim().max(50).allow('', null).messages({
    'string.max': 'Grade cannot exceed 50 characters'
  }),
  activities: Joi.string().trim().max(1000).allow('', null).messages({
    'string.max': 'Activities cannot exceed 1000 characters'
  }),
  description: Joi.string().trim().max(1000).allow('', null).messages({
    'string.max': 'Description cannot exceed 1000 characters'
  }),
  skill: Joi.array().items(Joi.string().trim().min(1).max(100)).max(20).allow(null).messages({
    'array.base': 'Skills must be an array',
    'array.max': 'Cannot have more than 20 skills',
    'string.min': 'Each skill must be at least 1 character long',
    'string.max': 'Each skill cannot exceed 100 characters'
  }),
  documentId: Joi.number().integer().positive().allow(null).messages({
    'number.base': 'Document ID must be a number',
    'number.integer': 'Document ID must be an integer',
    'number.positive': 'Document ID must be positive'
  })
}).min(1).custom((value, helpers) => {
  // Custom validation to ensure end date is after start date if both are provided
  const { startDate, endDate } = value;
  
  if (startDate && endDate && new Date(endDate) <= new Date(startDate)) {
    return helpers.error('custom.endDateAfterStart');
  }
  
  return value;
}).messages({
  'custom.endDateAfterStart': 'End date must be after start date'
});

// Delete education validation schema
const deleteEducation = Joi.object({
  educationId: Joi.number().integer().positive().required().messages({
    'number.base': 'Education ID must be a number',
    'number.integer': 'Education ID must be an integer',
    'number.positive': 'Education ID must be positive',
    'any.required': 'Education ID is required'
  })
});

module.exports = {
  getEducationById,
  addEducation,
  updateEducation,
  deleteEducation
};
