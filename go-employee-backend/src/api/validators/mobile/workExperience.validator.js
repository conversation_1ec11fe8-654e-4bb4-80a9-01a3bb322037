'use strict';

const Joi = require('joi');

/**
 * Mobile Work Experience Validators
 */

/**
 * Validate get work experience by ID parameters
 */
const getWorkExperienceById = {
  params: Joi.object({
    experienceId: Joi.number().integer().positive().required()
      .messages({
        'number.base': 'Experience ID must be a number',
        'number.integer': 'Experience ID must be an integer',
        'number.positive': 'Experience ID must be positive',
        'any.required': 'Experience ID is required'
      })
  })
};

/**
 * Validate add work experience request
 */
const addWorkExperience =  Joi.object({
    companyName: Joi.string().trim().min(1).max(255).required()
      .messages({
        'string.base': 'Company name must be a string',
        'string.empty': 'Company name cannot be empty',
        'string.min': 'Company name must be at least 1 character long',
        'string.max': 'Company name cannot exceed 255 characters',
        'any.required': 'Company name is required'
      }),
    position: Joi.string().required().messages({
        'string.empty': 'Position is required',
        'any.required': 'Position is required'
      }),
    
    department: Joi.string().trim().max(255).optional().allow('', null)
      .messages({
        'string.base': 'Department must be a string',
        'string.max': 'Department cannot exceed 255 characters'
      }),
    
    location: Joi.string().trim().max(255).optional().allow('', null)
      .messages({
        'string.base': 'Location must be a string',
        'string.max': 'Location cannot exceed 255 characters'
      }),
    
    startDate: Joi.date().iso().required()
      .messages({
        'date.base': 'Start date must be a valid date',
        'date.format': 'Start date must be in ISO format (YYYY-MM-DD)',
        'any.required': 'Start date is required'
      }),
    
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional().allow(null)
      .messages({
        'date.base': 'End date must be a valid date',
        'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
        'date.min': 'End date must be after start date'
      }),
    
    currentlyWorking: Joi.boolean().optional().default(false)
      .messages({
        'boolean.base': 'Currently working must be a boolean'
      }),
    
    jobDescription: Joi.string().trim().max(2000).optional().allow('', null)
      .messages({
        'string.base': 'Job description must be a string',
        'string.max': 'Job description cannot exceed 2000 characters'
      }),
    
    skills: Joi.alternatives().try(
      Joi.array().items(Joi.string().trim().min(1).max(100)),
      Joi.string().trim().max(1000)
    ).optional().allow('', null)
      .messages({
        'alternatives.types': 'Skills must be an array of strings or a single string',
        'string.max': 'Skills string cannot exceed 1000 characters',
        'array.base': 'Skills must be an array'
      }),
    
    achievements: Joi.string().trim().max(2000).optional().allow('', null)
      .messages({
        'string.base': 'Achievements must be a string',
        'string.max': 'Achievements cannot exceed 2000 characters'
      }),
    
    reasonForLeaving: Joi.string().trim().max(500).optional().allow('', null)
      .messages({
        'string.base': 'Reason for leaving must be a string',
        'string.max': 'Reason for leaving cannot exceed 500 characters'
      }),
    
    salary: Joi.number().positive().optional().allow(null)
      .messages({
        'number.base': 'Salary must be a number',
        'number.positive': 'Salary must be positive'
      }),
    
    currency: Joi.string().trim().length(3).uppercase().optional().allow('', null)
      .messages({
        'string.base': 'Currency must be a string',
        'string.length': 'Currency must be exactly 3 characters (e.g., USD, INR)',
        'string.uppercase': 'Currency must be uppercase'
      }),
    
    employmentType: Joi.string().trim().valid(
      'full-time', 'part-time', 'contract', 'freelance', 'internship', 'temporary'
    ).optional().allow('', null)
      .messages({
        'string.base': 'Employment type must be a string',
        'any.only': 'Employment type must be one of: full-time, part-time, contract, freelance, internship, temporary'
      }),
    
    documentId: Joi.number().integer().positive().optional()
      .messages({
        'number.base': 'Document ID must be a number',
        'number.integer': 'Document ID must be an integer',
        'number.positive': 'Document ID must be positive'
      }),
      
  }).custom((value, helpers) => {
    // If currently working is true, end date should not be provided
    if (value.currentlyWorking && value.endDate) {
      return helpers.error('custom.currentlyWorkingWithEndDate');
    }
    
    // If currently working is false, end date should be provided
    if (value.currentlyWorking === false && !value.endDate) {
      return helpers.error('custom.notCurrentlyWorkingWithoutEndDate');
    }
    
    return value;
  }, 'Work experience validation').messages({
    'custom.currentlyWorkingWithEndDate': 'End date should not be provided when currently working',
    'custom.notCurrentlyWorkingWithoutEndDate': 'End date is required when not currently working'
  })


/**
 * Validate update work experience request
 */
const updateWorkExperience =  Joi.object({
    companyName: Joi.string().trim().min(1).max(255).optional()
      .messages({
        'string.base': 'Company name must be a string',
        'string.empty': 'Company name cannot be empty',
        'string.min': 'Company name must be at least 1 character long',
        'string.max': 'Company name cannot exceed 255 characters'
      }),
    
    jobTitle: Joi.string().trim().min(1).max(255).optional()
      .messages({
        'string.base': 'Job title must be a string',
        'string.empty': 'Job title cannot be empty',
        'string.min': 'Job title must be at least 1 character long',
        'string.max': 'Job title cannot exceed 255 characters'
      }),
    
    department: Joi.string().trim().max(255).optional().allow('', null)
      .messages({
        'string.base': 'Department must be a string',
        'string.max': 'Department cannot exceed 255 characters'
      }),
    
    location: Joi.string().trim().max(255).optional().allow('', null)
      .messages({
        'string.base': 'Location must be a string',
        'string.max': 'Location cannot exceed 255 characters'
      }),
    
    startDate: Joi.date().iso().optional()
      .messages({
        'date.base': 'Start date must be a valid date',
        'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
      }),
    
    endDate: Joi.date().iso().optional().allow(null)
      .messages({
        'date.base': 'End date must be a valid date',
        'date.format': 'End date must be in ISO format (YYYY-MM-DD)'
      }),
    position: Joi.string().required().messages({
        'string.empty': 'Position is required',
        'any.required': 'Position is required'
      }),
    currentlyWorking: Joi.boolean().optional()
      .messages({
        'boolean.base': 'Currently working must be a boolean'
      }),
    
    jobDescription: Joi.string().trim().max(2000).optional().allow('', null)
      .messages({
        'string.base': 'Job description must be a string',
        'string.max': 'Job description cannot exceed 2000 characters'
      }),
    
    skills: Joi.alternatives().try(
      Joi.array().items(Joi.string().trim().min(1).max(100)),
      Joi.string().trim().max(1000)
    ).optional().allow('', null)
      .messages({
        'alternatives.types': 'Skills must be an array of strings or a single string',
        'string.max': 'Skills string cannot exceed 1000 characters',
        'array.base': 'Skills must be an array'
      }),
    
    achievements: Joi.string().trim().max(2000).optional().allow('', null)
      .messages({
        'string.base': 'Achievements must be a string',
        'string.max': 'Achievements cannot exceed 2000 characters'
      }),
    
    reasonForLeaving: Joi.string().trim().max(500).optional().allow('', null)
      .messages({
        'string.base': 'Reason for leaving must be a string',
        'string.max': 'Reason for leaving cannot exceed 500 characters'
      }),
    
    salary: Joi.number().positive().optional().allow(null)
      .messages({
        'number.base': 'Salary must be a number',
        'number.positive': 'Salary must be positive'
      }),
    
    currency: Joi.string().trim().length(3).uppercase().optional().allow('', null)
      .messages({
        'string.base': 'Currency must be a string',
        'string.length': 'Currency must be exactly 3 characters (e.g., USD, INR)',
        'string.uppercase': 'Currency must be uppercase'
      }),
    
    employmentType: Joi.string().trim().valid(
      'full-time', 'part-time', 'contract', 'freelance', 'internship', 'temporary'
    ).optional().allow('', null)
      .messages({
        'string.base': 'Employment type must be a string',
        'any.only': 'Employment type must be one of: full-time, part-time, contract, freelance, internship, temporary'
      }),
    
    documentId: Joi.number().integer().positive().optional()
      .messages({
        'number.base': 'Document ID must be a number',
        'number.integer': 'Document ID must be an integer',
        'number.positive': 'Document ID must be positive'
      })
  }).min(1).messages({
    'object.min': 'At least one field must be provided for update'
  })

/**
 * Validate delete work experience parameters
 */
const deleteWorkExperience =  Joi.object({
    experienceId: Joi.number().integer().positive().required()
      .messages({
        'number.base': 'Experience ID must be a number',
        'number.integer': 'Experience ID must be an integer',
        'number.positive': 'Experience ID must be positive',
        'any.required': 'Experience ID is required'
      })
  })


module.exports = {
  getWorkExperienceById,
  addWorkExperience,
  updateWorkExperience,
  deleteWorkExperience
};
