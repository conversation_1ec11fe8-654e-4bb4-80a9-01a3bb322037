'use strict';

/**
 * Mobile Schedule Validators - PRD Implementation
 * 
 * Validation schemas for mobile schedule management
 */

const Joi = require('joi');

// Common validation schemas
const assignmentIdParam = {
  params: Joi.object({
    assignmentId: Joi.number().integer().positive().required()
  })
};

// Get my schedule
const getMySchedule = {
  query: Joi.object({
    startDate: Joi.date().iso().default(() => new Date().toISOString().split('T')[0]),
    endDate: Joi.date().iso().default(() => {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date.toISOString().split('T')[0];
    }),
    status: Joi.string().valid('assigned', 'confirmed', 'completed', 'cancelled', 'no_show').optional(),
    includeSwapRequests: Joi.boolean().default(true)
  }).custom((value, helpers) => {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
    
    // Limit date range to 90 days for mobile
    const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
    if (daysDiff > 90) {
      return helpers.error('custom.maxDateRange');
    }
    
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date',
    'custom.maxDateRange': 'Date range cannot exceed 90 days'
  })
};

// Get upcoming shifts
const getUpcomingShifts = {
  query: Joi.object({
    limit: Joi.number().integer().min(1).max(50).default(10)
  })
};

// Get calendar view
const getCalendarView = {
  query: Joi.object({
    month: Joi.date().iso().default(() => new Date().toISOString().split('T')[0].substring(0, 7)),
    view: Joi.string().valid('month', 'week').default('month')
  })
};

// Confirm shift
const confirmShift = {
  body: Joi.object({
    notes: Joi.string().max(500).optional()
  })
};

// Get shift notifications
const getShiftNotifications = {
  query: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20),
    type: Joi.string().valid('all', 'shift_assigned', 'shift_changed', 'swap_request', 'reminder').default('all'),
    unreadOnly: Joi.boolean().default(false)
  })
};

module.exports = {
  assignmentIdParam,
  getMySchedule,
  getUpcomingShifts,
  getCalendarView,
  confirmShift,
  getShiftNotifications
};
