'use strict';

const Joi = require('joi');

/**
 * Mobile Document Validators
 * Following the mobile API specification requirements
 */

// Get documents validation schema
const getDocuments = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.base': 'Page must be a number',
    'number.min': 'Page must be at least 1'
  }),
  size: Joi.number().integer().min(1).max(100).default(10).messages({
    'number.base': 'Size must be a number',
    'number.min': 'Size must be at least 1',
    'number.max': 'Size cannot exceed 100'
  }),
  document_type: Joi.string().messages({
    'string.base': 'Document type must be a string'
  }),
  document_type_id: Joi.number().integer().messages({
    'number.base': 'Document type ID must be a number'
  }),
  sort_by: Joi.string().valid('title', 'document_type', 'created_at', 'updated_at').default('created_at').messages({
    'any.only': 'Sort by must be one of: title, document_type, created_at, updated_at'
  }),
  sort_order: Joi.string().valid('asc', 'desc').default('desc').messages({
    'any.only': 'Sort order must be either asc or desc'
  }),
  status: Joi.string().valid('active', 'inactive', 'all').default('active').messages({
    'any.only': 'Status must be one of: active, inactive, all'
  }),
  is_public: Joi.boolean().messages({
    'boolean.base': 'Is public must be a boolean'
  }),
  search: Joi.string().max(255).messages({
    'string.max': 'Search term cannot exceed 255 characters'
  })
});

// Create document validation schema
const createDocument = Joi.object({
  document_type: Joi.string().optional().messages({
    'string.empty': 'Document type is required',
    'any.required': 'Document type is required'
  }),
  document_type_id: Joi.number().integer().allow(null).messages({
    'number.base': 'Document type ID must be a number'
  }),
  title: Joi.string().required().max(255).messages({
    'string.empty': 'Title is required',
    'any.required': 'Title is required',
    'string.max': 'Title cannot exceed 255 characters'
  }),
  description: Joi.string().allow('', null).max(1000).messages({
    'string.max': 'Description cannot exceed 1000 characters'
  }),
  file_url: Joi.string().max(2048).required().messages({
    'string.uri': 'File URL must be a valid URL',
    'string.max': 'File URL cannot exceed 2048 characters',
    'string.empty': 'File URL is required',
    'any.required': 'File URL is required'
  }),
  file_size: Joi.number().integer().min(1).optional().messages({
    'number.base': 'File size must be a number',
    'number.min': 'File size must be at least 1 byte',
    'any.required': 'File size is required'
  }),
  mime_type: Joi.string().optional().messages({
    'string.empty': 'MIME type is required',
    'any.required': 'MIME type is required'
  }),
  expiry_date: Joi.date().allow(null).messages({
    'date.base': 'Expiry date must be a valid date'
  }),
  is_public: Joi.boolean().default(false).messages({
    'boolean.base': 'Is public must be a boolean'
  }),
  tags: Joi.string().optional().messages({
    'array.base': 'Tags must be an array of strings'
  }),
  business_unit_id: Joi.number().integer().allow(null).messages({
    'number.base': 'Business unit ID must be a number'
  }),
  notes: Joi.string().allow('', null).max(500).messages({
    'string.max': 'Notes cannot exceed 500 characters'
  })
});

// Update document validation schema (for PUT and PATCH)
const updateDocument = Joi.object({
  document_type: Joi.string().messages({
    'string.empty': 'Document type cannot be empty'
  }),
  document_type_id: Joi.number().integer().allow(null).messages({
    'number.base': 'Document type ID must be a number'
  }),
  title: Joi.string().max(255).messages({
    'string.empty': 'Title cannot be empty',
    'string.max': 'Title cannot exceed 255 characters'
  }),
  description: Joi.string().allow('', null).max(1000).messages({
    'string.max': 'Description cannot exceed 1000 characters'
  }),
  file_url: Joi.string().max(2048).messages({
    'string.uri': 'File URL must be a valid URL',
    'string.max': 'File URL cannot exceed 2048 characters',
    'string.empty': 'File URL cannot be empty'
  }),
  file_size: Joi.number().integer().min(1).messages({
    'number.base': 'File size must be a number',
    'number.min': 'File size must be at least 1 byte'
  }),
  mime_type: Joi.string().messages({
    'string.empty': 'MIME type cannot be empty'
  }),
  expiry_date: Joi.date().allow(null).messages({
    'date.base': 'Expiry date must be a valid date'
  }),
  is_public: Joi.boolean().messages({
    'boolean.base': 'Is public must be a boolean'
  }),
  tags: Joi.string().optional().messages({
    'array.base': 'Tags must be an array of strings'
  }),
  business_unit_id: Joi.number().integer().allow(null).messages({
    'number.base': 'Business unit ID must be a number'
  }),
  notes: Joi.string().allow('', null).max(500).messages({
    'string.max': 'Notes cannot exceed 500 characters'
  })
}).min(1).messages({
  'object.min': 'At least one field must be provided for update'
});

// Get document types validation schema
const getDocumentTypes = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.base': 'Page must be a number',
    'number.min': 'Page must be at least 1'
  }),
  size: Joi.number().integer().min(1).max(100).default(10).messages({
    'number.base': 'Size must be a number',
    'number.min': 'Size must be at least 1',
    'number.max': 'Size cannot exceed 100'
  }),
  category: Joi.string().valid(
    'identity', 
    'education', 
    'employment', 
    'financial', 
    'legal', 
    'medical', 
    'visa', 
    'certification', 
    'other'
  ).messages({
    'any.only': 'Category must be one of the allowed values'
  }),
  is_required: Joi.boolean().messages({
    'boolean.base': 'Is required must be a boolean'
  }),
  is_active: Joi.boolean().default(true).messages({
    'boolean.base': 'Is active must be a boolean'
  }),
  search: Joi.string().max(255).messages({
    'string.max': 'Search term cannot exceed 255 characters'
  })
});

// Create document type validation schema
const createDocumentType = Joi.object({
  name: Joi.string().required().max(100).messages({
    'string.empty': 'Name is required',
    'string.max': 'Name cannot exceed 100 characters',
    'any.required': 'Name is required'
  }),
  code: Joi.string().required().max(50).pattern(/^[a-z0-9_]+$/).messages({
    'string.empty': 'Code is required',
    'string.max': 'Code cannot exceed 50 characters',
    'string.pattern.base': 'Code can only contain lowercase letters, numbers, and underscores',
    'any.required': 'Code is required'
  }),
  description: Joi.string().allow('').max(500).messages({
    'string.max': 'Description cannot exceed 500 characters'
  }),
  category: Joi.string().valid(
    'identity', 
    'education', 
    'employment', 
    'financial', 
    'legal', 
    'medical', 
    'visa', 
    'certification', 
    'other'
  ).default('other').messages({
    'any.only': 'Category must be one of the allowed values'
  }),
  is_required: Joi.boolean().default(false).messages({
    'boolean.base': 'Is required must be a boolean'
  }),
  validity_duration: Joi.number().integer().allow(null).min(1).messages({
    'number.base': 'Validity duration must be a number',
    'number.min': 'Validity duration must be at least 1 day'
  }),
  allowed_file_types: Joi.array().items(Joi.string()).default(['pdf', 'jpg', 'jpeg', 'png']).messages({
    'array.base': 'Allowed file types must be an array'
  }),
  max_file_size: Joi.number().integer().min(1).default(5242880).messages({
    'number.base': 'Max file size must be a number',
    'number.min': 'Max file size must be at least 1 byte'
  }),
  requires_verification: Joi.boolean().default(false).messages({
    'boolean.base': 'Requires verification must be a boolean'
  }),
  is_active: Joi.boolean().default(true).messages({
    'boolean.base': 'Is active must be a boolean'
  })
});

// Update document type validation schema
const updateDocumentType = Joi.object({
  name: Joi.string().max(100).messages({
    'string.max': 'Name cannot exceed 100 characters'
  }),
  code: Joi.string().max(50).pattern(/^[a-z0-9_]+$/).messages({
    'string.max': 'Code cannot exceed 50 characters',
    'string.pattern.base': 'Code can only contain lowercase letters, numbers, and underscores'
  }),
  description: Joi.string().allow('').max(500).messages({
    'string.max': 'Description cannot exceed 500 characters'
  }),
  category: Joi.string().valid(
    'identity', 
    'education', 
    'employment', 
    'financial', 
    'legal', 
    'medical', 
    'visa', 
    'certification', 
    'other'
  ).messages({
    'any.only': 'Category must be one of the allowed values'
  }),
  is_required: Joi.boolean().messages({
    'boolean.base': 'Is required must be a boolean'
  }),
  validity_duration: Joi.number().integer().allow(null).min(1).messages({
    'number.base': 'Validity duration must be a number',
    'number.min': 'Validity duration must be at least 1 day'
  }),
  allowed_file_types: Joi.array().items(Joi.string()).messages({
    'array.base': 'Allowed file types must be an array'
  }),
  max_file_size: Joi.number().integer().min(1).messages({
    'number.base': 'Max file size must be a number',
    'number.min': 'Max file size must be at least 1 byte'
  }),
  requires_verification: Joi.boolean().messages({
    'boolean.base': 'Requires verification must be a boolean'
  }),
  is_active: Joi.boolean().messages({
    'boolean.base': 'Is active must be a boolean'
  })
}).min(1).messages({
  'object.min': 'At least one field must be provided for update'
});

module.exports = {
  getDocuments,
  createDocument,
  updateDocument,
  getDocumentTypes,
  createDocumentType,
  updateDocumentType
};
