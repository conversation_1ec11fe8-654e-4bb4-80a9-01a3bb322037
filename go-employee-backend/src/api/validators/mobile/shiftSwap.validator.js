'use strict';

/**
 * Mobile Shift Swap Validators - PRD Implementation
 * 
 * Validation schemas for mobile shift swap management
 */

const Joi = require('joi');

// Common validation schemas
const idParam = {
  params: Joi.object({
    id: Joi.number().integer().positive().required()
  })
};

// Get my swap requests
const getMySwapRequests = {
  query: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'executed', 'cancelled').optional(),
    type: Joi.string().valid('sent', 'received', 'all').default('all')
  })
};

// Create swap request
const createSwapRequest = {
  body: Joi.object({
    requesterAssignmentId: Joi.number().integer().positive().required(),
    targetAssignmentId: Joi.number().integer().positive().optional(),
    targetEmployeeId: Joi.number().integer().positive().optional(),
    swapType: Joi.string().valid('direct', 'open').default('direct'),
    reason: Joi.string().min(10).max(500).required(),
    notes: Joi.string().max(500).optional(),
    urgency: Joi.string().valid('low', 'medium', 'high').default('medium')
  }).custom((value, helpers) => {
    // For direct swaps, either targetAssignmentId or targetEmployeeId must be provided
    if (value.swapType === 'direct') {
      if (!value.targetAssignmentId && !value.targetEmployeeId) {
        return helpers.error('custom.targetRequired');
      }
    }
    
    // Cannot have both targetAssignmentId and targetEmployeeId
    if (value.targetAssignmentId && value.targetEmployeeId) {
      return helpers.error('custom.onlyOneTarget');
    }
    
    return value;
  }).messages({
    'custom.targetRequired': 'Either targetAssignmentId or targetEmployeeId is required for direct swaps',
    'custom.onlyOneTarget': 'Cannot specify both targetAssignmentId and targetEmployeeId'
  })
};

// Respond to swap request
const respondToSwapRequest = {
  body: Joi.object({
    action: Joi.string().valid('accept', 'decline').required(),
    notes: Joi.string().max(500).optional()
  })
};

// Cancel swap request
const cancelSwapRequest = {
  body: Joi.object({
    reason: Joi.string().min(10).max(500).required()
  })
};

// Get available swaps
const getAvailableSwaps = {
  query: Joi.object({
    myShiftId: Joi.number().integer().positive().optional(),
    startDate: Joi.date().iso().default(() => new Date().toISOString().split('T')[0]),
    endDate: Joi.date().iso().default(() => {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date.toISOString().split('T')[0];
    }),
    departmentId: Joi.number().integer().positive().optional(),
    limit: Joi.number().integer().min(1).max(50).default(20)
  }).custom((value, helpers) => {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
    
    // Limit date range to 60 days for mobile
    const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
    if (daysDiff > 60) {
      return helpers.error('custom.maxDateRange');
    }
    
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date',
    'custom.maxDateRange': 'Date range cannot exceed 60 days'
  })
};

// Get my swappable shifts
const getMySwappableShifts = {
  query: Joi.object({
    startDate: Joi.date().iso().default(() => new Date().toISOString().split('T')[0]),
    endDate: Joi.date().iso().default(() => {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date.toISOString().split('T')[0];
    }),
    limit: Joi.number().integer().min(1).max(50).default(20)
  }).custom((value, helpers) => {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
    
    // Limit date range to 60 days for mobile
    const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
    if (daysDiff > 60) {
      return helpers.error('custom.maxDateRange');
    }
    
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date',
    'custom.maxDateRange': 'Date range cannot exceed 60 days'
  })
};

module.exports = {
  idParam,
  getMySwapRequests,
  createSwapRequest,
  respondToSwapRequest,
  cancelSwapRequest,
  getAvailableSwaps,
  getMySwappableShifts
};
