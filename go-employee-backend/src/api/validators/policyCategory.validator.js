'use strict';

const Joi = require('joi');

// Get all policy categories
const getAllPolicyCategories = Joi.object({
  page: Joi.number().integer().min(1),
  limit: Joi.number().integer().min(1),
  search: Joi.string(),
  sortBy: Joi.string().valid('name', 'createdAt', 'updatedAt'),
  sortOrder: Joi.string().valid('asc', 'desc')
});

// Create policy category
const createPolicyCategory = Joi.object({
  name: Joi.string().required().max(100).required(),
  description: Joi.string().max(500).optional().allow('', null),
  isActive: Joi.boolean().optional().default(true)
});

// Update policy category
const updatePolicyCategory = Joi.object({
  name: Joi.string().required().max(100).required(),
  description: Joi.string().max(500).optional().allow('', null),
  isActive: Joi.boolean().optional().default(true)
});

// Get policies by category
const getPoliciesByCategory = Joi.object({
  page: Joi.number().integer().min(1),
  size: Joi.number().integer().min(1),
  status: Joi.string().valid('draft', 'active', 'inactive', 'archived')
});

module.exports = {
  getAllPolicyCategories,
  createPolicyCategory,
  updatePolicyCategory,
  getPoliciesByCategory
};