const Joi = require('joi');

// Designation override schema
const designationOverrideSchema = Joi.object({
  designationId: Joi.number().integer().positive().required(),
  requiredCount: Joi.number().integer().min(0).required()
});

// Day configuration schema
const dayConfigSchema = Joi.object({
  isWorkingDay: Joi.boolean().default(true),
  dayType: Joi.string().valid('regular', 'weekend', 'holiday').default('regular'),
  shifts: Joi.array().items(
    Joi.object({
      rotaShiftId: Joi.number().integer().positive().required(),
      priority: Joi.number().integer().min(1).max(10).default(1),
      isFlexible: Joi.boolean().default(false),
      notes: Joi.string().max(500).optional(),

      // ✅ Override system
      useDesignationOverride: Joi.boolean().default(false),
      designationOverrides: Joi.when('useDesignationOverride', {
        is: true,
        then: Joi.array().items(designationOverrideSchema).min(1).required(),
        otherwise: Joi.forbidden()
      })
    })
  ).default([])
});

// Day configurations schema
const dayConfigurationsSchema = Joi.object({
  monday: dayConfigSchema.optional(),
  tuesday: dayConfigSchema.optional(),
  wednesday: dayConfigSchema.optional(),
  thursday: dayConfigSchema.optional(),
  friday: dayConfigSchema.optional(),
  saturday: dayConfigSchema.optional(),
  sunday: dayConfigSchema.optional()
}).min(1);

// Assignment rules schema
const assignmentRulesSchema = Joi.object({
  enabled: Joi.boolean().default(true),
  preferenceOrder: Joi.array().items(
    Joi.string().valid('availability', 'skills', 'seniority', 'workload')
  ).default(['availability', 'skills']),
  avoidConsecutiveNights: Joi.boolean().default(true),
  maxConsecutiveDays: Joi.number().integer().min(1).max(14).default(6),
  minRestBetweenShifts: Joi.number().integer().min(8).max(24).default(12),
  skillMatching: Joi.string().valid('strict', 'preferred', 'optional').default('preferred'),
  balanceWorkload: Joi.boolean().default(true)
});

// Create template validation
const createTemplate = Joi.object({
  name: Joi.string().min(3).max(100).required(),
  code: Joi.string().min(3).max(20).pattern(/^[A-Z0-9_-]+$/).required(),
  description: Joi.string().max(500).optional(),
  category: Joi.string().valid('regular', 'overtime', 'holiday', 'emergency', 'training').default('regular'),
  templateType: Joi.string().valid('weekly', 'bi_weekly', 'monthly', 'custom').default('weekly'),
  dayConfigurations: dayConfigurationsSchema.required(),
  autoAssignmentEnabled: Joi.boolean().default(true),
  assignmentRules: assignmentRulesSchema.optional(),
  isActive: Joi.boolean().default(true),
  isDefault: Joi.boolean().default(false)
});

// Update template validation
const updateTemplate = Joi.object({
  name: Joi.string().min(3).max(100).optional(),
  code: Joi.string().min(3).max(20).pattern(/^[A-Z0-9_-]+$/).optional(),
  description: Joi.string().max(500).optional(),
  category: Joi.string().valid('regular', 'overtime', 'holiday', 'emergency', 'training').optional(),
  templateType: Joi.string().valid('weekly', 'bi_weekly', 'monthly', 'custom').optional(),
  dayConfigurations: dayConfigurationsSchema.optional(),
  autoAssignmentEnabled: Joi.boolean().optional(),
  assignmentRules: assignmentRulesSchema.optional(),
  isActive: Joi.boolean().optional(),
  isDefault: Joi.boolean().optional()
});

// Get all templates validation
const getAllTemplates = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  search: Joi.string().max(100).optional(),
  category: Joi.string().valid('regular', 'overtime', 'holiday', 'emergency', 'training').optional(),
  isActive: Joi.boolean().optional(),
  isDefault: Joi.boolean().optional(),
  sortBy: Joi.string().valid('name', 'code', 'category', 'createdAt', 'updatedAt', 'usageCount').default('createdAt'),
  sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC')
});

// Get template by ID validation
const getTemplateById = Joi.object({
  id: Joi.number().integer().positive().required()
});

// Delete template validation
const deleteTemplate = Joi.object({
  id: Joi.number().integer().positive().required()
});

// Clone template validation
const cloneTemplate = Joi.object({
  name: Joi.string().min(3).max(100).required(),
  code: Joi.string().min(3).max(20).pattern(/^[A-Z0-9_-]+$/).required(),
  description: Joi.string().max(500).optional()
});

// Set as default validation
const setAsDefault = Joi.object({
  id: Joi.number().integer().positive().required()
});

// Generate instances validation
const generateInstancesParams = Joi.object({
  id: Joi.string().pattern(/^\d+$/).required().messages({
    'string.pattern.base': 'ID must be a valid number'
  })
});

const generateInstancesBody = Joi.object({
  startDate: Joi.date().iso().required(),
  endDate: Joi.date().iso().min(Joi.ref('startDate')).required()
});

// Custom validation functions
const validateDayConfiguration = (dayConfig) => {
  if (!dayConfig.isWorkingDay) {
    // Non-working days should not have shifts
    if (dayConfig.shifts && dayConfig.shifts.length > 0) {
      throw new Error('Non-working days cannot have shifts');
    }
  } else {
    // Working days should have at least one shift
    if (!dayConfig.shifts || dayConfig.shifts.length === 0) {
      throw new Error('Working days must have at least one shift');
    }
  }

  // Validate shift counts
  if (dayConfig.shifts) {
    dayConfig.shifts.forEach((shift, index) => {
      if (shift.maxCount && shift.requiredCount && shift.maxCount < shift.requiredCount) {
        throw new Error(`Shift ${index + 1}: maxCount cannot be less than requiredCount`);
      }
    });
  }

  return dayConfig;
};

const validateDayConfigurations = (dayConfigurations) => {
  const hasWorkingDay = Object.values(dayConfigurations).some(config => config.isWorkingDay);
  
  if (!hasWorkingDay) {
    throw new Error('Template must have at least one working day');
  }

  // Validate each day configuration
  Object.entries(dayConfigurations).forEach(([day, config]) => {
    try {
      validateDayConfiguration(config);
    } catch (error) {
      throw new Error(`${day}: ${error.message}`);
    }
  });

  return dayConfigurations;
};

// Enhanced validation with custom rules
const createTemplateEnhanced = createTemplate.custom((value, helpers) => {
  try {
    validateDayConfigurations(value.dayConfigurations);
    return value;
  } catch (error) {
    return helpers.error('any.custom', { message: error.message });
  }
});

const updateTemplateEnhanced = updateTemplate.custom((value, helpers) => {
  if (value.dayConfigurations) {
    try {
      validateDayConfigurations(value.dayConfigurations);
      return value;
    } catch (error) {
      return helpers.error('any.custom', { message: error.message });
    }
  }
  return value;
});

module.exports = {
  createTemplate: createTemplateEnhanced,
  updateTemplate: updateTemplateEnhanced,
  getAllTemplates,
  getTemplateById,
  deleteTemplate,
  cloneTemplate,
  setAsDefault,
  generateInstancesParams,
  generateInstancesBody,

  // Export schemas for reuse
  dayConfigSchema,
  dayConfigurationsSchema,
  assignmentRulesSchema,

  // Export validation functions
  validateDayConfiguration,
  validateDayConfigurations
};
