'use strict';

/**
 * Shift Assignment Controller - PRD Implementation
 * 
 * Handles employee assignment management according to PRD specifications:
 * - Employee-to-instance assignment workflow
 * - Assignment status tracking and transitions
 * - Bulk assignment operations
 * - Assignment conflict detection and resolution
 */

const shiftAssignmentService = require('../../services/rota/shiftAssignmentService');
const { successResponse } = require('../../common/utils/response');
const { ValidationError, NotFoundError } = require('../../common/errors');

/**
 * Get all shift assignments with filtering
 * @route GET /api/v1/shift-assignments
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAllAssignments = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      employeeId,
      scheduleId,
      instanceId,
      status,
      assignmentType,
      startDate,
      endDate,
      departmentId,
      search,
      sortBy = 'assignedAt',
      sortOrder = 'DESC'
    } = req.query;

    const filters = {
      page: parseInt(page),
      limit: parseInt(limit),
      employeeId: employeeId ? parseInt(employeeId) : null,
      scheduleId: scheduleId ? parseInt(scheduleId) : null,
      instanceId: instanceId ? parseInt(instanceId) : null,
      status,
      assignmentType,
      startDate,
      endDate,
      departmentId: departmentId ? parseInt(departmentId) : null,
      search,
      sortBy,
      sortOrder
    };

    const result = await shiftAssignmentService.getAllAssignments(filters, req.tenantContext);

    return successResponse(res, {
      message: 'Shift assignments retrieved successfully',
      data: result.assignments,
      pagination: result.pagination,
      filters: filters
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get assignment by ID with detailed information
 * @route GET /api/v1/shift-assignments/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAssignmentById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { 
      includeEmployee = true,
      includeInstance = true,
      includeHistory = false 
    } = req.query;

    const options = {
      includeEmployee: includeEmployee === 'true',
      includeInstance: includeInstance === 'true',
      includeHistory: includeHistory === 'true'
    };

    const assignment = await shiftAssignmentService.getAssignmentById(id, req.tenantContext, options);

    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Shift assignment not found'
      });
    }

    return successResponse(res, {
      message: 'Shift assignment retrieved successfully',
      data: assignment
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Assign employee to shift instance
 * @route POST /api/v1/shift-instances/:instanceId/assign
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const assignEmployeeToShift = async (req, res, next) => {
  try {
    const { instanceId } = req.params;
    const { employeeId, notes, assignmentType = 'manual_assigned' } = req.body;

    const assignmentData = {
      shiftInstanceId: parseInt(instanceId),
      employeeId: parseInt(employeeId),
      assignmentType,
      notes,
      assignedBy: req.tenantContext.userId,
      assignedAt: new Date()
    };

    const assignment = await shiftAssignmentService.createAssignment(assignmentData, req.tenantContext);

    return successResponse(res, {
      message: 'Employee assigned to shift successfully',
      data: assignment
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Update assignment
 * @route PUT /api/v1/shift-assignments/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateAssignment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      updatedById: req.tenantContext.userId
    };

    const assignment = await shiftAssignmentService.updateAssignment(id, updateData, req.tenantContext);

    return successResponse(res, {
      message: 'Assignment updated successfully',
      data: assignment
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Remove assignment
 * @route DELETE /api/v1/shift-assignments/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const removeAssignment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    await shiftAssignmentService.removeAssignment(id, {
      reason,
      removedBy: req.tenantContext.userId
    }, req.tenantContext);

    return successResponse(res, {
      message: 'Assignment removed successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Confirm assignment (employee confirmation)
 * @route POST /api/v1/shift-assignments/:id/confirm
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const confirmAssignment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;

    const confirmData = {
      confirmedBy: req.tenantContext.userId,
      confirmedAt: new Date(),
      notes
    };

    const assignment = await shiftAssignmentService.confirmAssignment(id, confirmData, req.tenantContext);

    return successResponse(res, {
      message: 'Assignment confirmed successfully',
      data: assignment
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Mark assignment as completed
 * @route POST /api/v1/shift-assignments/:id/complete
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const completeAssignment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { notes, actualHours } = req.body;

    const completionData = {
      completedBy: req.tenantContext.userId,
      completedAt: new Date(),
      notes,
      actualHours
    };

    const assignment = await shiftAssignmentService.completeAssignment(id, completionData, req.tenantContext);

    return successResponse(res, {
      message: 'Assignment marked as completed successfully',
      data: assignment
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Mark assignment as no-show
 * @route POST /api/v1/shift-assignments/:id/no-show
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const markNoShow = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { reason, notes } = req.body;

    const noShowData = {
      reason,
      notes,
      markedBy: req.tenantContext.userId,
      markedAt: new Date()
    };

    const assignment = await shiftAssignmentService.markNoShow(id, noShowData, req.tenantContext);

    return successResponse(res, {
      message: 'Assignment marked as no-show successfully',
      data: assignment
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk assign employees
 * @route POST /api/v1/shift-assignments/bulk-assign
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkAssignEmployees = async (req, res, next) => {
  try {
    const { assignments } = req.body;

    if (!assignments || !Array.isArray(assignments) || assignments.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Assignments array is required'
      });
    }

    // Add common fields to all assignments
    const enrichedAssignments = assignments.map(assignment => ({
      ...assignment,
      assignedBy: req.tenantContext.userId,
      assignedAt: new Date(),
      assignmentType: assignment.assignmentType || 'bulk_assigned'
    }));

    const result = await shiftAssignmentService.bulkCreateAssignments(enrichedAssignments, req.tenantContext);

    return successResponse(res, {
      message: `Bulk assignment completed: ${result.successful} successful, ${result.failed} failed`,
      data: result
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk update assignments
 * @route PUT /api/v1/shift-assignments/bulk-update
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkUpdateAssignments = async (req, res, next) => {
  try {
    const { updates } = req.body;

    if (!updates || !Array.isArray(updates) || updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Updates array is required'
      });
    }

    const result = await shiftAssignmentService.bulkUpdateAssignments(updates, req.tenantContext);

    return successResponse(res, {
      message: `Bulk assignment update completed: ${result.successful} successful, ${result.failed} failed`,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk confirm assignments
 * @route POST /api/v1/shift-assignments/bulk-confirm
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkConfirmAssignments = async (req, res, next) => {
  try {
    const { assignmentIds, notes } = req.body;

    if (!assignmentIds || !Array.isArray(assignmentIds) || assignmentIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Assignment IDs array is required'
      });
    }

    const confirmData = {
      confirmedBy: req.tenantContext.userId,
      confirmedAt: new Date(),
      notes
    };

    const result = await shiftAssignmentService.bulkConfirmAssignments(assignmentIds, confirmData, req.tenantContext);

    return successResponse(res, {
      message: `Bulk confirmation completed: ${result.successful} successful, ${result.failed} failed`,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Assign employees to schedule shifts during schedule creation
 * @route POST /api/v1/shift-assignments/schedule/:scheduleId/assign
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const assignEmployeesToSchedule = async (req, res, next) => {
  try {
    const { scheduleId } = req.params;
    const { assignments } = req.body;
    const tenantContext = req.tenantContext;

    const result = await shiftAssignmentService.assignEmployeesToSchedule(
      parseInt(scheduleId),
      assignments,
      tenantContext
    );

    return successResponse(res, result, 'Employees assigned to schedule successfully', 200);
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk assign employees during schedule creation
 * @route POST /api/v1/shift-assignments/bulk-schedule-assign
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkScheduleAssign = async (req, res, next) => {
  try {
    const { scheduleId, assignments } = req.body;
    const tenantContext = req.tenantContext;

    const result = await shiftAssignmentService.bulkScheduleAssign(
      scheduleId,
      assignments,
      tenantContext
    );

    return successResponse(res, result, 'Bulk schedule assignment completed', 200);
  } catch (error) {
    next(error);
  }
};

module.exports = {
  // Core CRUD Operations
  getAllAssignments,
  getAssignmentById,
  assignEmployeeToShift,
  updateAssignment,
  removeAssignment,

  // Assignment Status Management
  confirmAssignment,
  completeAssignment,
  markNoShow,

  // Bulk Operations
  bulkAssignEmployees,
  bulkUpdateAssignments,
  bulkConfirmAssignments,

  // Schedule-time Assignment
  assignEmployeesToSchedule,
  bulkScheduleAssign
};
