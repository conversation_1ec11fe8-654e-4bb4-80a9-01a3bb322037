'use strict';

const EmployeeAvailabilityService = require('../../../services/rota/employeeAvailabilityService');
const { ValidationError, NotFoundError } = require('../../../common/errors');

class EmployeeAvailabilityController {
  // ==================== BASIC CRUD METHODS ====================

  /**
   * Get employee availability
   * GET /api/v1/employee-availability/:employeeId
   */
  async getEmployeeAvailability(req, res, next) {
    try {
      const availability = await EmployeeAvailabilityService.getEmployeeAvailability(
        parseInt(req.params.employeeId),
        req.tenantContext
      );

      return res.status(200).json({
        success: true,
        message: 'Employee availability retrieved successfully',
        data: availability
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update employee availability
   * PUT /api/v1/employee-availability/:employeeId
   */
  async updateEmployeeAvailability(req, res, next) {
    try {
      const availability = await EmployeeAvailabilityService.updateEmployeeAvailability(
        parseInt(req.params.employeeId),
        req.body,
        req.tenantContext
      );

      return res.status(200).json({
        success: true,
        message: 'Employee availability updated successfully',
        data: availability
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get availability for date range
   * GET /api/v1/employee-availability/:employeeId/date-range
   */
  async getAvailabilityForDateRange(req, res, next) {
    try {
      const availability = await EmployeeAvailabilityService.getAvailabilityForDateRange(
        parseInt(req.params.employeeId),
        req.query.startDate,
        req.query.endDate,
        req.tenantContext
      );

      return res.status(200).json({
        success: true,
        message: 'Availability for date range retrieved successfully',
        data: availability
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Add availability override
   * POST /api/v1/employee-availability/:employeeId/overrides
   */
  async addAvailabilityOverride(req, res, next) {
    try {
      const availability = await EmployeeAvailabilityService.addAvailabilityOverride(
        parseInt(req.params.employeeId),
        req.body,
        req.tenantContext
      );

      return res.status(201).json({
        success: true,
        message: 'Availability override added successfully',
        data: availability
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Remove availability override
   * DELETE /api/v1/employee-availability/:employeeId/overrides/:date
   */
  async removeAvailabilityOverride(req, res, next) {
    try {
      const availability = await EmployeeAvailabilityService.removeAvailabilityOverride(
        parseInt(req.params.employeeId),
        req.params.date,
        req.tenantContext
      );

      return res.status(200).json({
        success: true,
        message: 'Availability override removed successfully',
        data: availability
      });
    } catch (error) {
      next(error);
    }
  }

  // ==================== ENHANCED METHODS ====================

  /**
   * Enhanced availability check with advanced validations
   * GET /api/v1/employee-availability/:employeeId/check
   */
  async checkAvailability(req, res, next) {
    try {
      const { employeeId } = req.params;
      const { date, startTime, endTime, includeAdvanced = 'true' } = req.query;

      if (!date || !startTime || !endTime) {
        throw new ValidationError('Date, startTime, and endTime are required');
      }

      const shiftDetails = {
        date,
        startTime,
        endTime
      };

      const includeAdvancedValidations = includeAdvanced === 'true';

      const availability = await EmployeeAvailabilityService.checkEmployeeAvailabilityForShift(
        parseInt(employeeId),
        shiftDetails,
        req.tenantContext,
        includeAdvancedValidations
      );

      const response = {
        success: true,
        data: {
          employeeId: parseInt(employeeId),
          shiftDetails,
          availability: {
            available: availability.available,
            reason: availability.reason,
            violations: availability.violations || [],
            details: availability.details || {}
          },
          advancedValidations: includeAdvancedValidations
        }
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update employee consents
   * PUT /api/v1/employees/:employeeId/availability/consents
   */
  async updateConsents(req, res, next) {
    try {
      const { employeeId } = req.params;
      const {
        nightShiftConsent,
        weekendWorkConsent,
        holidayWorkConsent,
        overtimeConsent,
        emergencyCallConsent
      } = req.body;

      // Validate consent values
      const consents = {
        nightShiftConsent,
        weekendWorkConsent,
        holidayWorkConsent,
        overtimeConsent,
        emergencyCallConsent
      };

      // Remove undefined values
      Object.keys(consents).forEach(key => {
        if (consents[key] === undefined) {
          delete consents[key];
        } else if (typeof consents[key] !== 'boolean') {
          throw new ValidationError(`${key} must be a boolean value`);
        }
      });

      if (Object.keys(consents).length === 0) {
        throw new ValidationError('At least one consent field must be provided');
      }

      const updatedAvailability = await EmployeeAvailabilityService.setEmployeePreferences(
        parseInt(employeeId),
        consents,
        req.tenantContext
      );

      const response = {
        success: true,
        message: 'Employee consents updated successfully',
        data: {
          employeeId: parseInt(employeeId),
          updatedConsents: consents,
          preferences: updatedAvailability.preferences
        }
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get employee availability summary
   * GET /api/v1/employees/:employeeId/availability/summary
   */
  async getAvailabilitySummary(req, res, next) {
    try {
      const { employeeId } = req.params;

      const availability = await EmployeeAvailabilityService.getEmployeeAvailability(
        parseInt(employeeId),
        req.tenantContext
      );

      if (!availability) {
        throw new NotFoundError('Employee availability not found');
      }

      // Get upcoming shifts for context
      const upcomingShifts = await this.getUpcomingShifts(parseInt(employeeId), req.tenantContext);

      // Check for current violations
      const currentViolations = await this.getCurrentViolations(parseInt(employeeId), req.tenantContext);

      const response = {
        success: true,
        data: {
          employeeId: parseInt(employeeId),
          regularAvailability: availability.regularAvailability,
          overrides: availability.overrides,
          preferences: availability.preferences,
          consents: {
            nightShiftConsent: availability.preferences?.nightShiftConsent || false,
            weekendWorkConsent: availability.preferences?.weekendWorkConsent || true,
            holidayWorkConsent: availability.preferences?.holidayWorkConsent || false,
            overtimeConsent: availability.preferences?.overtimeConsent || true,
            emergencyCallConsent: availability.preferences?.emergencyCallConsent || true
          },
          workLimits: {
            maxHoursPerWeek: availability.preferences?.maxHoursPerWeek || 48,
            maxConsecutiveDays: availability.preferences?.maxConsecutiveDays || 5,
            minRestHours: availability.preferences?.minRestHours || 12
          },
          currentViolations,
          upcomingShifts,
          lastUpdated: availability.updatedAt
        }
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update work limits (hours, consecutive days, rest hours)
   * PUT /api/v1/employees/:employeeId/availability/work-limits
   */
  async updateWorkLimits(req, res, next) {
    try {
      const { employeeId } = req.params;
      const {
        maxHoursPerWeek,
        maxConsecutiveDays,
        minRestHours,
        preferredWorkload
      } = req.body;

      // Validate work limits
      const workLimits = {};

      if (maxHoursPerWeek !== undefined) {
        if (typeof maxHoursPerWeek !== 'number' || maxHoursPerWeek < 1 || maxHoursPerWeek > 80) {
          throw new ValidationError('maxHoursPerWeek must be a number between 1 and 80');
        }
        workLimits.maxHoursPerWeek = maxHoursPerWeek;
      }

      if (maxConsecutiveDays !== undefined) {
        if (typeof maxConsecutiveDays !== 'number' || maxConsecutiveDays < 1 || maxConsecutiveDays > 14) {
          throw new ValidationError('maxConsecutiveDays must be a number between 1 and 14');
        }
        workLimits.maxConsecutiveDays = maxConsecutiveDays;
      }

      if (minRestHours !== undefined) {
        if (typeof minRestHours !== 'number' || minRestHours < 8 || minRestHours > 24) {
          throw new ValidationError('minRestHours must be a number between 8 and 24');
        }
        workLimits.minRestHours = minRestHours;
      }

      if (preferredWorkload !== undefined) {
        if (!['light', 'normal', 'heavy'].includes(preferredWorkload)) {
          throw new ValidationError('preferredWorkload must be one of: light, normal, heavy');
        }
        workLimits.preferredWorkload = preferredWorkload;
      }

      if (Object.keys(workLimits).length === 0) {
        throw new ValidationError('At least one work limit field must be provided');
      }

      const updatedAvailability = await EmployeeAvailabilityService.setEmployeePreferences(
        parseInt(employeeId),
        workLimits,
        req.tenantContext
      );

      const response = {
        success: true,
        message: 'Work limits updated successfully',
        data: {
          employeeId: parseInt(employeeId),
          updatedWorkLimits: workLimits,
          preferences: updatedAvailability.preferences
        }
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get upcoming shifts for an employee
   * @private
   */
  async getUpcomingShifts(employeeId, tenantContext) {
    try {
      // This would integrate with the RotaShift service
      // For now, return empty array as placeholder
      return [];
    } catch (error) {
      console.error('Error getting upcoming shifts:', error);
      return [];
    }
  }

  /**
   * Get current violations for an employee
   * @private
   */
  async getCurrentViolations(employeeId, tenantContext) {
    try {
      // This would check for current availability violations
      // For now, return empty array as placeholder
      return [];
    } catch (error) {
      console.error('Error getting current violations:', error);
      return [];
    }
  }
}

module.exports = new EmployeeAvailabilityController();
