'use strict';

/**
 * Rota Shift Instance Controller - PRD Implementation
 * 
 * Handles shift instance operations according to PRD specifications:
 * - Instance CRUD operations
 * - Requirement overrides
 * - Assignment management
 * - Instance analytics
 */

const rotaShiftInstanceService = require('../../services/rota/rotaShiftInstanceService');
const shiftAssignmentService = require('../../services/rota/shiftAssignmentService');
const { successResponse } = require('../../common/utils/response');
const { ValidationError, NotFoundError } = require('../../common/errors');

/**
 * Get all shift instances with filtering
 * @route GET /api/v1/rota-shift-instances
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAllInstances = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 20,
      scheduleId,
      departmentId,
      startDate,
      endDate,
      status,
      sortBy = 'date',
      sortOrder = 'ASC'
    } = req.query;

    const filters = {
      page: parseInt(page),
      limit: parseInt(limit),
      scheduleId: scheduleId ? parseInt(scheduleId) : null,
      departmentId: departmentId ? parseInt(departmentId) : null,
      startDate,
      endDate,
      status,
      sortBy,
      sortOrder
    };

    const result = await rotaShiftInstanceService.getAllInstances(filters, req.tenantContext);

    return successResponse(res, {
      message: 'Shift instances retrieved successfully',
      data: result.instances,
      pagination: result.pagination,
      filters: filters
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get shift instance by ID
 * @route GET /api/v1/rota-shift-instances/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getInstanceById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { includeAssignments = 'true' } = req.query;

    const instance = await rotaShiftInstanceService.getInstanceById(id, req.tenantContext, {
      includeAssignments: includeAssignments === 'true'
    });

    if (!instance) {
      return res.status(404).json({
        success: false,
        message: 'Shift instance not found'
      });
    }

    return successResponse(res, {
      message: 'Shift instance retrieved successfully',
      data: instance
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update shift instance
 * @route PUT /api/v1/rota-shift-instances/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateInstance = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      updatedById: req.tenantContext.userId
    };

    const instance = await rotaShiftInstanceService.updateInstance(id, updateData, req.tenantContext);

    return successResponse(res, {
      message: 'Shift instance updated successfully',
      data: instance
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Override instance requirement count
 * @route POST /api/v1/rota-shift-instances/:id/override-count
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const overrideRequirementCount = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { newRequiredCount, reason, notes } = req.body;

    const overrideData = {
      newRequiredCount,
      reason,
      notes,
      overriddenBy: req.tenantContext.userId,
      overriddenAt: new Date()
    };

    const instance = await rotaShiftInstanceService.overrideRequirementCount(id, overrideData, req.tenantContext);

    return successResponse(res, {
      message: 'Instance requirement count overridden successfully',
      data: instance
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get instance assignments
 * @route GET /api/v1/shift-instances/:id/assignments
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getInstanceAssignments = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, includeEmployee = true } = req.query;

    const filters = {
      shiftInstanceId: id,
      status,
      includeEmployee: includeEmployee === 'true'
    };

    const assignments = await shiftAssignmentService.getAssignmentsByInstance(filters, req.tenantContext);

    return successResponse(res, {
      message: 'Instance assignments retrieved successfully',
      data: assignments
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Assign employee to instance
 * @route POST /api/v1/shift-instances/:id/assign
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const assignEmployeeToInstance = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { employeeId, assignmentType = 'manual', notes } = req.body;

    const assignmentData = {
      shiftInstanceId: id,
      employeeId,
      assignmentType,
      notes,
      assignedBy: req.tenantContext.userId,
      assignedAt: new Date()
    };

    const assignment = await shiftAssignmentService.createAssignment(assignmentData, req.tenantContext);

    return successResponse(res, {
      message: 'Employee assigned to shift instance successfully',
      data: assignment
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Get available employees for instance
 * @route GET /api/v1/shift-instances/:id/available-employees
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAvailableEmployees = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { 
      departmentId,
      designationId,
      skillIds,
      limit = 20 
    } = req.query;

    const options = {
      departmentId: departmentId ? parseInt(departmentId) : null,
      designationId: designationId ? parseInt(designationId) : null,
      skillIds: skillIds ? skillIds.split(',').map(id => parseInt(id)) : [],
      limit: parseInt(limit)
    };

    const availableEmployees = await rotaShiftInstanceService.getAvailableEmployees(id, options, req.tenantContext);

    return successResponse(res, {
      message: 'Available employees retrieved successfully',
      data: availableEmployees
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get instance coverage status
 * @route GET /api/v1/rota-shift-instances/:id/coverage
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getInstanceCoverage = async (req, res, next) => {
  try {
    const { id } = req.params;

    const coverage = await rotaShiftInstanceService.getInstanceCoverage(id, req.tenantContext);

    return successResponse(res, {
      message: 'Instance coverage retrieved successfully',
      data: coverage
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  // Core Instance Operations
  getAllInstances,
  getInstanceById,
  updateInstance,
  overrideRequirementCount,
  
  // Assignment Operations
  getInstanceAssignments,
  assignEmployeeToInstance,
  getAvailableEmployees,
  
  // Instance Analytics
  getInstanceCoverage
};
