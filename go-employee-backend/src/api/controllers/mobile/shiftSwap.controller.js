'use strict';

/**
 * Mobile Shift Swap Controller - PRD Implementation
 * 
 * Handles mobile-specific shift swap operations according to PRD specifications:
 * - Mobile-optimized swap discovery
 * - Simplified swap request workflow
 * - Push notification integration
 * - Offline-friendly data structure
 */

const shiftSwapService = require('../../../services/rota/shiftSwapService');
const shiftAssignmentService = require('../../../services/rota/shiftAssignmentService');
const { successResponse } = require('../../../common/utils/response');
const { ValidationError, NotFoundError } = require('../../../common/errors');
const moment = require('moment');

/**
 * Get my swap requests
 * @route GET /api/v1/mobile/my-swap-requests
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getMySwapRequests = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      type = 'all' // 'sent', 'received', 'all'
    } = req.query;

    const employeeId = req.tenantContext.userId;

    let filters = {
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      sortBy: 'createdAt',
      sortOrder: 'DESC'
    };

    // Filter based on type
    if (type === 'sent') {
      filters.requesterId = employeeId;
    } else if (type === 'received') {
      filters.targetId = employeeId;
    } else {
      // For 'all', we need to get both sent and received
      // This would require a more complex query or two separate calls
      filters.requesterId = employeeId;
    }

    const result = await shiftSwapService.getAllSwapRequests(filters, req.tenantContext);

    // Transform to mobile format
    const mobileSwapRequests = await Promise.all(
      result.swapRequests.map(swap => transformToMobileSwapRequest(swap, employeeId))
    );

    return successResponse(res, {
      message: 'Swap requests retrieved successfully',
      data: {
        swapRequests: mobileSwapRequests,
        pagination: result.pagination,
        summary: generateSwapSummary(mobileSwapRequests, employeeId)
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create swap request
 * @route POST /api/v1/mobile/swap-requests
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createSwapRequest = async (req, res, next) => {
  try {
    const employeeId = req.tenantContext.userId;
    
    const swapData = {
      ...req.body,
      requesterId: employeeId,
      createdById: employeeId
    };

    const swapRequest = await shiftSwapService.createSwapRequest(swapData, req.tenantContext);
    const mobileSwapRequest = await transformToMobileSwapRequest(swapRequest, employeeId);

    return successResponse(res, {
      message: 'Swap request created successfully',
      data: mobileSwapRequest
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Get available shifts for swapping
 * @route GET /api/v1/mobile/available-swaps
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAvailableSwaps = async (req, res, next) => {
  try {
    const employeeId = req.tenantContext.userId;
    const {
      myShiftId, // Assignment ID of my shift to swap
      startDate = moment().format('YYYY-MM-DD'),
      endDate = moment().add(30, 'days').format('YYYY-MM-DD'),
      departmentId,
      limit = 20
    } = req.query;

    const options = {
      startDate,
      endDate,
      departmentId: departmentId ? parseInt(departmentId) : null,
      limit: parseInt(limit)
    };

    const availableSwaps = await shiftSwapService.getAvailableSwaps(employeeId, req.tenantContext, options);

    // Transform to mobile format
    const mobileAvailableSwaps = await Promise.all(
      availableSwaps.map(swap => transformToMobileAvailableSwap(swap, myShiftId))
    );

    return successResponse(res, {
      message: 'Available swaps retrieved successfully',
      data: {
        availableSwaps: mobileAvailableSwaps,
        myShiftId: myShiftId ? parseInt(myShiftId) : null,
        searchCriteria: options
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Respond to swap request (accept/decline)
 * @route POST /api/v1/mobile/swap-requests/:id/respond
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const respondToSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { action, notes } = req.body; // action: 'accept' or 'decline'
    const employeeId = req.tenantContext.userId;

    // Get swap request to verify target employee
    const swapRequest = await shiftSwapService.getSwapRequestById(id, req.tenantContext);
    
    if (!swapRequest.targetId || swapRequest.targetId !== employeeId) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to respond to this swap request'
      });
    }

    let updatedSwapRequest;

    if (action === 'accept') {
      // Update swap request to include target acceptance
      updatedSwapRequest = await shiftSwapService.updateSwapRequest(id, {
        targetAcceptedAt: new Date(),
        targetNotes: notes,
        status: 'pending' // Still needs manager approval
      }, req.tenantContext);
    } else if (action === 'decline') {
      updatedSwapRequest = await shiftSwapService.updateSwapRequest(id, {
        targetDeclinedAt: new Date(),
        targetNotes: notes,
        status: 'cancelled'
      }, req.tenantContext);
    } else {
      throw new ValidationError('Invalid action. Must be "accept" or "decline"');
    }

    const mobileSwapRequest = await transformToMobileSwapRequest(updatedSwapRequest, employeeId);

    return successResponse(res, {
      message: `Swap request ${action}ed successfully`,
      data: mobileSwapRequest
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Cancel my swap request
 * @route POST /api/v1/mobile/swap-requests/:id/cancel
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const cancelSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const employeeId = req.tenantContext.userId;

    // Verify ownership
    const swapRequest = await shiftSwapService.getSwapRequestById(id, req.tenantContext);
    if (swapRequest.requesterId !== employeeId) {
      return res.status(403).json({
        success: false,
        message: 'You can only cancel your own swap requests'
      });
    }

    const cancelData = {
      reason,
      cancelledBy: employeeId,
      cancelledAt: new Date()
    };

    const updatedSwapRequest = await shiftSwapService.cancelSwapRequest(id, cancelData, req.tenantContext);
    const mobileSwapRequest = await transformToMobileSwapRequest(updatedSwapRequest, employeeId);

    return successResponse(res, {
      message: 'Swap request cancelled successfully',
      data: mobileSwapRequest
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get swap request details
 * @route GET /api/v1/mobile/swap-requests/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getSwapRequestDetails = async (req, res, next) => {
  try {
    const { id } = req.params;
    const employeeId = req.tenantContext.userId;

    const swapRequest = await shiftSwapService.getSwapRequestById(id, req.tenantContext, {
      includeValidation: true
    });

    // Verify access (requester or target)
    if (swapRequest.requesterId !== employeeId && swapRequest.targetId !== employeeId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this swap request'
      });
    }

    const mobileSwapRequest = await transformToMobileSwapRequest(swapRequest, employeeId, {
      includeValidation: true,
      includeTimeline: true
    });

    return successResponse(res, {
      message: 'Swap request details retrieved successfully',
      data: mobileSwapRequest
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get my swappable shifts
 * @route GET /api/v1/mobile/my-swappable-shifts
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getMySwappableShifts = async (req, res, next) => {
  try {
    const employeeId = req.tenantContext.userId;
    const {
      startDate = moment().format('YYYY-MM-DD'),
      endDate = moment().add(30, 'days').format('YYYY-MM-DD'),
      limit = 20
    } = req.query;

    const filters = {
      employeeId,
      startDate,
      endDate,
      status: ['assigned', 'confirmed'], // Only swappable statuses
      includeEmployee: false,
      includeInstance: true,
      sortBy: 'date',
      sortOrder: 'ASC',
      limit: parseInt(limit)
    };

    const assignments = await shiftAssignmentService.getAllAssignments(filters, req.tenantContext);

    // Transform to mobile format with swap indicators
    const swappableShifts = await Promise.all(
      assignments.assignments.map(assignment => transformToSwappableShift(assignment))
    );

    return successResponse(res, {
      message: 'Swappable shifts retrieved successfully',
      data: {
        shifts: swappableShifts,
        count: swappableShifts.length,
        period: { startDate, endDate }
      }
    });
  } catch (error) {
    next(error);
  }
};

// Helper Functions

/**
 * Transform swap request to mobile format
 * @param {Object} swapRequest - Swap request object
 * @param {number} currentEmployeeId - Current employee ID
 * @param {Object} options - Transform options
 * @returns {Object} Mobile swap request format
 */
async function transformToMobileSwapRequest(swapRequest, currentEmployeeId, options = {}) {
  const { includeValidation = false, includeTimeline = false } = options;

  const isRequester = swapRequest.requesterId === currentEmployeeId;
  const isTarget = swapRequest.targetId === currentEmployeeId;

  const mobileSwapRequest = {
    id: swapRequest.id,
    type: isRequester ? 'sent' : 'received',
    status: swapRequest.status,
    urgency: swapRequest.urgency,
    reason: swapRequest.reason,
    notes: swapRequest.notes,
    createdAt: swapRequest.createdAt,
    
    // My shift (the one being offered)
    myShift: transformShiftForMobile(
      isRequester ? swapRequest.requesterAssignment : swapRequest.targetAssignment
    ),
    
    // Their shift (the one being requested)
    theirShift: transformShiftForMobile(
      isRequester ? swapRequest.targetAssignment : swapRequest.requesterAssignment
    ),
    
    // Other party details
    otherParty: {
      id: isRequester ? swapRequest.target?.id : swapRequest.requester?.id,
      name: isRequester ? 
        `${swapRequest.target?.firstName} ${swapRequest.target?.lastName}` :
        `${swapRequest.requester?.firstName} ${swapRequest.requester?.lastName}`,
      employeeId: isRequester ? swapRequest.target?.employeeId : swapRequest.requester?.employeeId
    },
    
    // Available actions
    actions: getAvailableSwapActions(swapRequest, currentEmployeeId)
  };

  if (includeValidation && swapRequest.validation) {
    mobileSwapRequest.validation = {
      isValid: swapRequest.validation.isValid,
      conflicts: swapRequest.validation.conflicts,
      warnings: swapRequest.validation.warnings
    };
  }

  if (includeTimeline) {
    mobileSwapRequest.timeline = generateSwapTimeline(swapRequest);
  }

  return mobileSwapRequest;
}

/**
 * Transform available swap to mobile format
 * @param {Object} swap - Available swap object
 * @param {number} myShiftId - My shift ID for context
 * @returns {Object} Mobile available swap format
 */
async function transformToMobileAvailableSwap(swap, myShiftId) {
  return {
    myShift: {
      id: swap.assignment.id,
      date: swap.assignment.shiftInstance.date,
      shift: transformShiftForMobile(swap.assignment)
    },
    potentialPartners: swap.potentialPartners.map(partner => ({
      id: partner.id,
      date: partner.shiftInstance.date,
      shift: transformShiftForMobile(partner),
      employee: {
        id: partner.employee.id,
        name: `${partner.employee.firstName} ${partner.employee.lastName}`,
        employeeId: partner.employee.employeeId
      },
      compatibility: calculateCompatibility(swap.assignment, partner)
    }))
  };
}

/**
 * Transform assignment to swappable shift format
 * @param {Object} assignment - Assignment object
 * @returns {Object} Swappable shift format
 */
async function transformToSwappableShift(assignment) {
  const shift = assignment.shiftInstance.rotaShift;
  
  return {
    id: assignment.id,
    date: assignment.shiftInstance.date,
    dayOfWeek: moment(assignment.shiftInstance.date).format('dddd'),
    shift: {
      name: shift.name,
      startTime: shift.startTime,
      endTime: shift.endTime,
      department: shift.department?.name,
      designation: shift.designation?.name
    },
    status: assignment.status,
    canSwap: ['assigned', 'confirmed'].includes(assignment.status),
    swapDeadline: calculateSwapDeadline(assignment.shiftInstance.date),
    existingSwapRequests: 0 // This would be calculated from actual swap requests
  };
}

/**
 * Transform shift for mobile display
 * @param {Object} assignment - Assignment object
 * @returns {Object} Mobile shift format
 */
function transformShiftForMobile(assignment) {
  if (!assignment) return null;
  
  const shift = assignment.shiftInstance.rotaShift;
  
  return {
    id: assignment.id,
    date: assignment.shiftInstance.date,
    dayOfWeek: moment(assignment.shiftInstance.date).format('dddd'),
    name: shift.name,
    startTime: shift.startTime,
    endTime: shift.endTime,
    department: shift.department?.name,
    designation: shift.designation?.name,
    duration: calculateShiftDuration(shift.startTime, shift.endTime)
  };
}

/**
 * Generate swap summary
 * @param {Array} swapRequests - Mobile swap requests
 * @param {number} employeeId - Employee ID
 * @returns {Object} Swap summary
 */
function generateSwapSummary(swapRequests, employeeId) {
  const summary = {
    total: swapRequests.length,
    sent: 0,
    received: 0,
    pending: 0,
    approved: 0,
    executed: 0
  };

  swapRequests.forEach(swap => {
    if (swap.type === 'sent') summary.sent++;
    if (swap.type === 'received') summary.received++;
    
    if (swap.status === 'pending') summary.pending++;
    if (swap.status === 'approved') summary.approved++;
    if (swap.status === 'executed') summary.executed++;
  });

  return summary;
}

/**
 * Get available actions for swap request
 * @param {Object} swapRequest - Swap request object
 * @param {number} currentEmployeeId - Current employee ID
 * @returns {Array} Available actions
 */
function getAvailableSwapActions(swapRequest, currentEmployeeId) {
  const actions = [];
  const isRequester = swapRequest.requesterId === currentEmployeeId;
  const isTarget = swapRequest.targetId === currentEmployeeId;

  if (isRequester && swapRequest.status === 'pending') {
    actions.push('cancel', 'edit');
  }

  if (isTarget && swapRequest.status === 'pending' && !swapRequest.targetAcceptedAt) {
    actions.push('accept', 'decline');
  }

  return actions;
}

/**
 * Generate swap timeline
 * @param {Object} swapRequest - Swap request object
 * @returns {Array} Timeline events
 */
function generateSwapTimeline(swapRequest) {
  const timeline = [
    {
      event: 'created',
      timestamp: swapRequest.createdAt,
      description: 'Swap request created'
    }
  ];

  if (swapRequest.targetAcceptedAt) {
    timeline.push({
      event: 'accepted',
      timestamp: swapRequest.targetAcceptedAt,
      description: 'Target employee accepted'
    });
  }

  if (swapRequest.approvedAt) {
    timeline.push({
      event: 'approved',
      timestamp: swapRequest.approvedAt,
      description: 'Manager approved'
    });
  }

  if (swapRequest.executedAt) {
    timeline.push({
      event: 'executed',
      timestamp: swapRequest.executedAt,
      description: 'Swap executed'
    });
  }

  return timeline;
}

/**
 * Calculate compatibility between two shifts
 * @param {Object} shift1 - First shift
 * @param {Object} shift2 - Second shift
 * @returns {Object} Compatibility score and factors
 */
function calculateCompatibility(shift1, shift2) {
  let score = 100;
  const factors = [];

  const s1 = shift1.shiftInstance.rotaShift;
  const s2 = shift2.shiftInstance.rotaShift;

  if (s1.departmentId !== s2.departmentId) {
    score -= 20;
    factors.push('Different departments');
  }

  if (s1.designationId !== s2.designationId) {
    score -= 30;
    factors.push('Different designations');
  }

  const timeDiff = Math.abs(
    moment(s1.startTime, 'HH:mm').diff(moment(s2.startTime, 'HH:mm'), 'hours')
  );
  
  if (timeDiff > 2) {
    score -= 10;
    factors.push('Different shift times');
  }

  return {
    score: Math.max(0, score),
    factors,
    level: score >= 80 ? 'high' : score >= 60 ? 'medium' : 'low'
  };
}

/**
 * Calculate shift duration
 * @param {string} startTime - Start time
 * @param {string} endTime - End time
 * @returns {number} Duration in hours
 */
function calculateShiftDuration(startTime, endTime) {
  const start = moment(startTime, 'HH:mm');
  const end = moment(endTime, 'HH:mm');
  
  if (end.isBefore(start)) {
    end.add(1, 'day');
  }
  
  return end.diff(start, 'hours', true);
}

/**
 * Calculate swap deadline
 * @param {string} shiftDate - Shift date
 * @returns {string} Swap deadline
 */
function calculateSwapDeadline(shiftDate) {
  // Typically 24-48 hours before shift
  return moment(shiftDate).subtract(24, 'hours').format('YYYY-MM-DD HH:mm');
}

module.exports = {
  getMySwapRequests,
  createSwapRequest,
  getAvailableSwaps,
  respondToSwapRequest,
  cancelSwapRequest,
  getSwapRequestDetails,
  getMySwappableShifts
};
