'use strict';

const { mobileSuccessResponse } = require('../../../common/utils/response');
const { transformBankDetailsToMobile, transformBankDetailToMobile } = require('../../../common/utils/mobileResponseTransformer');
const bankDetailService = require('../../../services/employee/bankDetailService');
const { NotFoundError } = require('../../../common/errors');

/**
 * Get employee bank details for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getBankDetails = async (req, res, next) => {
  try {
    // Extract employee ID from tenant context (mobile tokens are employee-specific)
    const employeeId = req.tenantContext.employeeId;

    // Get bank details
    const bankDetails = await bankDetailService.getBankDetails(employeeId, req.tenantContext);

    // Transform for mobile response

    return mobileSuccessResponse(res, {
      message: 'Bank details retrieved successfully',
      result: bankDetails
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Add bank details for mobile (upsert functionality)
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const addBankDetails = async (req, res, next) => {
  try {
    // Extract employee ID from tenant context (mobile tokens are employee-specific)
    const employeeId = req.tenantContext.employeeId;

    // Add or update bank details (upsert)
    const bankDetail = await bankDetailService.addBankDetails(
      employeeId,
      req.body,
      req.tenantContext
    );

    // Transform for mobile response
    const transformedData = transformBankDetailToMobile(bankDetail);

    return mobileSuccessResponse(res, {
      message: 'Bank details saved successfully',
      data: transformedData
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Update bank details for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateBankDetails = async (req, res, next) => {
  try {
    // Extract employee ID from tenant context (mobile tokens are employee-specific)
    const employeeId = req.tenantContext.employeeId;
    const { bankAccountId } = req.params;

    // Update bank details

    const bankDetail = await bankDetailService.getBankDetails(employeeId,req.tenantContext)
    console.log(bankDetail,'-----------------')
    const bankAccount = await bankDetailService.updateBankDetails(
      employeeId,
      bankDetail.id,
      req.body,
      req.tenantContext
    );

    if (!bankAccount) {
      throw new NotFoundError('Bank account not found');
    }

    // Transform for mobile response
    const transformedData = transformBankDetailToMobile(bankAccount);

    return mobileSuccessResponse(res, {
      message: 'Bank details updated successfully',
      data: transformedData
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete bank details for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteBankDetails = async (req, res, next) => {
  try {
    // Extract employee ID from tenant context (mobile tokens are employee-specific)
    const employeeId = req.tenantContext.employeeId;
    const { bankAccountId } = req.params;

    // Delete bank details
    const bankDetail = await bankDetailService.getBankDetails(employeeId,req.tenantContext)
    const result = await bankDetailService.deleteEmployeeBankDetails(
      employeeId,
      bankDetail.id,
      req.tenantContext
    );

    if (!result) {
      throw new NotFoundError('Bank account not found');
    }

    return mobileSuccessResponse(res, {
      message: 'Bank details deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getBankDetails,
  addBankDetails,
  updateBankDetails,
  deleteBankDetails
};
