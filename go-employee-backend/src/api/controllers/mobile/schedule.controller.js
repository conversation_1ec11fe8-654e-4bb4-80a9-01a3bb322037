'use strict';

/**
 * Mobile Schedule Controller - PRD Implementation
 * 
 * Handles mobile-specific schedule operations according to PRD specifications:
 * - Employee schedule viewing (my shifts)
 * - Mobile-optimized responses
 * - Offline-friendly data structure
 * - Push notification integration
 */

const rotaScheduleService = require('../../../services/rota/rotaScheduleService');
const rotaShiftInstanceService = require('../../../services/rota/rotaShiftInstanceService');
const shiftAssignmentService = require('../../../services/rota/shiftAssignmentService');
const { successResponse } = require('../../../common/utils/response');
const { ValidationError, NotFoundError } = require('../../../common/errors');
const moment = require('moment');

/**
 * Get employee's schedule (my shifts)
 * @route GET /api/v1/mobile/my-schedule
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getMySchedule = async (req, res, next) => {
  try {
    const {
      startDate = moment().format('YYYY-MM-DD'),
      endDate = moment().add(30, 'days').format('YYYY-MM-DD'),
      status,
      includeSwapRequests = true
    } = req.query;

    const employeeId = req.tenantContext.userId; // Mobile token is employee-specific

    const filters = {
      employeeId,
      startDate,
      endDate,
      status,
      includeEmployee: false, // Not needed for mobile
      includeInstance: true,
      sortBy: 'date',
      sortOrder: 'ASC'
    };

    const assignments = await shiftAssignmentService.getAllAssignments(filters, req.tenantContext);

    // Transform to mobile-friendly format
    const mobileSchedule = await transformToMobileSchedule(assignments.assignments, {
      includeSwapRequests: includeSwapRequests === 'true',
      employeeId,
      tenantContext: req.tenantContext
    });

    return successResponse(res, {
      message: 'Schedule retrieved successfully',
      data: {
        schedule: mobileSchedule,
        summary: generateScheduleSummary(mobileSchedule),
        pagination: assignments.pagination
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get specific shift details
 * @route GET /api/v1/mobile/shifts/:assignmentId
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getShiftDetails = async (req, res, next) => {
  try {
    const { assignmentId } = req.params;
    const employeeId = req.tenantContext.userId;

    const assignment = await shiftAssignmentService.getAssignmentById(assignmentId, req.tenantContext, {
      includeEmployee: false,
      includeInstance: true
    });

    // Verify assignment belongs to current employee
    if (assignment.employeeId !== employeeId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this shift'
      });
    }

    const mobileShift = await transformToMobileShift(assignment, req.tenantContext);

    return successResponse(res, {
      message: 'Shift details retrieved successfully',
      data: mobileShift
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Confirm shift assignment
 * @route POST /api/v1/mobile/shifts/:assignmentId/confirm
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const confirmShift = async (req, res, next) => {
  try {
    const { assignmentId } = req.params;
    const { notes } = req.body;
    const employeeId = req.tenantContext.userId;

    // Verify assignment belongs to current employee
    const assignment = await shiftAssignmentService.getAssignmentById(assignmentId, req.tenantContext);
    if (assignment.employeeId !== employeeId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this shift'
      });
    }

    const confirmData = {
      confirmedBy: employeeId,
      confirmedAt: new Date(),
      notes
    };

    const updatedAssignment = await shiftAssignmentService.confirmAssignment(
      assignmentId, 
      confirmData, 
      req.tenantContext
    );

    const mobileShift = await transformToMobileShift(updatedAssignment, req.tenantContext);

    return successResponse(res, {
      message: 'Shift confirmed successfully',
      data: mobileShift
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get upcoming shifts (next 7 days)
 * @route GET /api/v1/mobile/upcoming-shifts
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getUpcomingShifts = async (req, res, next) => {
  try {
    const employeeId = req.tenantContext.userId;
    const { limit = 10 } = req.query;

    const startDate = moment().format('YYYY-MM-DD');
    const endDate = moment().add(7, 'days').format('YYYY-MM-DD');

    const filters = {
      employeeId,
      startDate,
      endDate,
      status: ['assigned', 'confirmed'],
      includeEmployee: false,
      includeInstance: true,
      sortBy: 'date',
      sortOrder: 'ASC',
      limit: parseInt(limit)
    };

    const assignments = await shiftAssignmentService.getAllAssignments(filters, req.tenantContext);

    const upcomingShifts = await Promise.all(
      assignments.assignments.map(assignment => 
        transformToMobileShift(assignment, req.tenantContext, { compact: true })
      )
    );

    return successResponse(res, {
      message: 'Upcoming shifts retrieved successfully',
      data: {
        shifts: upcomingShifts,
        count: upcomingShifts.length
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get shift calendar view
 * @route GET /api/v1/mobile/calendar
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getCalendarView = async (req, res, next) => {
  try {
    const {
      month = moment().format('YYYY-MM'),
      view = 'month'
    } = req.query;

    const employeeId = req.tenantContext.userId;

    let startDate, endDate;
    if (view === 'month') {
      startDate = moment(month).startOf('month').format('YYYY-MM-DD');
      endDate = moment(month).endOf('month').format('YYYY-MM-DD');
    } else if (view === 'week') {
      startDate = moment(month).startOf('week').format('YYYY-MM-DD');
      endDate = moment(month).endOf('week').format('YYYY-MM-DD');
    }

    const filters = {
      employeeId,
      startDate,
      endDate,
      includeEmployee: false,
      includeInstance: true,
      sortBy: 'date',
      sortOrder: 'ASC'
    };

    const assignments = await shiftAssignmentService.getAllAssignments(filters, req.tenantContext);

    const calendarData = await transformToCalendarView(assignments.assignments, view);

    return successResponse(res, {
      message: 'Calendar view retrieved successfully',
      data: {
        calendar: calendarData,
        period: { startDate, endDate, view },
        summary: generateCalendarSummary(calendarData)
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get shift notifications
 * @route GET /api/v1/mobile/notifications
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getShiftNotifications = async (req, res, next) => {
  try {
    const employeeId = req.tenantContext.userId;
    const { 
      page = 1, 
      limit = 20,
      type = 'all',
      unreadOnly = false 
    } = req.query;

    // This would integrate with notification service
    // For now, return mock structure
    const notifications = {
      notifications: [],
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      unreadCount: 0
    };

    return successResponse(res, {
      message: 'Notifications retrieved successfully',
      data: notifications
    });
  } catch (error) {
    next(error);
  }
};

// Helper Functions

/**
 * Transform assignments to mobile-friendly schedule format
 * @param {Array} assignments - Assignment objects
 * @param {Object} options - Transform options
 * @returns {Array} Mobile schedule format
 */
async function transformToMobileSchedule(assignments, options = {}) {
  const { includeSwapRequests = false } = options;

  const schedule = [];

  for (const assignment of assignments) {
    const mobileShift = await transformToMobileShift(assignment, options.tenantContext, {
      includeSwapRequests
    });
    schedule.push(mobileShift);
  }

  return schedule;
}

/**
 * Transform single assignment to mobile shift format
 * @param {Object} assignment - Assignment object
 * @param {Object} tenantContext - Tenant context
 * @param {Object} options - Transform options
 * @returns {Object} Mobile shift format
 */
async function transformToMobileShift(assignment, tenantContext, options = {}) {
  const { compact = false, includeSwapRequests = false } = options;
  const shift = assignment.shiftInstance.rotaShift;

  const mobileShift = {
    id: assignment.id,
    date: assignment.shiftInstance.date,
    dayOfWeek: moment(assignment.shiftInstance.date).format('dddd'),
    shift: {
      id: shift.id,
      name: shift.name,
      startTime: shift.startTime,
      endTime: shift.endTime,
      duration: calculateShiftDuration(shift.startTime, shift.endTime),
      department: shift.department?.name || 'Unknown',
      designation: shift.designation?.name || 'Unknown'
    },
    status: assignment.status,
    assignmentType: assignment.assignmentType,
    notes: assignment.notes,
    confirmedAt: assignment.confirmedAt,
    completedAt: assignment.completedAt,
    actualHours: assignment.actualHours
  };

  if (!compact) {
    mobileShift.actions = getAvailableActions(assignment);
    mobileShift.timeline = getShiftTimeline(assignment);
  }

  if (includeSwapRequests) {
    // This would fetch related swap requests
    mobileShift.swapRequests = [];
  }

  return mobileShift;
}

/**
 * Transform assignments to calendar view
 * @param {Array} assignments - Assignment objects
 * @param {string} view - Calendar view type
 * @returns {Object} Calendar data
 */
async function transformToCalendarView(assignments, view) {
  const calendar = {};

  assignments.forEach(assignment => {
    const date = assignment.shiftInstance.date;
    
    if (!calendar[date]) {
      calendar[date] = {
        date,
        dayOfWeek: moment(date).format('dddd'),
        shifts: [],
        totalHours: 0,
        status: 'free'
      };
    }

    const shift = assignment.shiftInstance.rotaShift;
    const duration = calculateShiftDuration(shift.startTime, shift.endTime);

    calendar[date].shifts.push({
      id: assignment.id,
      name: shift.name,
      startTime: shift.startTime,
      endTime: shift.endTime,
      status: assignment.status,
      department: shift.department?.name
    });

    calendar[date].totalHours += duration;
    calendar[date].status = assignment.status === 'confirmed' ? 'confirmed' : 'assigned';
  });

  return calendar;
}

/**
 * Generate schedule summary
 * @param {Array} schedule - Mobile schedule
 * @returns {Object} Summary statistics
 */
function generateScheduleSummary(schedule) {
  const summary = {
    totalShifts: schedule.length,
    confirmedShifts: 0,
    pendingShifts: 0,
    totalHours: 0,
    upcomingShifts: 0
  };

  const today = moment().format('YYYY-MM-DD');

  schedule.forEach(shift => {
    if (shift.status === 'confirmed') summary.confirmedShifts++;
    if (shift.status === 'assigned') summary.pendingShifts++;
    if (moment(shift.date).isAfter(today)) summary.upcomingShifts++;
    
    summary.totalHours += shift.shift.duration;
  });

  return summary;
}

/**
 * Generate calendar summary
 * @param {Object} calendar - Calendar data
 * @returns {Object} Calendar summary
 */
function generateCalendarSummary(calendar) {
  const dates = Object.keys(calendar);
  
  return {
    totalDays: dates.length,
    workingDays: dates.filter(date => calendar[date].shifts.length > 0).length,
    totalHours: dates.reduce((sum, date) => sum + calendar[date].totalHours, 0),
    averageHoursPerDay: dates.length > 0 ? 
      dates.reduce((sum, date) => sum + calendar[date].totalHours, 0) / dates.length : 0
  };
}

/**
 * Calculate shift duration in hours
 * @param {string} startTime - Start time
 * @param {string} endTime - End time
 * @returns {number} Duration in hours
 */
function calculateShiftDuration(startTime, endTime) {
  const start = moment(startTime, 'HH:mm');
  const end = moment(endTime, 'HH:mm');
  
  if (end.isBefore(start)) {
    end.add(1, 'day'); // Handle overnight shifts
  }
  
  return end.diff(start, 'hours', true);
}

/**
 * Get available actions for assignment
 * @param {Object} assignment - Assignment object
 * @returns {Array} Available actions
 */
function getAvailableActions(assignment) {
  const actions = [];
  
  if (assignment.status === 'assigned') {
    actions.push('confirm', 'request_swap');
  }
  
  if (assignment.status === 'confirmed') {
    actions.push('request_swap');
  }
  
  return actions;
}

/**
 * Get shift timeline
 * @param {Object} assignment - Assignment object
 * @returns {Array} Timeline events
 */
function getShiftTimeline(assignment) {
  const timeline = [
    {
      event: 'assigned',
      timestamp: assignment.assignedAt,
      description: 'Shift assigned'
    }
  ];
  
  if (assignment.confirmedAt) {
    timeline.push({
      event: 'confirmed',
      timestamp: assignment.confirmedAt,
      description: 'Shift confirmed'
    });
  }
  
  if (assignment.completedAt) {
    timeline.push({
      event: 'completed',
      timestamp: assignment.completedAt,
      description: 'Shift completed'
    });
  }
  
  return timeline;
}

module.exports = {
  getMySchedule,
  getShiftDetails,
  confirmShift,
  getUpcomingShifts,
  getCalendarView,
  getShiftNotifications
};
