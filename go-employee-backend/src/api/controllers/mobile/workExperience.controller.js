'use strict';

const { mobileSuccessResponse } = require('../../../common/utils/mobileResponseTransformer');
const logger = require('../../../common/logging');
const workExperienceService = require('../../../services/employee/workExperienceService');
const { NotFoundError, ValidationError } = require('../../../common/errors');

/**
 * Get current user's work experience records for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getWorkExperience = async (req, res, next) => {
  try {
    // Use the current user's employee ID from tenant context
    const employeeId = req.tenantContext.employeeId;

    if (!employeeId) {
      throw new ValidationError('Employee ID not found in user context');
    }

    // Get work experience records
    const workExperience = await workExperienceService.getWorkExperiences(employeeId, req.tenantContext);

  
    return mobileSuccessResponse(res, {
      message: 'Work experience records retrieved successfully',
      result: workExperience
    });
  } catch (error) {
    logger.error('Mobile get work experience error:', error);
    next(error);
  }
};

/**
 * Get single work experience record by ID for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getWorkExperienceById = async (req, res, next) => {
  try {
    const { experienceId } = req.params;
    const employeeId = req.tenantContext.employeeId;

    if (!employeeId) {
      throw new ValidationError('Employee ID not found in user context');
    }

    // Get single work experience record
    const workExperience = await workExperienceService.getWorkExperiences(employeeId, req.tenantContext);
    const experienceRecord = workExperience.find(exp => exp.id === parseInt(experienceId));

    if (!experienceRecord) {
      throw new NotFoundError('Work experience record not found');
    }

    // Transform data for mobile response
    const mobileWorkExperience = {
      id: experienceRecord.id,
      companyName: experienceRecord.companyName,
      jobTitle: experienceRecord.jobTitle,
      department: experienceRecord.department,
      location: experienceRecord.location,
      startDate: experienceRecord.startDate,
      endDate: experienceRecord.endDate,
      currentlyWorking: experienceRecord.currentlyWorking,
      jobDescription: experienceRecord.jobDescription,
      skills: Array.isArray(experienceRecord.skills) ? experienceRecord.skills : (experienceRecord.skills ? [experienceRecord.skills] : []),
      achievements: experienceRecord.achievements,
      reasonForLeaving: experienceRecord.reasonForLeaving,
      salary: experienceRecord.salary,
      currency: experienceRecord.currency,
      employmentType: experienceRecord.employmentType,
      isVerified: experienceRecord.isVerified,
      verifiedAt: experienceRecord.verifiedAt,
      document: experienceRecord.document ? {
        id: experienceRecord.document.id,
        title: experienceRecord.document.title,
        fileUrl: experienceRecord.document.fileUrl,
        documentType: experienceRecord.documentType
      } : null,
      verifier: experienceRecord.verifier ? {
        id: experienceRecord.verifier.id,
        name: `${experienceRecord.verifier.firstName} ${experienceRecord.verifier.lastName}`,
        email: experienceRecord.verifier.email
      } : null,
      createdAt: experienceRecord.createdAt,
      updatedAt: experienceRecord.updatedAt
    };

    return mobileSuccessResponse(res, {
      message: 'Work experience record retrieved successfully',
      result: mobileWorkExperience
    });
  } catch (error) {
    logger.error('Mobile get work experience by ID error:', error);
    next(error);
  }
};

/**
 * Add work experience record for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const addWorkExperience = async (req, res, next) => {
  try {
    // Use the current user's employee ID from tenant context
    const employeeId = req.tenantContext.employeeId;

    if (!employeeId) {
      throw new ValidationError('Employee ID not found in user context');
    }

    // Add work experience record
    const experienceRecord = await workExperienceService.addWorkExperience(
      employeeId,
      req.body,
      req.tenantContext
    );

    // Transform data for mobile response
    const mobileWorkExperience = {
      id: experienceRecord.id,
      companyName: experienceRecord.companyName,
      jobTitle: experienceRecord.jobTitle,
      department: experienceRecord.department,
      location: experienceRecord.location,
      startDate: experienceRecord.startDate,
      endDate: experienceRecord.endDate,
      currentlyWorking: experienceRecord.currentlyWorking,
      jobDescription: experienceRecord.jobDescription,
      skills: Array.isArray(experienceRecord.skills) ? experienceRecord.skills : (experienceRecord.skills ? [experienceRecord.skills] : []),
      achievements: experienceRecord.achievements,
      reasonForLeaving: experienceRecord.reasonForLeaving,
      salary: experienceRecord.salary,
      currency: experienceRecord.currency,
      employmentType: experienceRecord.employmentType,
      isVerified: experienceRecord.isVerified,
      verifiedAt: experienceRecord.verifiedAt,
      createdAt: experienceRecord.createdAt,
      updatedAt: experienceRecord.updatedAt
    };

    return mobileSuccessResponse(res, {
      message: 'Work experience record added successfully',
      result: mobileWorkExperience
    });
  } catch (error) {
    logger.error('Mobile add work experience error:', error);
    next(error);
  }
};

/**
 * Update work experience record for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateWorkExperience = async (req, res, next) => {
  try {
    const { experienceId } = req.params;
    const employeeId = req.tenantContext.employeeId;

    if (!employeeId) {
      throw new ValidationError('Employee ID not found in user context');
    }

    // Update work experience record
    const experienceRecord = await workExperienceService.updateWorkExperience(
      employeeId,
      experienceId,
      req.body,
      req.tenantContext
    );

    // Transform data for mobile response
    const mobileWorkExperience = {
      id: experienceRecord.id,
      companyName: experienceRecord.companyName,
      jobTitle: experienceRecord.jobTitle,
      department: experienceRecord.department,
      location: experienceRecord.location,
      startDate: experienceRecord.startDate,
      endDate: experienceRecord.endDate,
      currentlyWorking: experienceRecord.currentlyWorking,
      jobDescription: experienceRecord.jobDescription,
      skills: Array.isArray(experienceRecord.skills) ? experienceRecord.skills : (experienceRecord.skills ? [experienceRecord.skills] : []),
      achievements: experienceRecord.achievements,
      reasonForLeaving: experienceRecord.reasonForLeaving,
      salary: experienceRecord.salary,
      currency: experienceRecord.currency,
      employmentType: experienceRecord.employmentType,
      isVerified: experienceRecord.isVerified,
      verifiedAt: experienceRecord.verifiedAt,
      createdAt: experienceRecord.createdAt,
      updatedAt: experienceRecord.updatedAt
    };

    return mobileSuccessResponse(res, {
      message: 'Work experience record updated successfully',
      result: mobileWorkExperience
    });
  } catch (error) {
    logger.error('Mobile update work experience error:', error);
    next(error);
  }
};

/**
 * Delete work experience record for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteWorkExperience = async (req, res, next) => {
  try {
    const { experienceId } = req.params;
    const employeeId = req.tenantContext.employeeId;

    if (!employeeId) {
      throw new ValidationError('Employee ID not found in user context');
    }

    // Delete work experience record
    await workExperienceService.deleteWorkExperience(
      employeeId,
      experienceId,
      req.tenantContext
    );

    return mobileSuccessResponse(res, {
      message: 'Work experience record deleted successfully',
      result: null
    });
  } catch (error) {
    logger.error('Mobile delete work experience error:', error);
    next(error);
  }
};

module.exports = {
  getWorkExperience,
  getWorkExperienceById,
  addWorkExperience,
  updateWorkExperience,
  deleteWorkExperience
};
