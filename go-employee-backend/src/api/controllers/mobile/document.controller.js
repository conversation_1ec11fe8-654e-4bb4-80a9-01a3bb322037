'use strict';

const { mobileSuccessResponse, transformErrorResponse } = require('../../../common/utils/mobileResponseTransformer');
const documentService = require('../../../services/employee/documentService');
const { NotFoundError, ValidationError, ForbiddenError } = require('../../../common/errors');
const logger = require('../../../common/logging');
const { Employee, User } = require('../../../data/models');

/**
 * Helper function to get employee by user ID
 * @param {number} userId - User ID
 * @param {Object} tenantContext - Tenant context
 * @returns {Promise<Object>} Employee record
 */
const getEmployeeByUserId = async (userId, tenantContext) => {
  const employee = await Employee.findOne({
    where: {
      userId,
      companyId: tenantContext.companyId
    },
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'email', 'status']
      }
    ]
  });

  if (!employee) {
    throw new NotFoundError('Employee not found for this user');
  }

  return employee;
};

/**
 * Transform document data to mobile format
 * @param {Object} document - Document object
 * @returns {Object} Mobile formatted document
 */
const transformDocumentToMobile = (document) => {
  if (!document) return null;

  return {
    id: document.id,
    document_type: document.documentType,
    document_type_id: document.documentTypeId,
    title: document.title,
    description: document.description || '',
    file_url: document.fileUrl,
    file_size: document.fileSize,
    mime_type: document.mimeType,
    is_verified: document.isVerified || false,
    verified_by: document.verifiedBy,
    verified_at: document.verifiedAt,
    expiry_date: document.expiryDate,
    is_public: document.isPublic || false,
    tags: document.tags || [],
    created_at: document.createdAt,
    updated_at: document.updatedAt,
    document_type_details: document.documentTypeDetails ? {
      id: document.documentTypeDetails.id,
      name: document.documentTypeDetails.name,
      code: document.documentTypeDetails.code,
      category: document.documentTypeDetails.category
    } : null,
    verifier: document.verifier ? {
      id: document.verifier.id,
      first_name: document.verifier.firstName,
      last_name: document.verifier.lastName,
      email: document.verifier.email
    } : null
  };
};

/**
 * Transform document type data to mobile format
 * @param {Object} documentType - Document type object
 * @returns {Object} Mobile formatted document type
 */
const transformDocumentTypeToMobile = (documentType) => {
  if (!documentType) return null;

  return {
    id: documentType.id,
    name: documentType.name,
    code: documentType.code,
    description: documentType.description || '',
    category: documentType.category,
    is_required: documentType.isRequired || false,
    validity_duration: documentType.validityDuration,
    allowed_file_types: documentType.allowedFileTypes || [],
    max_file_size: documentType.maxFileSize,
    requires_verification: documentType.requiresVerification || false,
    is_active: documentType.isActive !== false,
    created_at: documentType.createdAt,
    updated_at: documentType.updatedAt
  };
};

/**
 * Get general documents
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getGeneralDocuments = async (req, res, next) => {
  try {
    const { 
      page = 1, 
      size = 10, 
      document_type, 
      document_type_id,
      sort_by = 'created_at', 
      sort_order = 'desc',
      is_public,
      search
    } = req.query;

    // Convert mobile params to service params
    const pagination = { page: parseInt(page), limit: parseInt(size) };
    const sorting = { 
      sortBy: sort_by === 'created_at' ? 'createdAt' : 
              sort_by === 'updated_at' ? 'updatedAt' : 
              sort_by === 'document_type' ? 'documentType' : sort_by,
      sortOrder: sort_order.toUpperCase()
    };
    const filters = { 
      documentType: document_type,
      documentTypeId: document_type_id,
      isPublic: is_public,
      search
    };

    // Get general documents
    const result = await documentService.getGeneralDocuments(
      filters,
      pagination,
      sorting,
      req.tenantContext
    );

    // Transform documents to mobile format
    const mobileDocuments = result.documents.map(transformDocumentToMobile);

    return mobileSuccessResponse(res, {
      count: result.total,
      rows: mobileDocuments
    }, 'General documents retrieved successfully');

  } catch (error) {
    logger.error('Error getting general documents:', error);
    return mobileSuccessResponse(res, {}, error.message, 500);
  }
};

/**
 * Get general document by ID
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getGeneralDocumentById = async (req, res, next) => {
  try {
    const { documentId } = req.params;

    // Get document by ID
    const document = await documentService.getDocumentById(documentId, req.tenantContext);

    if (!document) {
      return mobileSuccessResponse(res, {}, 'Document not found', 404);
    }

    // Verify it's a general document (not tied to employee)
    if (document.employeeId !== null) {
      return mobileSuccessResponse(res, {}, 'This is not a general document', 403);
    }

    // Transform to mobile format
    const mobileDocument = transformDocumentToMobile(document);

    return mobileSuccessResponse(res, mobileDocument, 'General document retrieved successfully');

  } catch (error) {
    logger.error('Error getting general document by ID:', error);
    return mobileSuccessResponse(res, {}, error.message, 500);
  }
};

/**
 * Create general document
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createGeneralDocument = async (req, res, next) => {
  try {
    // Convert mobile format to service format
    const documentData = {
      title: req.body.title || 'Other',
      description: req.body?.description||'',
      fileUrl: req.body.file_url,
      fileSize: req.body?.file_size||100,
      mimeType: req.body?.mime_type||'application/octet-stream',
      tags: [`${req.body?.tags||'Other'},certificate`],
    }; 
    const filter ={
      category:'other'
    }
    const docType = await documentService.getDocumentTypesSingle(req.tenantContext,filter);
    documentData.documentTypeId = docType.id;

    // Create general document
    const document = await documentService.createGeneralDocument(
      documentData,
      req.tenantContext
    );

    // Transform to mobile format
    const mobileDocument = transformDocumentToMobile(document);

    return mobileSuccessResponse(res, mobileDocument, 'General document created successfully', 201);

  } catch (error) {
    logger.error('Error creating general document:', error);
    return mobileSuccessResponse(res, {}, error.message, 500);
  }
};

/**
 * Update general document
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateGeneralDocument = async (req, res, next) => {
  try {
    const { documentId } = req.params;

    // First verify that the document is a general document (not tied to employee)
    const existingDocument = await documentService.getDocumentById(documentId, req.tenantContext);

    if (!existingDocument) {
      return mobileSuccessResponse(res, {}, 'Document not found', 404);
    }

    if (existingDocument.employeeId !== null) {
      return mobileSuccessResponse(res, {}, 'This is not a general document', 403);
    }

    // Convert mobile format to service format
    const documentData = {};
    if (req.body.document_type !== undefined) documentData.documentType = req.body.document_type;
    if (req.body.document_type_id !== undefined) documentData.documentTypeId = req.body.document_type_id;
    if (req.body.title !== undefined) documentData.title = req.body.title;
    if (req.body.description !== undefined) documentData.description = req.body.description;
    if (req.body.file_url !== undefined) documentData.fileUrl = req.body.file_url;
    if (req.body.file_size !== undefined) documentData.fileSize = req.body.file_size;
    if (req.body.mime_type !== undefined) documentData.mimeType = req.body.mime_type;
    if (req.body.expiry_date !== undefined) documentData.expiryDate = req.body.expiry_date;
    if (req.body.is_public !== undefined) documentData.isPublic = req.body.is_public;
    if (req.body.tags !== undefined) documentData.tags = req.body.tags;
    if (req.body.business_unit_id !== undefined) documentData.businessUnitId = req.body.business_unit_id;

    // Update general document
    const document = await documentService.updateGeneralDocument(
      documentId,
      documentData,
      req.tenantContext
    );

    // Transform to mobile format
    const mobileDocument = transformDocumentToMobile(document);

    return mobileSuccessResponse(res, mobileDocument, 'General document updated successfully');

  } catch (error) {
    logger.error('Error updating general document:', error);
    return mobileSuccessResponse(res, {}, error.message, 500);
  }
};

/**
 * Delete general document
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteGeneralDocument = async (req, res, next) => {
  try {
    const { documentId } = req.params;

    // First verify that the document is a general document (not tied to employee)
    const existingDocument = await documentService.getDocumentById(documentId, req.tenantContext);

    if (!existingDocument) {
      return mobileSuccessResponse(res, {}, 'Document not found', 404);
    }

    if (existingDocument.employeeId !== null) {
      return mobileSuccessResponse(res, {}, 'This is not a general document', 403);
    }

    // Delete general document
    await documentService.deleteDocument(documentId, req.tenantContext);

    return mobileSuccessResponse(res, {}, 'General document deleted successfully');

  } catch (error) {
    logger.error('Error deleting general document:', error);
    return mobileSuccessResponse(res, {}, error.message, 500);
  }
};

/**
 * Download general document
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const downloadGeneralDocument = async (req, res, next) => {
  try {
    const { documentId } = req.params;

    // Get document by ID
    const document = await documentService.getDocumentById(documentId, req.tenantContext);

    if (!document) {
      return mobileSuccessResponse(res, {}, 'Document not found', 404);
    }

    // Verify it's a general document (not tied to employee)
    if (document.employeeId !== null) {
      return mobileSuccessResponse(res, {}, 'This is not a general document', 403);
    }

    // Return download URL
    return mobileSuccessResponse(res, {
      download_url: document.fileUrl,
      file_name: document.title,
      file_size: document.fileSize,
      mime_type: document.mimeType
    }, 'Document download URL retrieved successfully');

  } catch (error) {
    logger.error('Error getting document download URL:', error);
    return mobileSuccessResponse(res, {}, error.message, 500);
  }
};

/**
 * Verify general document
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const verifyGeneralDocument = async (req, res, next) => {
  try {
    const { documentId } = req.params;

    // Get current user's employee record
    const employee = await getEmployeeByUserId(req.user.userId, req.tenantContext);

    // First verify that the document is a general document (not tied to employee)
    const existingDocument = await documentService.getDocumentById(documentId, req.tenantContext);

    if (!existingDocument) {
      return mobileSuccessResponse(res, {}, 'Document not found', 404);
    }

    if (existingDocument.employeeId !== null) {
      return mobileSuccessResponse(res, {}, 'This is not a general document', 403);
    }

    // Verify document
    const document = await documentService.verifyDocument(
      documentId,
      req.user.userId,
      req.tenantContext
    );

    // Transform to mobile format
    const mobileDocument = transformDocumentToMobile(document);

    return mobileSuccessResponse(res, mobileDocument, 'General document verified successfully');

  } catch (error) {
    logger.error('Error verifying general document:', error);
    return mobileSuccessResponse(res, {}, error.message, 500);
  }
};

/**
 * Get document types
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getDocumentTypes = async (req, res, next) => {
  try {
    const {
      page = 1,
      size = 10,
      category,
      is_required,
      is_active = true,
      search
    } = req.query;

    // Convert mobile params to service params
    const filters = {
      category,
      isRequired: is_required,
      isActive: is_active,
      search
    };

    // Get document types
    const documentTypes = await documentService.getDocumentTypes(req.tenantContext, filters);

    // Apply pagination manually since service doesn't support it
    const startIndex = (parseInt(page) - 1) * parseInt(size);
    const endIndex = startIndex + parseInt(size);
    const paginatedTypes = documentTypes.slice(startIndex, endIndex);

    // Transform to mobile format
    const mobileDocumentTypes = paginatedTypes.map(transformDocumentTypeToMobile);

    return mobileSuccessResponse(res, {
      count: documentTypes.length,
      rows: mobileDocumentTypes
    }, 'Document types retrieved successfully');

  } catch (error) {
    logger.error('Error getting document types:', error);
    return mobileSuccessResponse(res, {}, error.message, 500);
  }
};

/**
 * Get document type by ID
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getDocumentTypeById = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get document type by ID
    const documentType = await documentService.getDocumentTypeById(id, req.tenantContext);

    if (!documentType) {
      return mobileSuccessResponse(res, {}, 'Document type not found', 404);
    }

    // Transform to mobile format
    const mobileDocumentType = transformDocumentTypeToMobile(documentType);

    return mobileSuccessResponse(res, mobileDocumentType, 'Document type retrieved successfully');

  } catch (error) {
    logger.error('Error getting document type by ID:', error);
    return mobileSuccessResponse(res, {}, error.message, 500);
  }
};

/**
 * Create document type
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createDocumentType = async (req, res, next) => {
  try {
    // Convert mobile format to service format
    const documentTypeData = {
      name: req.body.name,
      code: req.body.code,
      description: req.body.description,
      category: req.body.category,
      isRequired: req.body.is_required,
      validityDuration: req.body.validity_duration,
      allowedFileTypes: req.body.allowed_file_types,
      maxFileSize: req.body.max_file_size,
      requiresVerification: req.body.requires_verification,
      isActive: req.body.is_active
    };

    // Create document type
    const documentType = await documentService.createDocumentType(
      documentTypeData,
      req.tenantContext
    );

    // Transform to mobile format
    const mobileDocumentType = transformDocumentTypeToMobile(documentType);

    return mobileSuccessResponse(res, mobileDocumentType, 'Document type created successfully', 201);

  } catch (error) {
    logger.error('Error creating document type:', error);
    return mobileSuccessResponse(res, {}, error.message, 500);
  }
};

/**
 * Update document type
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateDocumentType = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Convert mobile format to service format
    const documentTypeData = {};
    if (req.body.name !== undefined) documentTypeData.name = req.body.name;
    if (req.body.code !== undefined) documentTypeData.code = req.body.code;
    if (req.body.description !== undefined) documentTypeData.description = req.body.description;
    if (req.body.category !== undefined) documentTypeData.category = req.body.category;
    if (req.body.is_required !== undefined) documentTypeData.isRequired = req.body.is_required;
    if (req.body.validity_duration !== undefined) documentTypeData.validityDuration = req.body.validity_duration;
    if (req.body.allowed_file_types !== undefined) documentTypeData.allowedFileTypes = req.body.allowed_file_types;
    if (req.body.max_file_size !== undefined) documentTypeData.maxFileSize = req.body.max_file_size;
    if (req.body.requires_verification !== undefined) documentTypeData.requiresVerification = req.body.requires_verification;
    if (req.body.is_active !== undefined) documentTypeData.isActive = req.body.is_active;

    // Update document type
    const documentType = await documentService.updateDocumentType(
      id,
      documentTypeData,
      req.tenantContext
    );

    // Transform to mobile format
    const mobileDocumentType = transformDocumentTypeToMobile(documentType);

    return mobileSuccessResponse(res, mobileDocumentType, 'Document type updated successfully');

  } catch (error) {
    logger.error('Error updating document type:', error);
    return mobileSuccessResponse(res, {}, error.message, 500);
  }
};

module.exports = {
  getGeneralDocuments,
  getGeneralDocumentById,
  createGeneralDocument,
  updateGeneralDocument,
  deleteGeneralDocument,
  downloadGeneralDocument,
  verifyGeneralDocument,
  getDocumentTypes,
  getDocumentTypeById,
  createDocumentType,
  updateDocumentType
};
