'use strict';

const { mobileSuccessResponse } = require('../../../common/utils/mobileResponseTransformer');
const logger = require('../../../common/logging');
const employeeService = require('../../../services/employee/employeeService');
const { NotFoundError, ValidationError } = require('../../../common/errors');

/**
 * Get current user's education records for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getEducation = async (req, res, next) => {
  try {
    // Use the current user's employee ID from tenant context
    const employeeId = req.tenantContext.employeeId;

    if (!employeeId) {
      throw new ValidationError('Employee ID not found in user context');
    }

    // Get education records
    const education = await employeeService.getEducation(employeeId, req.tenantContext);

    // Transform data for mobile response
    const mobileEducation = education.map(edu => ({
      id: edu.id,
      institution: edu.institution,
      university: edu.university,
      degree: edu.degree,
      fieldOfStudy: edu.fieldOfStudy,
      startDate: edu.startDate,
      endDate: edu.endDate,
      grade: edu.grade,
      activities: edu.activities,
      description: edu.description,
      skills: Array.isArray(edu.skill) ? edu.skill : (edu.skill ? [edu.skill] : []),
      isVerified: edu.isVerified,
      verifiedAt: edu.verifiedAt,
      document: edu.document ? {
        id: edu.document.id,
        title: edu.document.title,
        fileUrl: edu.document.fileUrl,
        documentType: edu.document.documentType
      } : null,
      verifier: edu.verifier ? {
        id: edu.verifier.id,
        name: `${edu.verifier.firstName} ${edu.verifier.lastName}`,
        email: edu.verifier.email
      } : null,
      createdAt: edu.createdAt,
      updatedAt: edu.updatedAt
    }));

    return mobileSuccessResponse(
      res,
      mobileEducation,
      'Education records retrieved successfully'
    );
  } catch (error) {
    logger.error('Error getting education records:', error);
    next(error);
  }
};

/**
 * Get single education record for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getEducationById = async (req, res, next) => {
  try {
    const { educationId } = req.params;

    // Use the current user's employee ID from tenant context
    const employeeId = req.tenantContext.employeeId;

    if (!employeeId) {
      throw new ValidationError('Employee ID not found in user context');
    }

    if (!educationId || isNaN(educationId)) {
      throw new ValidationError('Valid education ID is required');
    }

    // Get all education records and find the specific one
    const education = await employeeService.getEducation(employeeId, req.tenantContext);
    const educationRecord = education.find(edu => edu.id === parseInt(educationId));

    if (!educationRecord) {
      throw new NotFoundError('Education record not found');
    }

    // Transform data for mobile response
    const mobileEducation = {
      id: educationRecord.id,
      institution: educationRecord.institution,
      university: educationRecord.university,
      degree: educationRecord.degree,
      fieldOfStudy: educationRecord.fieldOfStudy,
      startDate: educationRecord.startDate,
      endDate: educationRecord.endDate,
      grade: educationRecord.grade,
      activities: educationRecord.activities,
      description: educationRecord.description,
      skills: Array.isArray(educationRecord.skill) ? educationRecord.skill : (educationRecord.skill ? [educationRecord.skill] : []),
      isVerified: educationRecord.isVerified,
      verifiedAt: educationRecord.verifiedAt,
      document: educationRecord.document ? {
        id: educationRecord.document.id,
        title: educationRecord.document.title,
        fileUrl: educationRecord.document.fileUrl,
        documentType: educationRecord.document.documentType
      } : null,
      verifier: educationRecord.verifier ? {
        id: educationRecord.verifier.id,
        name: `${educationRecord.verifier.firstName} ${educationRecord.verifier.lastName}`,
        email: educationRecord.verifier.email
      } : null,
      createdAt: educationRecord.createdAt,
      updatedAt: educationRecord.updatedAt
    };

    return mobileSuccessResponse(
      res,
      mobileEducation,
      'Education record retrieved successfully'
    );
  } catch (error) {
    logger.error('Error getting education record:', error);
    next(error);
  }
};

/**
 * Add education record for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const addEducation = async (req, res, next) => {
  try {
    // Use the current user's employee ID from tenant context
    const employeeId = req.tenantContext.employeeId;

    if (!employeeId) {
      throw new ValidationError('Employee ID not found in user context');
    }

    // Add education record
    const educationRecord = await employeeService.addEducation(
      employeeId,
      req.body,
      req.tenantContext
    );

    // Transform data for mobile response
    const mobileEducation = {
      id: educationRecord.id,
      institution: educationRecord.institution,
      university: educationRecord.university,
      degree: educationRecord.degree,
      fieldOfStudy: educationRecord.fieldOfStudy,
      startDate: educationRecord.startDate,
      endDate: educationRecord.endDate,
      grade: educationRecord.grade,
      activities: educationRecord.activities,
      description: educationRecord.description,
      skills: Array.isArray(educationRecord.skill) ? educationRecord.skill : (educationRecord.skill ? [educationRecord.skill] : []),
      isVerified: educationRecord.isVerified,
      verifiedAt: educationRecord.verifiedAt,
      document: educationRecord.document ? {
        id: educationRecord.document.id,
        title: educationRecord.document.title,
        fileUrl: educationRecord.document.fileUrl,
        documentType: educationRecord.document.documentType
      } : null,
      createdAt: educationRecord.createdAt,
      updatedAt: educationRecord.updatedAt
    };

    return mobileSuccessResponse(
      res,
      mobileEducation,
      'Education record added successfully',
      201
    );
  } catch (error) {
    logger.error('Error adding education record:', error);
    next(error);
  }
};

/**
 * Update education record for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateEducation = async (req, res, next) => {
  try {
    const { educationId } = req.params || req.body?.educationId;

    // Use the current user's employee ID from tenant context
    const employeeId = req.tenantContext.employeeId;

    if (!employeeId) {
      throw new ValidationError('Employee ID not found in user context');
    }

    if (!educationId || isNaN(educationId)) {
      throw new ValidationError('Valid education ID is required');
    }

    // Update education record
    const educationRecord = await employeeService.updateEducation(
      employeeId,
      educationId,
      req.body,
      req.tenantContext
    );

    // Transform data for mobile response
    const mobileEducation = {
      id: educationRecord.id,
      institution: educationRecord.institution,
      university: educationRecord.university,
      degree: educationRecord.degree,
      fieldOfStudy: educationRecord.fieldOfStudy,
      startDate: educationRecord.startDate,
      endDate: educationRecord.endDate,
      grade: educationRecord.grade,
      activities: educationRecord.activities,
      description: educationRecord.description,
      skills: Array.isArray(educationRecord.skill) ? educationRecord.skill : (educationRecord.skill ? [educationRecord.skill] : []),
      isVerified: educationRecord.isVerified,
      verifiedAt: educationRecord.verifiedAt,
      document: educationRecord.document ? {
        id: educationRecord.document.id,
        title: educationRecord.document.title,
        fileUrl: educationRecord.document.fileUrl,
        documentType: educationRecord.document.documentType
      } : null,
      verifier: educationRecord.verifier ? {
        id: educationRecord.verifier.id,
        name: `${educationRecord.verifier.firstName} ${educationRecord.verifier.lastName}`,
        email: educationRecord.verifier.email
      } : null,
      createdAt: educationRecord.createdAt,
      updatedAt: educationRecord.updatedAt
    };

    return mobileSuccessResponse(
      res,
      mobileEducation,
      'Education record updated successfully'
    );
  } catch (error) {
    logger.error('Error updating education record:', error);
    next(error);
  }
};

/**
 * Delete education record for mobile
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteEducation = async (req, res, next) => {
  try {
    const { educationId } = req.params;

    // Use the current user's employee ID from tenant context
    const employeeId = req.tenantContext.employeeId;

    if (!employeeId) {
      throw new ValidationError('Employee ID not found in user context');
    }

    if (!educationId || isNaN(educationId)) {
      throw new ValidationError('Valid education ID is required');
    }

    // Delete education record
    await employeeService.deleteEducation(
      employeeId,
      educationId,
      req.tenantContext
    );

    return mobileSuccessResponse(
      res,
      { deleted: true },
      'Education record deleted successfully'
    );
  } catch (error) {
    logger.error('Error deleting education record:', error);
    next(error);
  }
};

module.exports = {
  getEducation,
  getEducationById,
  addEducation,
  updateEducation,
  deleteEducation
};
