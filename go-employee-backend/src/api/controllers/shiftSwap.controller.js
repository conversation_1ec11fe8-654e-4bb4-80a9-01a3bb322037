'use strict';

/**
 * Shift Swap Controller - PRD Implementation
 * 
 * Handles shift swap management according to PRD specifications:
 * - Shift swap request workflow (request → approve → execute)
 * - Swap validation and conflict detection
 * - Approval workflow management
 * - Swap history and analytics
 */

const shiftSwapService = require('../../services/rota/shiftSwapService');
const shiftSwapWorkflowService = require('../../services/rota/shiftSwapWorkflowService');
const workflowFilterService = require('../../services/common/workflowFilterService');
const approvalDetailsService = require('../../services/workflow/approvalDetailsService');
const { successResponse } = require('../../common/utils/response');
const { ValidationError, NotFoundError } = require('../../common/errors');
const { getPaginationParams, getSortingParams } = require('../../common/utils/pagination');
const { createPagination } = require('../../common/utils/response');

/**
 * Get all shift swap requests with filtering
 * @route GET /api/v1/shift-swaps
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAllSwapRequests = async (req, res, next) => {
  try {
    // Get pagination and sorting parameters
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'createdAt', 'desc');

    // Get filter parameters
    let filters = {};
    if (req.query.requesterId) filters.requesterId = req.query.requesterId;
    if (req.query.targetId) filters.targetId = req.query.targetId;
    if (req.query.approverId) filters.approverId = req.query.approverId;
    if (req.query.departmentId) filters.departmentId = req.query.departmentId;
    if (req.query.status) filters.status = req.query.status;
    if (req.query.startDate) filters.startDate = req.query.startDate;
    if (req.query.endDate) filters.endDate = req.query.endDate;
    if (req.query.search) filters.search = req.query.search;
    // Add decision-based filtering
    if (req.query.decision) filters.decision = req.query.decision;

    req.tenantContext.user = req.user;

    // Apply workflow-based filtering
    // Admin users see all swap requests, non-admin users only see swap requests assigned to them for approval
    filters = await workflowFilterService.applyWorkflowFiltering(filters, 'shift_swap', req.tenantContext);

    // Get all swap requests
    const result = await shiftSwapService.getAllSwapRequests(
      filters,
      pagination,
      sorting,
      req.tenantContext
    );

    // Add approver status with enhanced decision logic
    if (result.swapRequests && result.swapRequests.length > 0) {
      try {
        const swapRequestIds = result.swapRequests.map(swap => swap.id);

        // Get approver status for all swap requests
        const approverStatusMap = await workflowFilterService.getBulkApproverStatus(
          'shift_swap',
          swapRequestIds,
          req.tenantContext
        );

        // Transform response to maintain original structure with enhanced decision logic
        result.swapRequests = result.swapRequests.map(swap => {
          const swapData = swap.toJSON ? swap.toJSON() : swap;
          const approverStatus = approverStatusMap[swap.id] || {};

          // Enhanced decision logic: Apply priority-based decision logic
          let finalDecision = 'pending';
          let canApprove = false;
          let canReject = false;

          // Priority 1: Check entity's final status first
          if (swap.status === 'approved') {
            // Final status is decided - use that as decision
            finalDecision = swap.status;
            canApprove = false;
            canReject = true;
          }
          else if(swap.status === 'rejected'){
              finalDecision = swap.status;
              canApprove = false;
              canReject = false;
          } else if (swap.status === 'pending') {
            // Priority 2: Final status is still pending - check current approver's decision
            finalDecision = approverStatus.decision || 'pending';
            // User can take action only if their assignment is still pending
            canApprove = approverStatus.canApprove || false;
            canReject = approverStatus.canReject || false;
          } else {
            // Handle other statuses (cancelled, executed, etc.)
            finalDecision = swap.status;
            canApprove = false;
            canReject = false;
          }

          // Add approverStatus object with enhanced decision logic
          swapData.approverStatus = {
            canApprove: canApprove,
            canReject: canReject,
            isAssigned: approverStatus.isAssigned || false,
            approverLevel: approverStatus.approverLevel || null,
            isAdmin: approverStatus.isAdmin || false,
            assignmentStatus: approverStatus.assignmentStatus || null,
            decision: finalDecision, // Enhanced decision logic applied
            decidedAt: approverStatus.decidedAt || null,
            comments: approverStatus.comments || null
          };

          return swapData;
        });
      } catch (error) {
        console.error('❌ Failed to get approver status for swap requests:', error);
        // Add default approverStatus if there's an error
        result.swapRequests = result.swapRequests.map(swap => {
          const swapData = swap.toJSON ? swap.toJSON() : swap;
          swapData.approverStatus = {
            canApprove: false,
            canReject: false,
            isAssigned: false,
            approverLevel: null,
            isAdmin: false,
            assignmentStatus: null,
            decision: swap.status || 'pending',
            decidedAt: null,
            comments: null
          };
          return swapData;
        });
      }
    }

    // Conditionally add comprehensive approval details
    if (req.query.includeApproverDetails === 'true' && result.swapRequests && result.swapRequests.length > 0) {
      try {
        const swapRequestIds = result.swapRequests.map(swap => swap.id);

        // Get comprehensive approval details for each swap request
        const approvalDetailsPromises = swapRequestIds.map(async (swapId) => {
          try {
            return await approvalDetailsService.getComprehensiveApprovalDetails(
              'shift_swap',
              swapId,
              req.tenantContext
            );
          } catch (error) {
            console.error(`Failed to get approval details for swap request ${swapId}:`, error);
            return null;
          }
        });

        const approvalDetailsArray = await Promise.all(approvalDetailsPromises);

        // Add approval details to each swap request
        result.swapRequests = result.swapRequests.map((swap, index) => {
          const approvalDetails = approvalDetailsArray[index];

          if (approvalDetails) {
            swap.approvalDetails = approvalDetails;
          }

          return swap;
        });
      } catch (error) {
        console.error('❌ Failed to get comprehensive approval details for swap requests:', error);
        // Continue without approval details if there's an error
      }
    }

    return successResponse(res, {
      message: 'Shift swap requests retrieved successfully',
      data: result.swapRequests,
      pagination: createPagination(pagination.page, pagination.limit, result.total)
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get swap request by ID with detailed information
 * @route GET /api/v1/shift-swaps/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getSwapRequestById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { 
      includeHistory = false,
      includeValidation = true 
    } = req.query;

    const options = {
      includeHistory: includeHistory === 'true',
      includeValidation: includeValidation === 'true'
    };

    const swapRequest = await shiftSwapService.getSwapRequestById(id, req.tenantContext, options);

    if (!swapRequest) {
      return res.status(404).json({
        success: false,
        message: 'Shift swap request not found'
      });
    }

    return successResponse(res, {
      message: 'Shift swap request retrieved successfully',
      data: swapRequest
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create new shift swap request
 * @route POST /api/v1/shift-swaps
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createSwapRequest = async (req, res, next) => {
  try {
    const swapData = {
      ...req.body, 
      createdById: req.tenantContext.userId
    };

    const swapRequest = await shiftSwapService.createSwapRequest(swapData, req.tenantContext);

    return successResponse(res, {
      message: 'Shift swap request created successfully',
      data: swapRequest
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Update swap request
 * @route PUT /api/v1/shift-swaps/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      updatedById: req.tenantContext.userId
    };

    const swapRequest = await shiftSwapService.updateSwapRequest(id, updateData, req.tenantContext);

    return successResponse(res, {
      message: 'Shift swap request updated successfully',
      data: swapRequest
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Cancel swap request
 * @route POST /api/v1/shift-swaps/:id/cancel
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const cancelSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const cancelData = {
      reason,
      cancelledBy: req.tenantContext.userId,
      cancelledAt: new Date()
    };

    const swapRequest = await shiftSwapService.cancelSwapRequest(id, cancelData, req.tenantContext);

    return successResponse(res, {
      message: 'Shift swap request cancelled successfully',
      data: swapRequest
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Approve swap request through workflow (same pattern as leave approval)
 * @route POST /api/v1/shift-swaps/:id/approve
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const approveSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { comments, notes, executeImmediately = false } = req.body;

    const approvalData = {
      comments: comments || notes || 'Approved',
      executeImmediately,
      approvedBy: req.tenantContext.userId,
      approvedAt: new Date()
    };

    // Use workflow service for approval (same pattern as leave workflow)
    const result = await shiftSwapWorkflowService.processSwapApproval(id, approvalData, req.tenantContext);

    return successResponse(res, {
      message: 'Shift swap request approval processed successfully',
      data: {
        workflowResult: result,
        isFinalDecision: result.isFinalDecision,
        status: result.workflowStatus
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Reject swap request through workflow (same pattern as leave rejection)
 * @route POST /api/v1/shift-swaps/:id/reject
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const rejectSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { reason, notes, rejectionReason } = req.body;

    const rejectionData = {
      rejectionReason: rejectionReason || reason || 'Rejected',
      comments: notes || reason || 'Rejected',
      rejectedBy: req.tenantContext.userId,
      rejectedAt: new Date()
    };

    // Use workflow service for rejection (same pattern as leave workflow)
    const result = await shiftSwapWorkflowService.processSwapRejection(id, rejectionData, req.tenantContext);

    return successResponse(res, {
      message: 'Shift swap request rejection processed successfully',
      data: {
        workflowResult: result,
        isFinalDecision: result.isFinalDecision,
        status: result.workflowStatus
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Execute approved swap
 * @route POST /api/v1/shift-swaps/:id/execute
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const executeSwap = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;

    const executionData = {
      notes,
      executedBy: req.tenantContext.userId,
      executedAt: new Date()
    };

    const swapRequest = await shiftSwapService.executeSwap(id, executionData, req.tenantContext);

    return successResponse(res, {
      message: 'Shift swap executed successfully',
      data: swapRequest
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create swap request (alternative endpoint)
 * @route POST /api/v1/shift-swaps/request
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createSwapRequestAlt = async (req, res, next) => {
  try {
    const swapData = {
      ...req.body,
      requesterId: req.tenantContext.userId,
      createdById: req.tenantContext.userId
    };

    const swapRequest = await shiftSwapService.createSwapRequest(swapData, req.tenantContext);

    return successResponse(res, {
      message: 'Swap request created successfully',
      data: swapRequest
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Respond to swap request (employee response)
 * @route POST /api/v1/shift-swaps/:id/respond
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const respondToSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { response, notes } = req.body; // response: 'accept' or 'decline'

    const responseData = {
      response,
      notes,
      respondedBy: req.tenantContext.userId,
      respondedAt: new Date()
    };

    const swapRequest = await shiftSwapService.respondToSwapRequest(id, responseData, req.tenantContext);

    return successResponse(res, {
      message: `Swap request ${response}ed successfully`,
      data: swapRequest
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get eligible employees for swap
 * @route GET /api/v1/shift-swaps/:id/eligible-employees
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getEligibleEmployees = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { limit = 20, includeUnavailable = false } = req.query;

    const options = {
      limit: parseInt(limit),
      includeUnavailable: includeUnavailable === 'true'
    };

    const eligibleEmployees = await shiftSwapService.getEligibleEmployeesForSwap(id, options, req.tenantContext);

    return successResponse(res, {
      message: 'Eligible employees retrieved successfully',
      data: eligibleEmployees
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get swap analytics
 * @route GET /api/v1/shift-swaps/analytics
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getSwapAnalytics = async (req, res, next) => {
  try {
    const {
      startDate,
      endDate,
      departmentId,
      period = '30d'
    } = req.query;

    const filters = {
      startDate,
      endDate,
      departmentId: departmentId ? parseInt(departmentId) : null,
      period
    };

    const analytics = await shiftSwapService.getSwapAnalytics(filters, req.tenantContext);

    return successResponse(res, {
      message: 'Swap analytics retrieved successfully',
      data: analytics
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get available swaps for employee
 * @route GET /api/v1/employees/:employeeId/available-swaps
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAvailableSwaps = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    const {
      startDate,
      endDate,
      departmentId,
      limit = 20
    } = req.query;

    const options = {
      startDate,
      endDate,
      departmentId: departmentId ? parseInt(departmentId) : null,
      limit: parseInt(limit)
    };

    const availableSwaps = await shiftSwapService.getAvailableSwaps(employeeId, req.tenantContext, options);

    return successResponse(res, {
      message: 'Available swaps retrieved successfully',
      data: availableSwaps
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Validate swap request
 * @route POST /api/v1/shift-swaps/validate
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const validateSwapRequest = async (req, res, next) => {
  try {
    const { requesterAssignmentId, targetAssignmentId } = req.body;

    const validation = await shiftSwapService.validateSwapRequest({
      requesterAssignmentId,
      targetAssignmentId
    }, req.tenantContext);

    return successResponse(res, {
      message: 'Swap validation completed',
      data: validation
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get swap statistics
 * @route GET /api/v1/shift-swaps/statistics
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getSwapStatistics = async (req, res, next) => {
  try {
    const { 
      startDate, 
      endDate, 
      departmentId,
      period = '30d' 
    } = req.query;

    const options = {
      startDate,
      endDate,
      departmentId: departmentId ? parseInt(departmentId) : null,
      period
    };

    const statistics = await shiftSwapService.getSwapStatistics(options, req.tenantContext);

    return successResponse(res, {
      message: 'Swap statistics retrieved successfully',
      data: statistics
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get pending approvals for current user
 * @route GET /api/v1/shift-swaps/pending-approvals
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getPendingApprovals = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      urgency,
      departmentId
    } = req.query;

    const filters = {
      page: parseInt(page),
      limit: parseInt(limit),
      urgency,
      departmentId: departmentId ? parseInt(departmentId) : null
    };

    const result = await shiftSwapWorkflowService.getPendingApprovalsForUser(
      req.tenantContext.userId,
      req.tenantContext,
      filters
    );

    return successResponse(res, {
      message: 'Pending approvals retrieved successfully',
      data: result.assignments,
      pagination: result.pagination
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk approve swap requests
 * @route POST /api/v1/shift-swaps/bulk-approve
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkApproveSwaps = async (req, res, next) => {
  try {
    const { swapIds, notes, executeImmediately = false } = req.body;

    if (!swapIds || !Array.isArray(swapIds) || swapIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Swap IDs array is required'
      });
    }

    const approvalData = {
      notes,
      executeImmediately
    };

    const result = await shiftSwapWorkflowService.bulkApproveSwapRequests(swapIds, approvalData, req.tenantContext);

    return successResponse(res, {
      message: `Bulk approval completed: ${result.successful} successful, ${result.failed} failed`,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get employee's assigned shift for a specific date
 * @route GET /api/v1/shift-swaps/employee/:employeeId/shift/:date
 */
const getEmployeeAssignedShift = async (req, res, next) => {
  try {
    const { employeeId, date } = req.params;
    const tenantContext = req.tenantContext;

    const result = await shiftSwapService.getEmployeeAssignedShift(
      parseInt(employeeId),
      date,
      tenantContext
    );
    if (!result) {
      return res.status(404).json(result);
    }

    return successResponse(res,{message:'Assigned shift(s) fetched successfully.' ,data: result});
  } catch (error) {
    next(error);
  }
};

/**
 * Get available employees for shift swap
 * @route GET /api/v1/shift-swaps/available-employees/:date
 */
const getAvailableEmployeesForSwap = async (req, res, next) => {
  try {
    const { date } = req.params;
    const { designationId, shiftId } = req.query;
    const tenantContext = req.tenantContext;

    const result = await shiftSwapService.getAvailableEmployeesForSwap(
      date,
      designationId ? parseInt(designationId) : null,
      shiftId ? parseInt(shiftId) : null,
      tenantContext
    );

   return successResponse(res,{message:"" ,data: result});
  } catch (error) {
    next(error);
  }
};

/**
 * Get all scheduled shifts for a specific date
 * @route GET /api/v1/shift-swaps/scheduled-shifts/:date
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getScheduledShiftsByDate = async (req, res, next) => {
  try {
    const { date } = req.params;
    const { departmentId, designationId } = req.query;

    // Validate date format
    if (!date || !/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid date format. Use YYYY-MM-DD format.'
      });
    }

    const filters = {
      date,
      departmentId: departmentId ? parseInt(departmentId) : null,
      designationId: designationId ? parseInt(designationId) : null
    };

    const scheduledShifts = await shiftSwapService.getScheduledShiftsByDate(filters, req.tenantContext);

    return successResponse(res, {
      message: `Scheduled shifts retrieved for ${date}`,
      data: scheduledShifts
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  // Core CRUD Operations
  getAllSwapRequests,
  getSwapRequestById,
  createSwapRequest,
  updateSwapRequest,

  // Alternative Endpoints
  createSwapRequestAlt,
  respondToSwapRequest,

  // Swap Workflow
  cancelSwapRequest,
  approveSwapRequest,
  rejectSwapRequest,
  executeSwap,

  // Workflow Integration
  getPendingApprovals,

  // Swap Discovery & Validation
  getAvailableSwaps,
  getEligibleEmployees,
  validateSwapRequest,

  // Analytics & Bulk Operations
  getSwapStatistics,
  getSwapAnalytics,
  bulkApproveSwaps,

  // NEW APIs
  getEmployeeAssignedShift,
  getAvailableEmployeesForSwap,
  getScheduledShiftsByDate
};
