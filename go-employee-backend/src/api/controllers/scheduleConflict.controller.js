const { successResponse, errorResponse } = require('../utils/responseHelper');
const scheduleConflictService = require('../../services/rota/scheduleConflictService');

/**
 * Schedule Conflict Controller
 * Handles conflict management operations
 */

/**
 * Get all conflicts with filtering and pagination
 */
const getAllConflicts = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      conflictType,
      severity,
      designationId,
      employeeId,
      startDate,
      endDate,
      sessionId
    } = req.query;

    const filters = {
      status,
      conflictType,
      severity,
      designationId,
      employeeId,
      startDate,
      endDate,
      sessionId
    };

    const conflicts = await scheduleConflictService.getAllConflicts(
      filters,
      { page: parseInt(page), limit: parseInt(limit) },
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Conflicts retrieved successfully',
      data: conflicts
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get conflict by ID with recommendations
 */
const getConflictById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const conflict = await scheduleConflictService.getConflictById(
      id,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Conflict retrieved successfully',
      data: conflict
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get conflicts by session ID
 */
const getConflictsBySession = async (req, res, next) => {
  try {
    const { sessionId } = req.params;
    const { includeResolved = false } = req.query;

    const conflicts = await scheduleConflictService.getConflictsBySession(
      sessionId,
      { includeResolved: includeResolved === 'true' },
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Session conflicts retrieved successfully',
      data: conflicts
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update conflict status
 */
const updateConflictStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, resolutionStrategy, resolutionNotes } = req.body;

    const updatedConflict = await scheduleConflictService.updateConflictStatus(
      id,
      {
        status,
        resolutionStrategy,
        resolutionNotes,
        resolvedBy: req.tenantContext.userId
      },
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Conflict status updated successfully',
      data: updatedConflict
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Resolve conflict
 */
const resolveConflict = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { resolutionStrategy, resolutionNotes, implementedRecommendations } = req.body;

    const resolvedConflict = await scheduleConflictService.resolveConflict(
      id,
      {
        resolutionStrategy,
        resolutionNotes,
        implementedRecommendations,
        resolvedBy: req.tenantContext.userId
      },
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Conflict resolved successfully',
      data: resolvedConflict
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get conflict analytics/dashboard data
 */
const getConflictAnalytics = async (req, res, next) => {
  try {
    const {
      startDate,
      endDate,
      groupBy = 'type' // type, date, designation, severity
    } = req.query;

    const analytics = await scheduleConflictService.getConflictAnalytics(
      { startDate, endDate, groupBy },
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Conflict analytics retrieved successfully',
      data: analytics
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get recommendations for a conflict
 */
const getConflictRecommendations = async (req, res, next) => {
  try {
    const { id } = req.params;

    const recommendations = await scheduleConflictService.getConflictRecommendations(
      id,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Conflict recommendations retrieved successfully',
      data: recommendations
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update recommendation status
 */
const updateRecommendationStatus = async (req, res, next) => {
  try {
    const { recommendationId } = req.params;
    const { 
      status, 
      implementationNotes, 
      effectivenessRating,
      configurationChanges 
    } = req.body;

    const updatedRecommendation = await scheduleConflictService.updateRecommendationStatus(
      recommendationId,
      {
        status,
        implementationNotes,
        effectivenessRating,
        configurationChanges,
        implementedBy: req.tenantContext.userId
      },
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Recommendation status updated successfully',
      data: updatedRecommendation
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk resolve conflicts
 */
const bulkResolveConflicts = async (req, res, next) => {
  try {
    const { conflictIds, resolutionStrategy, resolutionNotes } = req.body;

    const results = await scheduleConflictService.bulkResolveConflicts(
      conflictIds,
      {
        resolutionStrategy,
        resolutionNotes,
        resolvedBy: req.tenantContext.userId
      },
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Conflicts resolved successfully',
      data: results
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get conflict trends and patterns
 */
const getConflictTrends = async (req, res, next) => {
  try {
    const {
      period = '30d', // 7d, 30d, 90d
      metric = 'count' // count, resolution_time, effectiveness
    } = req.query;

    const trends = await scheduleConflictService.getConflictTrends(
      { period, metric },
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Conflict trends retrieved successfully',
      data: trends
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllConflicts,
  getConflictById,
  getConflictsBySession,
  updateConflictStatus,
  resolveConflict,
  getConflictAnalytics,
  getConflictRecommendations,
  updateRecommendationStatus,
  bulkResolveConflicts,
  getConflictTrends
};
