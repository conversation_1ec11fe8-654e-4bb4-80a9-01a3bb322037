'use strict';

const { successResponse } = require('../../common/utils/response');
const TimesheetAutoApprovalCron = require('../../services/cron/timesheetAutoApprovalCron');
const logger = require('../../common/logging');

// Global instance of the cron service
let timesheetAutoApprovalCron = null;

/**
 * Initialize the timesheet auto-approval cron service
 */
const initializeCronService = () => {
  if (!timesheetAutoApprovalCron) {
    timesheetAutoApprovalCron = new TimesheetAutoApprovalCron();
    timesheetAutoApprovalCron.start();
    logger.info('✅ Timesheet Auto-Approval Cron Service initialized');
  }
  return timesheetAutoApprovalCron;
};

/**
 * Get cron job status
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getCronStatus = async (req, res, next) => {
  try {
    const cronService = initializeCronService();
    const status = cronService.getStatus();
    
    return successResponse(res, {
      message: 'Timesheet auto-approval cron status retrieved successfully',
      data: {
        serviceName: 'Timesheet Auto-Approval Cron Service',
        description: 'Automatically approves yesterday\'s pending timesheets daily at 2:00 AM UTC',
        currentTime: new Date().toISOString(),
        status: status,
        schedule: {
          cronExpression: '0 2 * * *',
          description: 'Daily at 2:00 AM UTC (7:30 AM IST)',
          timezone: 'UTC',
          purpose: 'Process previous day timesheets to avoid project calculation impacts'
        },
        features: [
          'Bypasses approval checking for automated processing',
          'Updates task and project actual hours',
          'Sends email notifications to employees',
          'Generates daily summary reports for HR',
          'Prevents project calculation delays'
        ]
      }
    });
  } catch (error) {
    logger.error('Error getting timesheet auto-approval cron status:', error);
    next(error);
  }
};

/**
 * Start the cron job
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const startCronJob = async (req, res, next) => {
  try {
    const cronService = initializeCronService();
    
    return successResponse(res, {
      message: 'Timesheet auto-approval cron job started successfully',
      data: {
        status: 'started',
        nextRun: 'Daily at 2:00 AM UTC',
        description: 'Cron job will automatically approve yesterday\'s pending timesheets'
      }
    });
  } catch (error) {
    logger.error('Error starting timesheet auto-approval cron job:', error);
    next(error);
  }
};

/**
 * Stop the cron job
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const stopCronJob = async (req, res, next) => {
  try {
    if (timesheetAutoApprovalCron) {
      timesheetAutoApprovalCron.stop();
      
      return successResponse(res, {
        message: 'Timesheet auto-approval cron job stopped successfully',
        data: {
          status: 'stopped',
          description: 'Cron job has been stopped and will not run automatically'
        }
      });
    } else {
      return successResponse(res, {
        message: 'Timesheet auto-approval cron job was not running',
        data: {
          status: 'not_running'
        }
      });
    }
  } catch (error) {
    logger.error('Error stopping timesheet auto-approval cron job:', error);
    next(error);
  }
};

/**
 * Manually trigger the cron job
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const manualTrigger = async (req, res, next) => {
  try {
    const cronService = initializeCronService();
    const { targetDate } = req.body; // Optional: specify a specific date to process
    
    logger.info(`🧪 Manual trigger initiated by user ${req.tenantContext.userId}`);
    
    const results = await cronService.manualTrigger(targetDate);
    
    return successResponse(res, {
      message: 'Timesheet auto-approval manually triggered successfully',
      data: {
        triggerType: 'manual',
        targetDate: targetDate || 'yesterday',
        results: {
          totalProcessed: results.totalProcessed,
          totalApproved: results.totalApproved,
          totalErrors: results.totalErrors,
          approvedTimesheets: results.approvedTimesheets,
          errors: results.errors
        },
        triggeredBy: req.tenantContext.userId,
        triggeredAt: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error manually triggering timesheet auto-approval:', error);
    next(error);
  }
};

/**
 * Get detailed statistics
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getDetailedStats = async (req, res, next) => {
  try {
    const cronService = initializeCronService();
    const status = cronService.getStatus();
    
    return successResponse(res, {
      message: 'Detailed timesheet auto-approval statistics retrieved successfully',
      data: {
        serviceName: 'Timesheet Auto-Approval Cron Service',
        currentStatus: {
          isScheduled: status.isScheduled,
          isRunning: status.isRunning,
          nextRun: status.nextRun
        },
        statistics: status.stats,
        configuration: {
          schedule: 'Daily at 2:00 AM UTC',
          timezone: 'UTC',
          targetDate: 'Previous day (yesterday)',
          approvalBypass: true,
          emailNotifications: true,
          summaryReports: true
        },
        workflow: [
          '1. Cron triggers daily at 2:00 AM UTC',
          '2. Finds all pending timesheets from yesterday',
          '3. Auto-approves each timesheet (bypasses permission checks)',
          '4. Updates task and project actual hours',
          '5. Sends email notifications to employees',
          '6. Generates summary report for HR managers',
          '7. Logs all activities for audit trail'
        ],
        benefits: [
          'Prevents project calculation delays',
          'Ensures timely billing and reporting',
          'Reduces manual approval workload',
          'Maintains audit trail and notifications',
          'Provides daily summary reports for oversight'
        ]
      }
    });
  } catch (error) {
    logger.error('Error getting detailed timesheet auto-approval stats:', error);
    next(error);
  }
};

/**
 * Test the email notification system
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const testEmailNotification = async (req, res, next) => {
  try {
    const cronService = initializeCronService();
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email address is required for testing'
      });
    }
    
    // Create mock results for testing
    const mockResults = {
      totalProcessed: 5,
      totalApproved: 4,
      totalErrors: 1,
      approvedTimesheets: [
        {
          id: 123,
          employeeId: 'EMP00001',
          employeeName: 'John Doe',
          projectName: 'Test Project',
          taskName: 'Development Task',
          duration: '8:00',
          date: new Date().toISOString().split('T')[0]
        }
      ],
      errors: [
        {
          timesheetId: 124,
          employeeId: 'EMP00002',
          error: 'Test error for demonstration'
        }
      ]
    };
    
    await cronService.sendSummaryEmail(mockResults);
    
    return successResponse(res, {
      message: 'Test email notification sent successfully',
      data: {
        testType: 'email_notification',
        sentTo: email,
        mockData: mockResults,
        sentAt: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error testing email notification:', error);
    next(error);
  }
};

module.exports = {
  initializeCronService,
  getCronStatus,
  startCronJob,
  stopCronJob,
  manualTrigger,
  getDetailedStats,
  testEmailNotification
};
