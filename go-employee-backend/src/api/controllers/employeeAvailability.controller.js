'use strict';

/**
 * Employee Availability Controller - PRD Implementation
 * 
 * Handles employee availability management according to PRD specifications:
 * - Employee availability CRUD operations
 * - Availability overrides and exceptions
 * - Consent management for shift assignments
 * - Bulk availability operations
 */

const employeeAvailabilityService = require('../../services/rota/employeeAvailabilityService');
const { successResponse } = require('../../common/utils/response');
const { ValidationError, NotFoundError } = require('../../common/errors');

/**
 * Get employee availability
 * @route GET /api/v1/employees/:employeeId/availability
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getEmployeeAvailability = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    const { 
      startDate, 
      endDate, 
      includeOverrides = true,
      includeConsents = false 
    } = req.query;

    const options = {
      startDate,
      endDate,
      includeOverrides: includeOverrides === 'true',
      includeConsents: includeConsents === 'true'
    };

    const availability = await employeeAvailabilityService.getEmployeeAvailability(
      employeeId, 
      req.tenantContext, 
      options
    );

    return successResponse(res, {
      message: 'Employee availability retrieved successfully',
      data: availability
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update employee availability
 * @route PUT /api/v1/employees/:employeeId/availability
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateEmployeeAvailability = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    const availabilityData = {
      ...req.body,
      updatedById: req.tenantContext.userId
    };

    const availability = await employeeAvailabilityService.updateEmployeeAvailability(
      employeeId,
      availabilityData,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Employee availability updated successfully',
      data: availability
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Add availability override
 * @route POST /api/v1/employees/:employeeId/availability/override
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const addAvailabilityOverride = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    const overrideData = {
      ...req.body,
      employeeId: parseInt(employeeId),
      createdById: req.tenantContext.userId
    };

    const override = await employeeAvailabilityService.addAvailabilityOverride(
      overrideData,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Availability override added successfully',
      data: override
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Remove availability override
 * @route DELETE /api/v1/employees/:employeeId/availability/override/:overrideId
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const removeAvailabilityOverride = async (req, res, next) => {
  try {
    const { employeeId, overrideId } = req.params;
    const { reason } = req.body;

    await employeeAvailabilityService.removeAvailabilityOverride(
      overrideId,
      { reason, removedBy: req.tenantContext.userId },
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Availability override removed successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Check employee availability for specific date/time
 * @route GET /api/v1/employees/:employeeId/availability/check
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const checkEmployeeAvailability = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    const { date, startTime, endTime, shiftInstanceId } = req.query;

    if (!date) {
      return res.status(400).json({
        success: false,
        message: 'Date is required for availability check'
      });
    }

    const checkData = {
      date,
      startTime,
      endTime,
      shiftInstanceId: shiftInstanceId ? parseInt(shiftInstanceId) : null
    };

    const availabilityCheck = await employeeAvailabilityService.checkEmployeeAvailability(
      employeeId,
      checkData,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Employee availability check completed',
      data: availabilityCheck
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get available employees for shift instance
 * @route GET /api/v1/shift-instances/:instanceId/available-employees
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAvailableEmployeesForInstance = async (req, res, next) => {
  try {
    const { instanceId } = req.params;
    const { 
      includeUnavailable = false,
      sortBy = 'priority',
      limit = 50 
    } = req.query;

    const options = {
      includeUnavailable: includeUnavailable === 'true',
      sortBy,
      limit: parseInt(limit)
    };

    const employees = await employeeAvailabilityService.getAvailableEmployeesForInstance(
      instanceId,
      req.tenantContext,
      options
    );

    return successResponse(res, {
      message: 'Available employees retrieved successfully',
      data: employees
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk availability check
 * @route POST /api/v1/availability/bulk-check
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkAvailabilityCheck = async (req, res, next) => {
  try {
    const { checks } = req.body;

    if (!checks || !Array.isArray(checks) || checks.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Checks array is required'
      });
    }

    const results = await employeeAvailabilityService.bulkAvailabilityCheck(
      checks,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Bulk availability check completed',
      data: results
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get employee consents
 * @route GET /api/v1/employees/:employeeId/availability/consents
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getEmployeeConsents = async (req, res, next) => {
  try {
    const { employeeId } = req.params;

    const consents = await employeeAvailabilityService.getEmployeeConsents(
      employeeId,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Employee consents retrieved successfully',
      data: consents
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update employee consents
 * @route PUT /api/v1/employees/:employeeId/availability/consents
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateEmployeeConsents = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    const consentData = {
      ...req.body,
      updatedById: req.tenantContext.userId
    };

    const consents = await employeeAvailabilityService.updateEmployeeConsents(
      employeeId,
      consentData,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Employee consents updated successfully',
      data: consents
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get consent summary for all employees
 * @route GET /api/v1/employees/consents/summary
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getConsentSummary = async (req, res, next) => {
  try {
    const { departmentId, designationId } = req.query;

    const filters = {
      departmentId: departmentId ? parseInt(departmentId) : null,
      designationId: designationId ? parseInt(designationId) : null
    };

    const summary = await employeeAvailabilityService.getConsentSummary(
      filters,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Consent summary retrieved successfully',
      data: summary
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  // Core Availability Operations
  getEmployeeAvailability,
  updateEmployeeAvailability,
  
  // Availability Overrides
  addAvailabilityOverride,
  removeAvailabilityOverride,
  
  // Availability Checking
  checkEmployeeAvailability,
  getAvailableEmployeesForInstance,
  bulkAvailabilityCheck,
  
  // Consent Management
  getEmployeeConsents,
  updateEmployeeConsents,
  getConsentSummary
};
