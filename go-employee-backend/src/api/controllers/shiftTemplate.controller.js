const shiftTemplateService = require('../../services/rota/shiftTemplateService');
const { successResponse } = require('../../common/utils/response');

/**
 * Create new shift template
 * @route POST /api/v1/shift-templates
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createTemplate = async (req, res, next) => {
  try {
    console.log('🚀 Creating shift template:', req.body.name);

    const template = await shiftTemplateService.createTemplate(req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Shift template created successfully',
      data: template
    }, 201);
  } catch (error) {
    console.error('❌ Create template error:', error.message);
    next(error);
  }
};

/**
 * Get all shift templates with filters
 * @route GET /api/v1/shift-templates
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAllTemplates = async (req, res, next) => {
  try {
    console.log('📋 Getting all shift templates with filters:', req.query);

    const result = await shiftTemplateService.getAllTemplates(req.query, req.tenantContext);

    return successResponse(res, {
      message: 'Shift templates retrieved successfully',
      data: result.templates,
      pagination: result.pagination
    });
  } catch (error) {
    console.error('❌ Get all templates error:', error.message);
    next(error);
  }
};

/**
 * Get shift template by ID
 * @route GET /api/v1/shift-templates/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getTemplateById = async (req, res, next) => {
  try {
    const { id } = req.params;
    console.log('📄 Getting shift template by ID:', id);

    const template = await shiftTemplateService.getTemplateById(id, req.tenantContext);

    return successResponse(res, {
      message: 'Shift template retrieved successfully',
      data: template
    });
  } catch (error) {
    console.error('❌ Get template by ID error:', error.message);
    next(error);
  }
};

/**
 * Update shift template
 * @route PUT /api/v1/shift-templates/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateTemplate = async (req, res, next) => {
  try {
    const { id } = req.params;
    console.log('✏️ Updating shift template:', id, req.body);

    const template = await shiftTemplateService.updateTemplate(id, req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Shift template updated successfully',
      data: template
    });
  } catch (error) {
    console.error('❌ Update template error:', error.message);
    next(error);
  }
};

/**
 * Delete shift template
 * @route DELETE /api/v1/shift-templates/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteTemplate = async (req, res, next) => {
  try {
    const { id } = req.params;
    console.log('🗑️ Deleting shift template:', id);

    await shiftTemplateService.deleteTemplate(id, req.tenantContext);

    return successResponse(res, {
      message: 'Shift template deleted successfully'
    });
  } catch (error) {
    console.error('❌ Delete template error:', error.message);
    next(error);
  }
};

/**
 * Clone shift template
 * @route POST /api/v1/shift-templates/:id/clone
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const cloneTemplate = async (req, res, next) => {
  try {
    const { id } = req.params;
    console.log('📋 Cloning shift template:', id, 'to:', req.body.name);

    const clonedTemplate = await shiftTemplateService.cloneTemplate(id, req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Shift template cloned successfully',
      data: clonedTemplate
    }, 201);
  } catch (error) {
    console.error('❌ Clone template error:', error.message);
    next(error);
  }
};

/**
 * Set template as default
 * @route PUT /api/v1/shift-templates/:id/set-default
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const setAsDefault = async (req, res, next) => {
  try {
    const { id } = req.params;
    console.log('⭐ Setting template as default:', id);

    const template = await shiftTemplateService.setAsDefault(id, req.tenantContext);

    return successResponse(res, {
      message: 'Template set as default successfully',
      data: template
    });
  } catch (error) {
    console.error('❌ Set as default error:', error.message);
    next(error);
  }
};

/**
 * Get available RotaShifts for template creation
 * @route GET /api/v1/shift-templates/available-shifts
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAvailableShifts = async (req, res, next) => {
  try {
    console.log('🔍 Getting available RotaShifts for template creation');

    const shifts = await shiftTemplateService.getAvailableRotaShifts(req.tenantContext);

    return successResponse(res, {
      message: 'Available shifts retrieved successfully',
      data: shifts
    });
  } catch (error) {
    console.error('❌ Get available shifts error:', error.message);
    next(error);
  }
};

/**
 * Get template usage statistics
 * @route GET /api/v1/shift-templates/:id/usage-stats
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getUsageStats = async (req, res, next) => {
  try {
    const { id } = req.params;
    console.log('📊 Getting template usage statistics:', id);

    const template = await shiftTemplateService.getTemplateById(id, req.tenantContext);

    // Calculate stats from normalized structure
    let totalWorkingDays = 0;
    let totalShifts = 0;

    if (template.dayConfigs) {
      totalWorkingDays = template.dayConfigs.filter(dayConfig => dayConfig.isWorkingDay).length;
      totalShifts = template.dayConfigs.reduce((total, dayConfig) => {
        return total + (dayConfig.dayShifts ? dayConfig.dayShifts.length : 0);
      }, 0);
    }

    const stats = {
      templateId: template.id,
      templateName: template.name,
      usageCount: template.usageCount || 0,
      lastUsedAt: template.lastUsedAt,
      isDefault: template.isDefault,
      totalWorkingDays,
      totalShifts,
      createdAt: template.createdAt,
      createdBy: template.createdBy
    };

    return successResponse(res, {
      message: 'Template usage statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    console.error('❌ Get usage stats error:', error.message);
    next(error);
  }
};

/**
 * Validate template configuration
 * @route POST /api/v1/shift-templates/validate
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const validateTemplate = async (req, res, next) => {
  try {
    console.log('✅ Validating template configuration');

    const { dayConfigurations } = req.body;

    // Validation is handled by the validator middleware
    // If we reach here, validation passed

    // Calculate validation summary from input dayConfigurations (still JSONB format from frontend)
    const validationResult = {
      isValid: true,
      message: 'Template configuration is valid',
      summary: {
        totalDays: Object.keys(dayConfigurations).length,
        workingDays: Object.values(dayConfigurations).filter(day => day.isWorkingDay !== false).length,
        totalShifts: Object.values(dayConfigurations)
          .reduce((total, day) => total + (day.shifts?.length || 0), 0),
        overrideShifts: Object.values(dayConfigurations)
          .reduce((total, day) => {
            return total + (day.shifts?.filter(shift => shift.useDesignationOverride)?.length || 0);
          }, 0)
      }
    };

    return successResponse(res, {
      message: 'Template validation completed',
      data: validationResult
    });
  } catch (error) {
    console.error('❌ Validate template error:', error.message);
    next(error);
  }
};

/**
 * Generate shift instances from template
 * @route POST /api/v1/shift-templates/:id/generate-instances
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const generateInstances = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { startDate, endDate } = req.body;

    console.log(`🚀 Generating shift instances from template ${id} for ${startDate} to ${endDate}`);

    const instances = await shiftTemplateService.generateShiftInstancesFromTemplate(
      id,
      startDate,
      endDate,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Shift instances generated successfully from template',
      data: {
        templateId: parseInt(id),
        dateRange: { startDate, endDate },
        totalInstances: instances.length,
        instances: instances
      }
    });
  } catch (error) {
    console.error('❌ Generate instances error:', error.message);
    next(error);
  }
};

module.exports = {
  createTemplate,
  getAllTemplates,
  getTemplateById,
  updateTemplate,
  deleteTemplate,
  cloneTemplate,
  setAsDefault,
  getAvailableShifts,
  getUsageStats,
  validateTemplate,
  generateInstances
};
