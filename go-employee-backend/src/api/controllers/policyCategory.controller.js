'use strict';

const { PolicyCategory, Policy } = require('../../data/models');
const { Op } = require('sequelize');
const { ApiError, ValidationError } = require('../../common/errors');
const { getPaginationParams, getSortingParams } = require('../../common/utils/pagination');
const { successResponse, createPagination } = require('../../common/utils/response');

// Simple pagination helper functions
const getPagination = (page, size) => {
  const limit = size ? +size : 10;
  const offset = page ? page * limit : 0;
  return { limit, offset };
};


/**
 * Get all policy categories
 */
const getAllPolicyCategories = async (req, res, next) => {
  try {
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'createdAt', 'desc');
    
    const condition = { companyId: req?.tenantContext?.companyId };
    if (req?.query?.search) {
      condition[Op.or] = [
        {
          name: {
            [Op.iLike]: `%${req?.query?.search}%`
          }
        },
        {
          description: {
            [Op.iLike]: `%${req?.query?.search}%`
          }
        }
      ]
    }
    if (req?.query?.isActive) {
      condition[Op.or] = [
        {
          isActive: req?.query?.isActive
        }
      ]
    }
    const order = [[sorting.sortBy, sorting.sortOrder.toUpperCase()]];
    const categories = await PolicyCategory.findAndCountAll({
      where: condition,
      limit: pagination.limit,
      offset: pagination.offset,
      order
    });
    
    return successResponse(res, {
      message: 'Policies retrieved successfully',
      data: categories.rows,
      pagination: createPagination(pagination.page, pagination.limit, categories.count)
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new policy category
 */
const createPolicyCategory = async (req, res, next) => {
  try {
    const categoryData = req.body;
    categoryData.companyId = req?.tenantContext?.companyId;
    categoryData.createdById = req?.tenantContext?.employeeId;
    const category = await PolicyCategory.create(categoryData);
    res.status(201).json({
      success: true,
      status_code: 201,
      message: 'Policy category created successfully',
      result: category,
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get policy category by ID
 */
const getPolicyCategoryById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const category = await PolicyCategory.findOne(
      {
        where: {
          id: id,
          companyId: req?.tenantContext?.companyId
        }
      }
    );
    if (!category) {
      throw new ApiError(404, 'Policy category not found');
    }
    
    res.status(200).json({
      success: true,
      status_code: 200,
      message: 'Policy category retrieved successfully',
      result: category,
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update policy category
 */
const updatePolicyCategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    const categoryData = req.body;
    
    const category = await PolicyCategory.findOne({
      where: {
        id,
        companyId: req?.tenantContext?.companyId
      }
    });
    
    if (!category) {
      throw new ApiError(404, 'Policy category not found');
    }
    
    categoryData.updatedById = req?.tenantContext?.employeeId;
    await category.update(categoryData);
    
    res.status(200).json({
      success: true,
      status_code: 200,
      message: 'Policy category updated successfully',
      result: category,
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete policy category
 */
const deletePolicyCategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const category = await PolicyCategory.findOne({
      where: {
        id,
        companyId: req?.tenantContext?.companyId
      }
    });
    
    if (!category) {
      throw new ApiError(404, 'Policy category not found');
    }
    
    // Check if there are policies using this category
    const policiesCount = await Policy.count({
      where: { policy_category_id: id }
    });
    
    if (policiesCount > 0) {
      throw new ApiError(400, `Cannot delete category. It is associated with ${policiesCount} policies.`);
    }
    
    await category.destroy();
    
    res.status(200).json({
      success: true,
      status_code: 200,
      message: 'Policy category deleted successfully',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get policies by category
 */
const getPoliciesByCategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { page, size, status } = req.query;
    const { limit, offset } = getPagination(page, size);
    
    const category = await PolicyCategory.findByPk(id);
    
    if (!category) {
      throw new ApiError(404, 'Policy category not found');
    }
    
    const condition = { category_id: id };
    if (status) condition.status = status;
    
    const policies = await Policy.findAndCountAll({
      where: condition,
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });
    
    const response = getPagingData(policies, page, limit);
    
    res.status(200).json({
      success: true,
      status_code: 200,
      message: 'Policies by category retrieved successfully',
      result: response,
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllPolicyCategories,
  createPolicyCategory,
  getPolicyCategoryById,
  updatePolicyCategory,
  deletePolicyCategory,
  getPoliciesByCategory
};
