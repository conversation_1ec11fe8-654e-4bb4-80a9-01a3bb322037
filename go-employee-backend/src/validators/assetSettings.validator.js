'use strict';

const Joi = require('joi');

const updateAssetSettings = Joi.object({
  // Asset Numbering Configuration
  assetTagFormat: Joi.string().max(100).optional(),
  assetTagPrefix: Joi.string().max(10).optional(),
  assetTagStartSequence: Joi.number().integer().min(1).optional(),
  autoGenerateAssetTag: Joi.boolean().optional(),
  assetTagLength: Joi.number().integer().min(5).max(50).optional(),
  includeBusinessUnitInTag: Joi.boolean().optional(),
  includeCategoryInTag: Joi.boolean().optional(),
  
  // Depreciation Settings
  defaultDepreciationMethod: Joi.string().valid(
    'straight_line', 
    'declining_balance', 
    'sum_of_years_digits', 
    'units_of_production', 
    'none'
  ).optional(),
  defaultDepreciationRate: Joi.number().min(0).max(100).optional(),
  defaultUsefulLifeYears: Joi.number().integer().min(1).max(100).optional(),
  depreciationCalculationFrequency: Joi.string().valid('monthly', 'quarterly', 'annually').optional(),
  autoUpdateDepreciation: Joi.boolean().optional(),
  depreciationStartDate: Joi.string().valid('purchase_date', 'first_use_date').optional(),
  enableTaxDepreciation: Joi.boolean().optional(),
  taxDepreciationMethod: Joi.string().valid(
    'straight_line', 
    'declining_balance', 
    'sum_of_years_digits', 
    'units_of_production'
  ).optional(),
  
  // Approval Workflow Settings
  assetRequestApprovalRequired: Joi.boolean().optional(),
  assetRequestApprovalWorkflow: Joi.string().valid('manager_only', 'multi_level', 'auto_approve').optional(),
  assetRequestApprovalThresholds: Joi.object({
    manager: Joi.number().min(0).optional(),
    director: Joi.number().min(0).optional(),
    executive: Joi.number().min(0).optional()
  }).optional(),
  assetTransferApprovalRequired: Joi.boolean().optional(),
  assetDisposalApprovalRequired: Joi.boolean().optional(),
  maintenanceApprovalThreshold: Joi.number().min(0).optional(),
  
  // Asset Category Settings
  allowCustomCategories: Joi.boolean().optional(),
  requireCategoryForAssets: Joi.boolean().optional(),
  enableCategoryHierarchy: Joi.boolean().optional(),
  maxCategoryDepth: Joi.number().integer().min(1).max(10).optional(),
  inheritDepreciationFromCategory: Joi.boolean().optional(),
  
  // Maintenance Settings
  enablePreventiveMaintenance: Joi.boolean().optional(),
  defaultMaintenanceFrequency: Joi.string().valid('monthly', 'quarterly', 'semi_annually', 'annually').optional(),
  maintenanceReminderDays: Joi.number().integer().min(1).max(365).optional(),
  enableMaintenanceCalendar: Joi.boolean().optional(),
  allowMaintenanceOverride: Joi.boolean().optional(),
  maintenanceApprovalRequired: Joi.boolean().optional(),
  maintenanceVendorRequired: Joi.boolean().optional(),
  
  // Location Management Settings
  enableLocationTracking: Joi.boolean().optional(),
  requireLocationForAssets: Joi.boolean().optional(),
  enableGeoCoordinates: Joi.boolean().optional(),
  allowLocationHierarchy: Joi.boolean().optional(),
  enableLocationTransferTracking: Joi.boolean().optional(),
  defaultLocation: Joi.string().max(255).optional(),
  
  // Integration Settings
  integrateWithExpenseModule: Joi.boolean().optional(),
  integrateWithProjectModule: Joi.boolean().optional(),
  integrateWithPayrollModule: Joi.boolean().optional(),
  enableAssetExpenseTracking: Joi.boolean().optional(),
  enableAssetCostAllocation: Joi.boolean().optional(),
  enableBudgetIntegration: Joi.boolean().optional(),
  
  // Notification Settings
  enableAssetNotifications: Joi.boolean().optional(),
  notificationSettings: Joi.object({
    assetAssignment: Joi.boolean().optional(),
    assetReturn: Joi.boolean().optional(),
    maintenanceDue: Joi.boolean().optional(),
    warrantyExpiry: Joi.boolean().optional(),
    assetRequest: Joi.boolean().optional(),
    assetApproval: Joi.boolean().optional()
  }).optional(),
  
  // Audit and Compliance Settings
  enableAssetAudit: Joi.boolean().optional(),
  auditFrequency: Joi.string().valid('monthly', 'quarterly', 'semi_annually', 'annually').optional(),
  enableComplianceTracking: Joi.boolean().optional(),
  
  // Document and Image Settings
  enableAssetImages: Joi.boolean().optional(),
  maxAssetImages: Joi.number().integer().min(1).max(20).optional(),
  enableAssetDocuments: Joi.boolean().optional(),
  maxAssetDocuments: Joi.number().integer().min(1).max(50).optional(),
  requiredDocumentTypes: Joi.array().items(Joi.string()).optional(),
  
  // Currency and Financial Settings
  defaultCurrency: Joi.string().length(3).optional(),
  enableMultiCurrency: Joi.boolean().optional(),
  enableAssetInsurance: Joi.boolean().optional(),
  insuranceReminderDays: Joi.number().integer().min(1).max(365).optional()
});

const generateAssetTag = Joi.object({
  businessUnitId: Joi.number().integer().optional(),
  categoryId: Joi.number().integer().optional(),
  businessUnitCode: Joi.string().optional(),
  categoryCode: Joi.string().optional()
});

module.exports = {
  updateAssetSettings,
  generateAssetTag
};
