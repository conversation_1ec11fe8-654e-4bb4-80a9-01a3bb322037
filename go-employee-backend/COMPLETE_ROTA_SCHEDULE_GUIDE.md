# Complete Rota Schedule Management Guide

This document provides comprehensive examples for creating, updating, and managing rota schedules with employee assignments.

## **Method 1: RotaShift-Based Schedule Creation**

Creates schedules using existing RotaShift configurations with their predefined designation requirements.

### **API Endpoint**
```
POST /api/v1/rota-schedules
```

### **Request Example**
```json
{
  "name": "TEST 1: RotaShift-Based Schedule",
  "description": "Schedule created using existing RotaShift configuration",
  "startDate": "2025-08-15",
  "endDate": "2025-08-17",
  "type": "rotaShift",
  "businessUnitId": 8,
  "shiftSources": [
    {
      "type": "rotaShift",
      "rotaShiftId": 5,
      "dates": ["2025-08-15", "2025-08-16", "2025-08-17"]
    }
  ]
}
```

### **Key Features**
- Uses existing RotaShift designation requirements
- Automatically inherits shift timing and break configurations
- Suitable for recurring shift patterns
- `rotaShiftId` and `sourceId` both point to the same RotaShift

---

## **Method 2: Template-Based Schedule Creation**

Creates schedules using ShiftTemplate configurations for more flexible, reusable patterns.

### **API Endpoint**
```
POST /api/v1/rota-schedules
```

### **Request Example**
```json
{
  "name": "TEST 2: Template-Based Schedule",
  "description": "Schedule created using ShiftTemplate configuration",
  "startDate": "2025-08-18",
  "endDate": "2025-08-20",
  "type": "template",
  "businessUnitId": 8,
  "shiftSources": [
    {
      "type": "template",
      "templateId": 4,
      "dates": ["2025-08-18", "2025-08-19", "2025-08-20"]
    }
  ]
}
```

### **Key Features**
- Uses ShiftTemplate for flexible scheduling patterns
- Supports weekly/monthly template configurations
- `rotaShiftId` is null, `sourceId` points to template
- Ideal for complex scheduling requirements

---

## **Method 3: Hybrid Schedule Creation**

Combines both RotaShift and Template sources in a single schedule for maximum flexibility.

### **API Endpoint**
```
POST /api/v1/rota-schedules
```

### **Request Example**
```json
{
  "name": "TEST 3: Hybrid Schedule",
  "description": "Schedule combining RotaShift and Template sources",
  "startDate": "2025-08-21",
  "endDate": "2025-08-25",
  "type": "hybrid",
  "businessUnitId": 8,
  "shiftSources": [
    {
      "type": "rotaShift",
      "rotaShiftId": 5,
      "dates": ["2025-08-21", "2025-08-22"],
      "customRequirements": [
        {
          "designationId": 14,
          "requiredCount": 3
        },
        {
          "designationId": 15,
          "requiredCount": 2
        }
      ]
    },
    {
      "type": "template",
      "templateId": 4,
      "dates": ["2025-08-23", "2025-08-24", "2025-08-25"]
    }
  ]
}
```

### **Key Features**
- Combines multiple source types in one schedule
- Supports custom requirement overrides
- Maximum flexibility for complex scheduling needs
- Each source maintains its own configuration

---

## **Employee Assignment Methods**

### **Method A: Employee Assignment During Schedule Creation**

Assign employees while creating the schedule for immediate staffing.

### **API Endpoint**
```
POST /api/v1/rota-schedules
```

### **Request Example**
```json
{
  "name": "TEST: Employee Assignment During Creation",
  "description": "Schedule with employees assigned during creation",
  "startDate": "2025-08-20",
  "endDate": "2025-08-22",
  "type": "hybrid",
  "businessUnitId": 8,
  "shiftSources": [
    {
      "type": "rotaShift",
      "rotaShiftId": 5,
      "dates": ["2025-08-20", "2025-08-21", "2025-08-22"],
      "customRequirements": [
        {
          "designationId": 476,
          "requiredCount": 2
        },
        {
          "designationId": 32,
          "requiredCount": 1
        }
      ]
    }
  ],
  "employeeAssignments": [
    {
      "employeeId": 11,
      "date": "2025-08-20",
      "designationId": 476,
      "rotaShiftId": 5
    },
    {
      "employeeId": 246,
      "date": "2025-08-20",
      "designationId": 32,
      "rotaShiftId": 5
    },
    {
      "employeeId": 211,
      "date": "2025-08-21",
      "designationId": 1196,
      "rotaShiftId": 5
    }
  ]
}
```

### **Key Features**
- Employees assigned during schedule creation
- Immediate staffing without additional API calls
- Validates employee designation compatibility
- Returns assignment statistics in response

---

### **Method B: Employee Assignment After Schedule Creation**

Assign employees to existing schedules for flexible staffing management.

### **API Endpoint**
```
POST /api/v1/rota-schedules/{scheduleId}/assign-employees
```

### **Request Example**
```json
{
  "assignments": [
    {
      "shiftInstanceId": 541,
      "employeeId": 11,
      "designationId": 476,
      "notes": "Additional assignment after schedule creation"
    }
  ]
}
```

### **Key Features**
- Flexible post-creation employee assignment
- Supports bulk assignment operations
- Includes conflict detection and validation
- Allows assignment notes and metadata

---

## **Schedule Update Operations**

Update existing schedules to modify dates, requirements, or other properties.

### **API Endpoint**
```
PUT /api/v1/rota-schedules/{scheduleId}
```

### **Request Example**
```json
{
  "name": "UPDATED: Schedule Name Modified",
  "description": "Updated description after testing functionality",
  "startDate": "2025-08-20",
  "endDate": "2025-08-23"
}
```

### **Response Example**
```json
{
  "id": 161,
  "name": "UPDATED: Schedule Name Modified",
  "description": "Updated description after testing functionality",
  "startDate": "2025-08-20",
  "endDate": "2025-08-23",
  "status": "draft",
  "businessUnit": {
    "id": 8,
    "name": "Business Unit Name",
    "timezone": "Asia/Kolkata"
  },
  "metrics": {
    "totalShiftInstances": 3,
    "totalAssignments": 3,
    "fillRate": "100%"
  },
  "updateSummary": {
    "shiftInstancesModified": 0,
    "lastModified": "2025-07-20T06:41:54.000Z",
    "modifiedBy": 86
  }
}
```

### **Key Features**
- Updates schedule metadata and date ranges
- Recalculates shift instances if dates change
- Returns updated metrics and statistics
- Maintains assignment integrity during updates

---

## **Custom Requirements Override**

All methods support custom requirement overrides to modify designation requirements per shift source.

### **Custom Requirements Format**
```json
"customRequirements": [
  {
    "designationId": 14,
    "requiredCount": 3
  },
  {
    "designationId": 15,
    "requiredCount": 2
  }
]
```

### **How It Works**
1. **Base Requirements**: Inherited from RotaShift or Template
2. **Custom Override**: Applied on top of base requirements
3. **Final Requirements**: Combination used for shift instance creation

---

## **Employee Assignment Validation**

### **Important Requirements**
1. **Designation Match**: Employee's designation must match assignment designation
2. **Active Status**: Only active employees can be assigned
3. **Business Unit**: Employee must belong to the same business unit
4. **Conflict Detection**: System checks for scheduling conflicts

### **Common Issues & Solutions**
- **"Employee not found or designation mismatch"**: Ensure employee has correct designation ID
- **Validation errors**: Check employee status and business unit alignment
- **Assignment failures**: Verify shift instance exists and is in correct status

---

## **Response Structure**

All methods return a consistent response structure:

```json
{
  "id": 161,
  "name": "Schedule Name",
  "description": "Schedule Description",
  "startDate": "2025-08-20",
  "endDate": "2025-08-22",
  "status": "draft",
  "type": "hybrid",
  "businessUnitId": 8,
  "statistics": {
    "totalShiftInstances": 3,
    "totalAssignments": 3,
    "averageStaffingLevel": "100%",
    "dateRange": {
      "2025-08-20": { "instances": 1, "assignments": 2 },
      "2025-08-21": { "instances": 1, "assignments": 1 },
      "2025-08-22": { "instances": 1, "assignments": 0 }
    }
  },
  "createdAt": "2025-07-20T06:25:54.000Z",
  "updatedAt": "2025-07-20T06:25:54.000Z"
}
```

---

## **Architecture Notes**

### **Database Design**
- **RotaShift-based**: `rotaShiftId` and `sourceId` both reference RotaShift
- **Template-based**: `rotaShiftId` is null, `sourceId` references ShiftTemplate
- **Hybrid**: Mixed approach supporting both patterns

### **Employee Assignment Architecture**
- **During Creation**: Processed in same transaction as schedule creation
- **After Creation**: Separate endpoint for flexible assignment management
- **Validation**: Multi-layer validation for data integrity
- **Statistics**: Real-time calculation of assignment metrics

---

## **Best Practices**

1. **Use RotaShift** for simple, recurring patterns
2. **Use Templates** for complex, reusable configurations
3. **Use Hybrid** when you need both approaches
4. **Employee Assignment During Creation** for immediate staffing needs
5. **Employee Assignment After Creation** for flexible management
6. **Custom Requirements** for one-off adjustments
7. **Test thoroughly** with correct employee designation IDs
8. **Update schedules** carefully to maintain assignment integrity

---

## **Testing Results Summary**

### **✅ Tested & Working**
- ✅ **Schedule Creation**: All three methods (RotaShift, Template, Hybrid)
- ✅ **Employee Assignment During Creation**: Fully functional with validation
- ✅ **Employee Assignment After Creation**: Working with proper endpoints
- ✅ **Schedule Updates**: Fixed circular reference issue, now working
- ✅ **Custom Requirements**: Override functionality working correctly
- ✅ **Validation**: Employee designation matching implemented
- ✅ **Statistics**: Real-time calculation of metrics and fill rates

### **🔧 Key Fixes Applied**
- Fixed circular reference in schedule update response
- Corrected employee assignment validation logic
- Implemented proper designation ID matching
- Added comprehensive error handling and validation

---

## **Testing Credentials**

For API testing, use these credentials:
- **Email**: `<EMAIL>`
- **Password**: `Admin@123`

### **Test Data References**
- **RotaShift ID**: 5 (for RotaShift-based examples)
- **Template ID**: 4 (for Template-based examples)
- **Business Unit ID**: 8
- **Employee IDs**: 11, 246, 211 (with designations 476, 32, 1196 respectively)
